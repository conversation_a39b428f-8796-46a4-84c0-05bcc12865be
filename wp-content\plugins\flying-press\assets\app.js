var Y2=Je=>{throw TypeError(Je)};var Am=(Je,gt,vt)=>gt.has(Je)||Y2("Cannot "+vt);var j=(Je,gt,vt)=>(Am(Je,gt,"read from private field"),vt?vt.call(Je):gt.get(Je)),pe=(Je,gt,vt)=>gt.has(Je)?Y2("Cannot add the same private member more than once"):gt instanceof WeakSet?gt.add(Je):gt.set(Je,vt),se=(Je,gt,vt,Ca)=>(Am(Je,gt,"write to private field"),Ca?Ca.call(Je,vt):gt.set(Je,vt),vt),Te=(Je,gt,vt)=>(Am(Je,gt,"access private method"),vt);var cc=(Je,gt,vt,Ca)=>({set _(Er){se(Je,gt,Er,vt)},get _(){return j(Je,gt,Ca)}});(function(){"use strict";var Ni,La,Ds,t1,_s,Ua,Ms,n1,Ri,a1,Ns,Rs,dn,Oi,Vt,tl,Vi,_n,Ta,i1,qn,s1,Fn,Ht,zi,Gn,yi,r1,ia,Mn,nl,l1,at,Ba,Ha,Os,Vs,Pa,zs,ks,o1,Qt,Ve,al,Pt,ki,Ls,qa,Yn,il,Us,Bs,Li,Ui,Fa,Hs,Pe,no,Dm,_m,Mm,Nm,Rm,Om,Vm,Q2,u1,Ga,Ya,Xt,sa,ra,fc,zm,c1,f1;var Je=document.createElement("style");Je.textContent=`*,:before,:after{--tw-border-spacing-x: 0;--tw-border-spacing-y: 0;--tw-translate-x: 0;--tw-translate-y: 0;--tw-rotate: 0;--tw-skew-x: 0;--tw-skew-y: 0;--tw-scale-x: 1;--tw-scale-y: 1;--tw-pan-x: ;--tw-pan-y: ;--tw-pinch-zoom: ;--tw-scroll-snap-strictness: proximity;--tw-gradient-from-position: ;--tw-gradient-via-position: ;--tw-gradient-to-position: ;--tw-ordinal: ;--tw-slashed-zero: ;--tw-numeric-figure: ;--tw-numeric-spacing: ;--tw-numeric-fraction: ;--tw-ring-inset: ;--tw-ring-offset-width: 0px;--tw-ring-offset-color: #fff;--tw-ring-color: rgb(59 130 246 / .5);--tw-ring-offset-shadow: 0 0 #0000;--tw-ring-shadow: 0 0 #0000;--tw-shadow: 0 0 #0000;--tw-shadow-colored: 0 0 #0000;--tw-blur: ;--tw-brightness: ;--tw-contrast: ;--tw-grayscale: ;--tw-hue-rotate: ;--tw-invert: ;--tw-saturate: ;--tw-sepia: ;--tw-drop-shadow: ;--tw-backdrop-blur: ;--tw-backdrop-brightness: ;--tw-backdrop-contrast: ;--tw-backdrop-grayscale: ;--tw-backdrop-hue-rotate: ;--tw-backdrop-invert: ;--tw-backdrop-opacity: ;--tw-backdrop-saturate: ;--tw-backdrop-sepia: ;--tw-contain-size: ;--tw-contain-layout: ;--tw-contain-paint: ;--tw-contain-style: }::backdrop{--tw-border-spacing-x: 0;--tw-border-spacing-y: 0;--tw-translate-x: 0;--tw-translate-y: 0;--tw-rotate: 0;--tw-skew-x: 0;--tw-skew-y: 0;--tw-scale-x: 1;--tw-scale-y: 1;--tw-pan-x: ;--tw-pan-y: ;--tw-pinch-zoom: ;--tw-scroll-snap-strictness: proximity;--tw-gradient-from-position: ;--tw-gradient-via-position: ;--tw-gradient-to-position: ;--tw-ordinal: ;--tw-slashed-zero: ;--tw-numeric-figure: ;--tw-numeric-spacing: ;--tw-numeric-fraction: ;--tw-ring-inset: ;--tw-ring-offset-width: 0px;--tw-ring-offset-color: #fff;--tw-ring-color: rgb(59 130 246 / .5);--tw-ring-offset-shadow: 0 0 #0000;--tw-ring-shadow: 0 0 #0000;--tw-shadow: 0 0 #0000;--tw-shadow-colored: 0 0 #0000;--tw-blur: ;--tw-brightness: ;--tw-contrast: ;--tw-grayscale: ;--tw-hue-rotate: ;--tw-invert: ;--tw-saturate: ;--tw-sepia: ;--tw-drop-shadow: ;--tw-backdrop-blur: ;--tw-backdrop-brightness: ;--tw-backdrop-contrast: ;--tw-backdrop-grayscale: ;--tw-backdrop-hue-rotate: ;--tw-backdrop-invert: ;--tw-backdrop-opacity: ;--tw-backdrop-saturate: ;--tw-backdrop-sepia: ;--tw-contain-size: ;--tw-contain-layout: ;--tw-contain-paint: ;--tw-contain-style: }*,:before,:after{box-sizing:border-box;border-width:0;border-style:solid;border-color:#e5e7eb}:before,:after{--tw-content: ""}html,:host{line-height:1.5;-webkit-text-size-adjust:100%;-moz-tab-size:4;-o-tab-size:4;tab-size:4;font-family:ui-sans-serif,system-ui,sans-serif,"Apple Color Emoji","Segoe UI Emoji",Segoe UI Symbol,"Noto Color Emoji";font-feature-settings:normal;font-variation-settings:normal;-webkit-tap-highlight-color:transparent}body{margin:0;line-height:inherit}hr{height:0;color:inherit;border-top-width:1px}abbr:where([title]){-webkit-text-decoration:underline dotted;text-decoration:underline dotted}h1,h2,h3,h4,h5,h6{font-size:inherit;font-weight:inherit}a{color:inherit;text-decoration:inherit}b,strong{font-weight:bolder}code,kbd,samp,pre{font-family:ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,Liberation Mono,Courier New,monospace;font-feature-settings:normal;font-variation-settings:normal;font-size:1em}small{font-size:80%}sub,sup{font-size:75%;line-height:0;position:relative;vertical-align:baseline}sub{bottom:-.25em}sup{top:-.5em}table{text-indent:0;border-color:inherit;border-collapse:collapse}button,input,optgroup,select,textarea{font-family:inherit;font-feature-settings:inherit;font-variation-settings:inherit;font-size:100%;font-weight:inherit;line-height:inherit;letter-spacing:inherit;color:inherit;margin:0;padding:0}button,select{text-transform:none}button,input:where([type=button]),input:where([type=reset]),input:where([type=submit]){-webkit-appearance:button;background-color:transparent;background-image:none}:-moz-focusring{outline:auto}:-moz-ui-invalid{box-shadow:none}progress{vertical-align:baseline}::-webkit-inner-spin-button,::-webkit-outer-spin-button{height:auto}[type=search]{-webkit-appearance:textfield;outline-offset:-2px}::-webkit-search-decoration{-webkit-appearance:none}::-webkit-file-upload-button{-webkit-appearance:button;font:inherit}summary{display:list-item}blockquote,dl,dd,h1,h2,h3,h4,h5,h6,hr,figure,p,pre{margin:0}fieldset{margin:0;padding:0}legend{padding:0}ol,ul,menu{list-style:none;margin:0;padding:0}dialog{padding:0}textarea{resize:vertical}input::-moz-placeholder,textarea::-moz-placeholder{opacity:1;color:#9ca3af}input::placeholder,textarea::placeholder{opacity:1;color:#9ca3af}button,[role=button]{cursor:pointer}:disabled{cursor:default}img,svg,video,canvas,audio,iframe,embed,object{display:block;vertical-align:middle}img,video{max-width:100%;height:auto}[hidden]:where(:not([hidden=until-found])){display:none}.sr-only{position:absolute;width:1px;height:1px;padding:0;margin:-1px;overflow:hidden;clip:rect(0,0,0,0);white-space:nowrap;border-width:0}.static{position:static}.fixed{position:fixed}.absolute{position:absolute}.relative{position:relative}.inset-0{top:0;right:0;bottom:0;left:0}.bottom-0{bottom:0}.left-0{left:0}.mx-auto{margin-left:auto;margin-right:auto}.my-4{margin-top:1rem;margin-bottom:1rem}.mb-1{margin-bottom:.25rem}.mb-4{margin-bottom:1rem}.mr-2{margin-right:.5rem}.mt-1{margin-top:.25rem}.mt-6{margin-top:1.5rem}.mt-8{margin-top:2rem}.block{display:block}.inline-block{display:inline-block}.flex{display:flex}.inline-flex{display:inline-flex}.grid{display:grid}.hidden{display:none}.size-4{width:1rem;height:1rem}.size-5{width:1.25rem;height:1.25rem}.size-6{width:1.5rem;height:1.5rem}.h-48{height:12rem}.h-5{height:1.25rem}.h-\\[3px\\]{height:3px}.h-auto{height:auto}.max-h-\\[85vh\\]{max-height:85vh}.w-10{width:2.5rem}.w-44{width:11rem}.w-64{width:16rem}.w-80{width:20rem}.w-\\[500px\\]{width:500px}.w-full{width:100%}.max-w-6xl{max-width:72rem}.max-w-\\[90vw\\]{max-width:90vw}.max-w-xl{max-width:36rem}.flex-1{flex:1 1 0%}.flex-shrink-0,.shrink-0{flex-shrink:0}.grow{flex-grow:1}@keyframes spin{to{transform:rotate(360deg)}}.animate-spin{animation:spin 1s linear infinite}.cursor-pointer{cursor:pointer}.grid-cols-1{grid-template-columns:repeat(1,minmax(0,1fr))}.flex-row{flex-direction:row}.flex-col{flex-direction:column}.flex-wrap{flex-wrap:wrap}.items-start{align-items:flex-start}.items-center{align-items:center}.justify-start{justify-content:flex-start}.justify-end{justify-content:flex-end}.justify-center{justify-content:center}.justify-between{justify-content:space-between}.gap-2{gap:.5rem}.gap-3{gap:.75rem}.gap-4{gap:1rem}.space-x-4>:not([hidden])~:not([hidden]){--tw-space-x-reverse: 0;margin-right:calc(1rem * var(--tw-space-x-reverse));margin-left:calc(1rem * calc(1 - var(--tw-space-x-reverse)))}.space-y-4>:not([hidden])~:not([hidden]){--tw-space-y-reverse: 0;margin-top:calc(1rem * calc(1 - var(--tw-space-y-reverse)));margin-bottom:calc(1rem * var(--tw-space-y-reverse))}.space-y-6>:not([hidden])~:not([hidden]){--tw-space-y-reverse: 0;margin-top:calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));margin-bottom:calc(1.5rem * var(--tw-space-y-reverse))}.divide-y>:not([hidden])~:not([hidden]){--tw-divide-y-reverse: 0;border-top-width:calc(1px * calc(1 - var(--tw-divide-y-reverse)));border-bottom-width:calc(1px * var(--tw-divide-y-reverse))}.divide-gray-200>:not([hidden])~:not([hidden]){--tw-divide-opacity: 1;border-color:rgb(229 231 235 / var(--tw-divide-opacity, 1))}.overflow-x-auto{overflow-x:auto}.overflow-y-auto{overflow-y:auto}.whitespace-nowrap{white-space:nowrap}.rounded-full{border-radius:9999px}.rounded-lg{border-radius:.5rem}.rounded-md{border-radius:.375rem}.rounded-xl{border-radius:.75rem}.border{border-width:1px}.border-b{border-bottom-width:1px}.border-t{border-top-width:1px}.\\!border-none{border-style:none!important}.border-gray-200{--tw-border-opacity: 1;border-color:rgb(229 231 235 / var(--tw-border-opacity, 1))}.bg-black\\/50{background-color:#00000080}.bg-gray-200{--tw-bg-opacity: 1;background-color:rgb(229 231 235 / var(--tw-bg-opacity, 1))}.bg-indigo-50{--tw-bg-opacity: 1;background-color:rgb(238 242 255 / var(--tw-bg-opacity, 1))}.bg-indigo-600{--tw-bg-opacity: 1;background-color:rgb(79 70 229 / var(--tw-bg-opacity, 1))}.bg-red-50{--tw-bg-opacity: 1;background-color:rgb(254 242 242 / var(--tw-bg-opacity, 1))}.bg-red-600{--tw-bg-opacity: 1;background-color:rgb(220 38 38 / var(--tw-bg-opacity, 1))}.bg-white{--tw-bg-opacity: 1;background-color:rgb(255 255 255 / var(--tw-bg-opacity, 1))}.p-2{padding:.5rem}.p-6{padding:1.5rem}.\\!px-3{padding-left:.75rem!important;padding-right:.75rem!important}.\\!py-1{padding-top:.25rem!important;padding-bottom:.25rem!important}.px-2\\.5{padding-left:.625rem;padding-right:.625rem}.px-3{padding-left:.75rem;padding-right:.75rem}.px-4{padding-left:1rem;padding-right:1rem}.px-6{padding-left:1.5rem;padding-right:1.5rem}.py-1\\.5{padding-top:.375rem;padding-bottom:.375rem}.py-2{padding-top:.5rem;padding-bottom:.5rem}.py-3{padding-top:.75rem;padding-bottom:.75rem}.py-4{padding-top:1rem;padding-bottom:1rem}.py-8{padding-top:2rem;padding-bottom:2rem}.font-mono{font-family:ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,Liberation Mono,Courier New,monospace}.text-2xl{font-size:1.5rem;line-height:2rem}.text-base{font-size:1rem;line-height:1.5rem}.text-lg{font-size:1.125rem;line-height:1.75rem}.text-sm{font-size:.875rem;line-height:1.25rem}.text-xs{font-size:.75rem;line-height:1rem}.font-medium{font-weight:500}.font-normal{font-weight:400}.text-gray-400{--tw-text-opacity: 1;color:rgb(156 163 175 / var(--tw-text-opacity, 1))}.text-gray-500{--tw-text-opacity: 1;color:rgb(107 114 128 / var(--tw-text-opacity, 1))}.text-gray-600{--tw-text-opacity: 1;color:rgb(75 85 99 / var(--tw-text-opacity, 1))}.text-gray-700{--tw-text-opacity: 1;color:rgb(55 65 81 / var(--tw-text-opacity, 1))}.text-gray-800{--tw-text-opacity: 1;color:rgb(31 41 55 / var(--tw-text-opacity, 1))}.text-gray-900{--tw-text-opacity: 1;color:rgb(17 24 39 / var(--tw-text-opacity, 1))}.text-green-500{--tw-text-opacity: 1;color:rgb(34 197 94 / var(--tw-text-opacity, 1))}.text-indigo-600{--tw-text-opacity: 1;color:rgb(79 70 229 / var(--tw-text-opacity, 1))}.text-orange-400{--tw-text-opacity: 1;color:rgb(251 146 60 / var(--tw-text-opacity, 1))}.text-red-600{--tw-text-opacity: 1;color:rgb(220 38 38 / var(--tw-text-opacity, 1))}.text-white{--tw-text-opacity: 1;color:rgb(255 255 255 / var(--tw-text-opacity, 1))}.underline{text-decoration-line:underline}.\\!shadow-none{--tw-shadow: 0 0 #0000 !important;--tw-shadow-colored: 0 0 #0000 !important;box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)!important}.shadow-lg{--tw-shadow: 0 10px 15px -3px rgb(0 0 0 / .1), 0 4px 6px -4px rgb(0 0 0 / .1);--tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.outline{outline-style:solid}.outline-1{outline-width:1px}.-outline-offset-1{outline-offset:-1px}.outline-gray-300{outline-color:#d1d5db}.ring-1{--tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow:var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow, 0 0 #0000)}.ring-2{--tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow:var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow, 0 0 #0000)}.ring-inset{--tw-ring-inset: inset}.ring-gray-300{--tw-ring-opacity: 1;--tw-ring-color: rgb(209 213 219 / var(--tw-ring-opacity, 1))}.ring-indigo-600{--tw-ring-opacity: 1;--tw-ring-color: rgb(79 70 229 / var(--tw-ring-opacity, 1))}.filter{filter:var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow)}.transition{transition-property:color,background-color,border-color,text-decoration-color,fill,stroke,opacity,box-shadow,transform,filter,-webkit-backdrop-filter;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke,opacity,box-shadow,transform,filter,backdrop-filter;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke,opacity,box-shadow,transform,filter,backdrop-filter,-webkit-backdrop-filter;transition-timing-function:cubic-bezier(.4,0,.2,1);transition-duration:.15s}.transition-all{transition-property:all;transition-timing-function:cubic-bezier(.4,0,.2,1);transition-duration:.15s}.transition-colors{transition-property:color,background-color,border-color,text-decoration-color,fill,stroke;transition-timing-function:cubic-bezier(.4,0,.2,1);transition-duration:.15s}.duration-200{transition-duration:.2s}.ease-in-out{transition-timing-function:cubic-bezier(.4,0,.2,1)}#wpcontent{padding-left:0!important}body{background-color:#f9fafb!important}.placeholder\\:text-gray-400::-moz-placeholder{--tw-text-opacity: 1;color:rgb(156 163 175 / var(--tw-text-opacity, 1))}.placeholder\\:text-gray-400::placeholder{--tw-text-opacity: 1;color:rgb(156 163 175 / var(--tw-text-opacity, 1))}.after\\:absolute:after{content:var(--tw-content);position:absolute}.after\\:left-\\[2px\\]:after{content:var(--tw-content);left:2px}.after\\:top-0\\.5:after{content:var(--tw-content);top:.125rem}.after\\:h-4:after{content:var(--tw-content);height:1rem}.after\\:w-4:after{content:var(--tw-content);width:1rem}.after\\:rounded-full:after{content:var(--tw-content);border-radius:9999px}.after\\:bg-white:after{content:var(--tw-content);--tw-bg-opacity: 1;background-color:rgb(255 255 255 / var(--tw-bg-opacity, 1))}.after\\:transition-all:after{content:var(--tw-content);transition-property:all;transition-timing-function:cubic-bezier(.4,0,.2,1);transition-duration:.15s}.after\\:content-\\[\\'\\'\\]:after{--tw-content: "";content:var(--tw-content)}.hover\\:cursor-pointer:hover{cursor:pointer}.hover\\:bg-indigo-700:hover{--tw-bg-opacity: 1;background-color:rgb(67 56 202 / var(--tw-bg-opacity, 1))}.hover\\:bg-red-700:hover{--tw-bg-opacity: 1;background-color:rgb(185 28 28 / var(--tw-bg-opacity, 1))}.hover\\:text-gray-500:hover{--tw-text-opacity: 1;color:rgb(107 114 128 / var(--tw-text-opacity, 1))}.hover\\:text-indigo-600:hover{--tw-text-opacity: 1;color:rgb(79 70 229 / var(--tw-text-opacity, 1))}.hover\\:text-indigo-700:hover{--tw-text-opacity: 1;color:rgb(67 56 202 / var(--tw-text-opacity, 1))}.focus\\:\\!border-none:focus{border-style:none!important}.focus\\:outline-none:focus{outline:2px solid transparent;outline-offset:2px}.focus\\:\\!outline:focus{outline-style:solid!important}.focus\\:outline:focus{outline-style:solid}.focus\\:\\!outline-2:focus{outline-width:2px!important}.focus\\:outline-2:focus{outline-width:2px}.focus\\:\\!-outline-offset-2:focus{outline-offset:-2px!important}.focus\\:-outline-offset-2:focus{outline-offset:-2px}.focus\\:\\!outline-indigo-600:focus{outline-color:#4f46e5!important}.focus\\:outline-indigo-600:focus{outline-color:#4f46e5}.peer:checked~.peer-checked\\:bg-indigo-600{--tw-bg-opacity: 1;background-color:rgb(79 70 229 / var(--tw-bg-opacity, 1))}.peer:checked~.peer-checked\\:text-white{--tw-text-opacity: 1;color:rgb(255 255 255 / var(--tw-text-opacity, 1))}.peer:checked~.peer-checked\\:ring-2{--tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow:var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow, 0 0 #0000)}.peer:checked~.peer-checked\\:ring-inset{--tw-ring-inset: inset}.peer:checked~.peer-checked\\:ring-indigo-600{--tw-ring-opacity: 1;--tw-ring-color: rgb(79 70 229 / var(--tw-ring-opacity, 1))}.peer:checked~.peer-checked\\:after\\:translate-x-5:after{content:var(--tw-content);--tw-translate-x: 1.25rem;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}@media (min-width: 640px){.sm\\:text-sm\\/6{font-size:.875rem;line-height:1.5rem}}@media (min-width: 768px){.md\\:mb-0{margin-bottom:0}.md\\:w-64{width:16rem}.md\\:w-auto{width:auto}.md\\:grid-cols-2{grid-template-columns:repeat(2,minmax(0,1fr))}.md\\:grid-cols-3{grid-template-columns:repeat(3,minmax(0,1fr))}.md\\:flex-row{flex-direction:row}.md\\:items-center{align-items:center}}@media (min-width: 1024px){.lg\\:flex-row{flex-direction:row}.lg\\:flex-col{flex-direction:column}.lg\\:items-center{align-items:center}}
/*$vite$:1*/`,document.head.appendChild(Je);function gt(t,a){for(var s=0;s<a.length;s++){const r=a[s];if(typeof r!="string"&&!Array.isArray(r)){for(const o in r)if(o!=="default"&&!(o in t)){const c=Object.getOwnPropertyDescriptor(r,o);c&&Object.defineProperty(t,o,c.get?c:{enumerable:!0,get:()=>r[o]})}}}return Object.freeze(Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}))}var vt=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function Ca(t){return t&&t.__esModule&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t}var Er={exports:{}},Tr={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var km;function X2(){if(km)return Tr;km=1;var t=Symbol.for("react.transitional.element"),a=Symbol.for("react.fragment");function s(r,o,c){var d=null;if(c!==void 0&&(d=""+c),o.key!==void 0&&(d=""+o.key),"key"in o){c={};for(var h in o)h!=="key"&&(c[h]=o[h])}else c=o;return o=c.ref,{$$typeof:t,type:r,key:d,ref:o!==void 0?o:null,props:c}}return Tr.Fragment=a,Tr.jsx=s,Tr.jsxs=s,Tr}var Lm;function K2(){return Lm||(Lm=1,Er.exports=X2()),Er.exports}var m=K2(),cs=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(t){return this.listeners.add(t),this.onSubscribe(),()=>{this.listeners.delete(t),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}},gi=typeof window>"u"||"Deno"in globalThis;function ln(){}function Z2(t,a){return typeof t=="function"?t(a):t}function dc(t){return typeof t=="number"&&t>=0&&t!==1/0}function Um(t,a){return Math.max(t+(a||0)-Date.now(),0)}function fs(t,a){return typeof t=="function"?t(a):t}function Cn(t,a){return typeof t=="function"?t(a):t}function Bm(t,a){const{type:s="all",exact:r,fetchStatus:o,predicate:c,queryKey:d,stale:h}=t;if(d){if(r){if(a.queryHash!==hc(d,a.options))return!1}else if(!Cr(a.queryKey,d))return!1}if(s!=="all"){const p=a.isActive();if(s==="active"&&!p||s==="inactive"&&p)return!1}return!(typeof h=="boolean"&&a.isStale()!==h||o&&o!==a.state.fetchStatus||c&&!c(a))}function Hm(t,a){const{exact:s,status:r,predicate:o,mutationKey:c}=t;if(c){if(!a.options.mutationKey)return!1;if(s){if(vi(a.options.mutationKey)!==vi(c))return!1}else if(!Cr(a.options.mutationKey,c))return!1}return!(r&&a.state.status!==r||o&&!o(a))}function hc(t,a){return((a==null?void 0:a.queryKeyHashFn)||vi)(t)}function vi(t){return JSON.stringify(t,(a,s)=>mc(s)?Object.keys(s).sort().reduce((r,o)=>(r[o]=s[o],r),{}):s)}function Cr(t,a){return t===a?!0:typeof t!=typeof a?!1:t&&a&&typeof t=="object"&&typeof a=="object"?!Object.keys(a).some(s=>!Cr(t[s],a[s])):!1}function Pm(t,a){if(t===a)return t;const s=qm(t)&&qm(a);if(s||mc(t)&&mc(a)){const r=s?t:Object.keys(t),o=r.length,c=s?a:Object.keys(a),d=c.length,h=s?[]:{};let p=0;for(let y=0;y<d;y++){const v=s?y:c[y];(!s&&r.includes(v)||s)&&t[v]===void 0&&a[v]===void 0?(h[v]=void 0,p++):(h[v]=Pm(t[v],a[v]),h[v]===t[v]&&t[v]!==void 0&&p++)}return o===d&&p===o?t:h}return a}function ao(t,a){if(!a||Object.keys(t).length!==Object.keys(a).length)return!1;for(const s in t)if(t[s]!==a[s])return!1;return!0}function qm(t){return Array.isArray(t)&&t.length===Object.keys(t).length}function mc(t){if(!Fm(t))return!1;const a=t.constructor;if(a===void 0)return!0;const s=a.prototype;return!(!Fm(s)||!s.hasOwnProperty("isPrototypeOf")||Object.getPrototypeOf(t)!==Object.prototype)}function Fm(t){return Object.prototype.toString.call(t)==="[object Object]"}function W2(t){return new Promise(a=>{setTimeout(a,t)})}function pc(t,a,s){return typeof s.structuralSharing=="function"?s.structuralSharing(t,a):s.structuralSharing!==!1?Pm(t,a):a}function $2(t,a,s=0){const r=[...t,a];return s&&r.length>s?r.slice(1):r}function J2(t,a,s=0){const r=[a,...t];return s&&r.length>s?r.slice(0,-1):r}var yc=Symbol();function Gm(t,a){return!t.queryFn&&(a!=null&&a.initialPromise)?()=>a.initialPromise:!t.queryFn||t.queryFn===yc?()=>Promise.reject(new Error(`Missing queryFn: '${t.queryHash}'`)):t.queryFn}var I2=(t1=class extends cs{constructor(){super();pe(this,Ni);pe(this,La);pe(this,Ds);se(this,Ds,a=>{if(!gi&&window.addEventListener){const s=()=>a();return window.addEventListener("visibilitychange",s,!1),()=>{window.removeEventListener("visibilitychange",s)}}})}onSubscribe(){j(this,La)||this.setEventListener(j(this,Ds))}onUnsubscribe(){var a;this.hasListeners()||((a=j(this,La))==null||a.call(this),se(this,La,void 0))}setEventListener(a){var s;se(this,Ds,a),(s=j(this,La))==null||s.call(this),se(this,La,a(r=>{typeof r=="boolean"?this.setFocused(r):this.onFocus()}))}setFocused(a){j(this,Ni)!==a&&(se(this,Ni,a),this.onFocus())}onFocus(){const a=this.isFocused();this.listeners.forEach(s=>{s(a)})}isFocused(){var a;return typeof j(this,Ni)=="boolean"?j(this,Ni):((a=globalThis.document)==null?void 0:a.visibilityState)!=="hidden"}},Ni=new WeakMap,La=new WeakMap,Ds=new WeakMap,t1),gc=new I2,eS=(n1=class extends cs{constructor(){super();pe(this,_s,!0);pe(this,Ua);pe(this,Ms);se(this,Ms,a=>{if(!gi&&window.addEventListener){const s=()=>a(!0),r=()=>a(!1);return window.addEventListener("online",s,!1),window.addEventListener("offline",r,!1),()=>{window.removeEventListener("online",s),window.removeEventListener("offline",r)}}})}onSubscribe(){j(this,Ua)||this.setEventListener(j(this,Ms))}onUnsubscribe(){var a;this.hasListeners()||((a=j(this,Ua))==null||a.call(this),se(this,Ua,void 0))}setEventListener(a){var s;se(this,Ms,a),(s=j(this,Ua))==null||s.call(this),se(this,Ua,a(this.setOnline.bind(this)))}setOnline(a){j(this,_s)!==a&&(se(this,_s,a),this.listeners.forEach(r=>{r(a)}))}isOnline(){return j(this,_s)}},_s=new WeakMap,Ua=new WeakMap,Ms=new WeakMap,n1),io=new eS;function vc(){let t,a;const s=new Promise((o,c)=>{t=o,a=c});s.status="pending",s.catch(()=>{});function r(o){Object.assign(s,o),delete s.resolve,delete s.reject}return s.resolve=o=>{r({status:"fulfilled",value:o}),t(o)},s.reject=o=>{r({status:"rejected",reason:o}),a(o)},s}function tS(t){return Math.min(1e3*2**t,3e4)}function Ym(t){return(t??"online")==="online"?io.isOnline():!0}var Qm=class extends Error{constructor(t){super("CancelledError"),this.revert=t==null?void 0:t.revert,this.silent=t==null?void 0:t.silent}};function bc(t){return t instanceof Qm}function Xm(t){let a=!1,s=0,r=!1,o;const c=vc(),d=C=>{var R;r||(S(new Qm(C)),(R=t.abort)==null||R.call(t))},h=()=>{a=!0},p=()=>{a=!1},y=()=>gc.isFocused()&&(t.networkMode==="always"||io.isOnline())&&t.canRun(),v=()=>Ym(t.networkMode)&&t.canRun(),b=C=>{var R;r||(r=!0,(R=t.onSuccess)==null||R.call(t,C),o==null||o(),c.resolve(C))},S=C=>{var R;r||(r=!0,(R=t.onError)==null||R.call(t,C),o==null||o(),c.reject(C))},E=()=>new Promise(C=>{var R;o=z=>{(r||y())&&C(z)},(R=t.onPause)==null||R.call(t)}).then(()=>{var C;o=void 0,r||(C=t.onContinue)==null||C.call(t)}),N=()=>{if(r)return;let C;const R=s===0?t.initialPromise:void 0;try{C=R??t.fn()}catch(z){C=Promise.reject(z)}Promise.resolve(C).then(b).catch(z=>{var X;if(r)return;const B=t.retry??(gi?0:3),H=t.retryDelay??tS,W=typeof H=="function"?H(s,z):H,O=B===!0||typeof B=="number"&&s<B||typeof B=="function"&&B(s,z);if(a||!O){S(z);return}s++,(X=t.onFail)==null||X.call(t,s,z),W2(W).then(()=>y()?void 0:E()).then(()=>{a?S(z):N()})})};return{promise:c,cancel:d,continue:()=>(o==null||o(),c),cancelRetry:h,continueRetry:p,canStart:v,start:()=>(v()?N():E().then(N),c)}}function nS(){let t=[],a=0,s=h=>{h()},r=h=>{h()},o=h=>setTimeout(h,0);const c=h=>{a?t.push(h):o(()=>{s(h)})},d=()=>{const h=t;t=[],h.length&&o(()=>{r(()=>{h.forEach(p=>{s(p)})})})};return{batch:h=>{let p;a++;try{p=h()}finally{a--,a||d()}return p},batchCalls:h=>(...p)=>{c(()=>{h(...p)})},schedule:c,setNotifyFunction:h=>{s=h},setBatchNotifyFunction:h=>{r=h},setScheduler:h=>{o=h}}}var bt=nS(),Km=(a1=class{constructor(){pe(this,Ri)}destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),dc(this.gcTime)&&se(this,Ri,setTimeout(()=>{this.optionalRemove()},this.gcTime))}updateGcTime(t){this.gcTime=Math.max(this.gcTime||0,t??(gi?1/0:5*60*1e3))}clearGcTimeout(){j(this,Ri)&&(clearTimeout(j(this,Ri)),se(this,Ri,void 0))}},Ri=new WeakMap,a1),aS=(i1=class extends Km{constructor(a){super();pe(this,_n);pe(this,Ns);pe(this,Rs);pe(this,dn);pe(this,Oi);pe(this,Vt);pe(this,tl);pe(this,Vi);se(this,Vi,!1),se(this,tl,a.defaultOptions),this.setOptions(a.options),this.observers=[],se(this,Oi,a.client),se(this,dn,j(this,Oi).getQueryCache()),this.queryKey=a.queryKey,this.queryHash=a.queryHash,se(this,Ns,iS(this.options)),this.state=a.state??j(this,Ns),this.scheduleGc()}get meta(){return this.options.meta}get promise(){var a;return(a=j(this,Vt))==null?void 0:a.promise}setOptions(a){this.options={...j(this,tl),...a},this.updateGcTime(this.options.gcTime)}optionalRemove(){!this.observers.length&&this.state.fetchStatus==="idle"&&j(this,dn).remove(this)}setData(a,s){const r=pc(this.state.data,a,this.options);return Te(this,_n,Ta).call(this,{data:r,type:"success",dataUpdatedAt:s==null?void 0:s.updatedAt,manual:s==null?void 0:s.manual}),r}setState(a,s){Te(this,_n,Ta).call(this,{type:"setState",state:a,setStateOptions:s})}cancel(a){var r,o;const s=(r=j(this,Vt))==null?void 0:r.promise;return(o=j(this,Vt))==null||o.cancel(a),s?s.then(ln).catch(ln):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(j(this,Ns))}isActive(){return this.observers.some(a=>Cn(a.options.enabled,this)!==!1)}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===yc||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStale(){return this.state.isInvalidated?!0:this.getObserversCount()>0?this.observers.some(a=>a.getCurrentResult().isStale):this.state.data===void 0}isStaleByTime(a=0){return this.state.isInvalidated||this.state.data===void 0||!Um(this.state.dataUpdatedAt,a)}onFocus(){var s;const a=this.observers.find(r=>r.shouldFetchOnWindowFocus());a==null||a.refetch({cancelRefetch:!1}),(s=j(this,Vt))==null||s.continue()}onOnline(){var s;const a=this.observers.find(r=>r.shouldFetchOnReconnect());a==null||a.refetch({cancelRefetch:!1}),(s=j(this,Vt))==null||s.continue()}addObserver(a){this.observers.includes(a)||(this.observers.push(a),this.clearGcTimeout(),j(this,dn).notify({type:"observerAdded",query:this,observer:a}))}removeObserver(a){this.observers.includes(a)&&(this.observers=this.observers.filter(s=>s!==a),this.observers.length||(j(this,Vt)&&(j(this,Vi)?j(this,Vt).cancel({revert:!0}):j(this,Vt).cancelRetry()),this.scheduleGc()),j(this,dn).notify({type:"observerRemoved",query:this,observer:a}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||Te(this,_n,Ta).call(this,{type:"invalidate"})}fetch(a,s){var p,y,v;if(this.state.fetchStatus!=="idle"){if(this.state.data!==void 0&&(s!=null&&s.cancelRefetch))this.cancel({silent:!0});else if(j(this,Vt))return j(this,Vt).continueRetry(),j(this,Vt).promise}if(a&&this.setOptions(a),!this.options.queryFn){const b=this.observers.find(S=>S.options.queryFn);b&&this.setOptions(b.options)}const r=new AbortController,o=b=>{Object.defineProperty(b,"signal",{enumerable:!0,get:()=>(se(this,Vi,!0),r.signal)})},c=()=>{const b=Gm(this.options,s),S={client:j(this,Oi),queryKey:this.queryKey,meta:this.meta};return o(S),se(this,Vi,!1),this.options.persister?this.options.persister(b,S,this):b(S)},d={fetchOptions:s,options:this.options,queryKey:this.queryKey,client:j(this,Oi),state:this.state,fetchFn:c};o(d),(p=this.options.behavior)==null||p.onFetch(d,this),se(this,Rs,this.state),(this.state.fetchStatus==="idle"||this.state.fetchMeta!==((y=d.fetchOptions)==null?void 0:y.meta))&&Te(this,_n,Ta).call(this,{type:"fetch",meta:(v=d.fetchOptions)==null?void 0:v.meta});const h=b=>{var S,E,N,C;bc(b)&&b.silent||Te(this,_n,Ta).call(this,{type:"error",error:b}),bc(b)||((E=(S=j(this,dn).config).onError)==null||E.call(S,b,this),(C=(N=j(this,dn).config).onSettled)==null||C.call(N,this.state.data,b,this)),this.scheduleGc()};return se(this,Vt,Xm({initialPromise:s==null?void 0:s.initialPromise,fn:d.fetchFn,abort:r.abort.bind(r),onSuccess:b=>{var S,E,N,C;if(b===void 0){h(new Error(`${this.queryHash} data is undefined`));return}try{this.setData(b)}catch(R){h(R);return}(E=(S=j(this,dn).config).onSuccess)==null||E.call(S,b,this),(C=(N=j(this,dn).config).onSettled)==null||C.call(N,b,this.state.error,this),this.scheduleGc()},onError:h,onFail:(b,S)=>{Te(this,_n,Ta).call(this,{type:"failed",failureCount:b,error:S})},onPause:()=>{Te(this,_n,Ta).call(this,{type:"pause"})},onContinue:()=>{Te(this,_n,Ta).call(this,{type:"continue"})},retry:d.options.retry,retryDelay:d.options.retryDelay,networkMode:d.options.networkMode,canRun:()=>!0})),j(this,Vt).start()}},Ns=new WeakMap,Rs=new WeakMap,dn=new WeakMap,Oi=new WeakMap,Vt=new WeakMap,tl=new WeakMap,Vi=new WeakMap,_n=new WeakSet,Ta=function(a){const s=r=>{switch(a.type){case"failed":return{...r,fetchFailureCount:a.failureCount,fetchFailureReason:a.error};case"pause":return{...r,fetchStatus:"paused"};case"continue":return{...r,fetchStatus:"fetching"};case"fetch":return{...r,...Zm(r.data,this.options),fetchMeta:a.meta??null};case"success":return{...r,data:a.data,dataUpdateCount:r.dataUpdateCount+1,dataUpdatedAt:a.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!a.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":const o=a.error;return bc(o)&&o.revert&&j(this,Rs)?{...j(this,Rs),fetchStatus:"idle"}:{...r,error:o,errorUpdateCount:r.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:r.fetchFailureCount+1,fetchFailureReason:o,fetchStatus:"idle",status:"error"};case"invalidate":return{...r,isInvalidated:!0};case"setState":return{...r,...a.state}}};this.state=s(this.state),bt.batch(()=>{this.observers.forEach(r=>{r.onQueryUpdate()}),j(this,dn).notify({query:this,type:"updated",action:a})})},i1);function Zm(t,a){return{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:Ym(a.networkMode)?"fetching":"paused",...t===void 0&&{error:null,status:"pending"}}}function iS(t){const a=typeof t.initialData=="function"?t.initialData():t.initialData,s=a!==void 0,r=s?typeof t.initialDataUpdatedAt=="function"?t.initialDataUpdatedAt():t.initialDataUpdatedAt:0;return{data:a,dataUpdateCount:0,dataUpdatedAt:s?r??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:s?"success":"pending",fetchStatus:"idle"}}var sS=(s1=class extends cs{constructor(a={}){super();pe(this,qn);this.config=a,se(this,qn,new Map)}build(a,s,r){const o=s.queryKey,c=s.queryHash??hc(o,s);let d=this.get(c);return d||(d=new aS({client:a,queryKey:o,queryHash:c,options:a.defaultQueryOptions(s),state:r,defaultOptions:a.getQueryDefaults(o)}),this.add(d)),d}add(a){j(this,qn).has(a.queryHash)||(j(this,qn).set(a.queryHash,a),this.notify({type:"added",query:a}))}remove(a){const s=j(this,qn).get(a.queryHash);s&&(a.destroy(),s===a&&j(this,qn).delete(a.queryHash),this.notify({type:"removed",query:a}))}clear(){bt.batch(()=>{this.getAll().forEach(a=>{this.remove(a)})})}get(a){return j(this,qn).get(a)}getAll(){return[...j(this,qn).values()]}find(a){const s={exact:!0,...a};return this.getAll().find(r=>Bm(s,r))}findAll(a={}){const s=this.getAll();return Object.keys(a).length>0?s.filter(r=>Bm(a,r)):s}notify(a){bt.batch(()=>{this.listeners.forEach(s=>{s(a)})})}onFocus(){bt.batch(()=>{this.getAll().forEach(a=>{a.onFocus()})})}onOnline(){bt.batch(()=>{this.getAll().forEach(a=>{a.onOnline()})})}},qn=new WeakMap,s1),rS=(r1=class extends Km{constructor(a){super();pe(this,Gn);pe(this,Fn);pe(this,Ht);pe(this,zi);this.mutationId=a.mutationId,se(this,Ht,a.mutationCache),se(this,Fn,[]),this.state=a.state||Wm(),this.setOptions(a.options),this.scheduleGc()}setOptions(a){this.options=a,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(a){j(this,Fn).includes(a)||(j(this,Fn).push(a),this.clearGcTimeout(),j(this,Ht).notify({type:"observerAdded",mutation:this,observer:a}))}removeObserver(a){se(this,Fn,j(this,Fn).filter(s=>s!==a)),this.scheduleGc(),j(this,Ht).notify({type:"observerRemoved",mutation:this,observer:a})}optionalRemove(){j(this,Fn).length||(this.state.status==="pending"?this.scheduleGc():j(this,Ht).remove(this))}continue(){var a;return((a=j(this,zi))==null?void 0:a.continue())??this.execute(this.state.variables)}async execute(a){var o,c,d,h,p,y,v,b,S,E,N,C,R,z,B,H,W,O,X,J;se(this,zi,Xm({fn:()=>this.options.mutationFn?this.options.mutationFn(a):Promise.reject(new Error("No mutationFn found")),onFail:(Z,Y)=>{Te(this,Gn,yi).call(this,{type:"failed",failureCount:Z,error:Y})},onPause:()=>{Te(this,Gn,yi).call(this,{type:"pause"})},onContinue:()=>{Te(this,Gn,yi).call(this,{type:"continue"})},retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>j(this,Ht).canRun(this)}));const s=this.state.status==="pending",r=!j(this,zi).canStart();try{if(!s){Te(this,Gn,yi).call(this,{type:"pending",variables:a,isPaused:r}),await((c=(o=j(this,Ht).config).onMutate)==null?void 0:c.call(o,a,this));const Y=await((h=(d=this.options).onMutate)==null?void 0:h.call(d,a));Y!==this.state.context&&Te(this,Gn,yi).call(this,{type:"pending",context:Y,variables:a,isPaused:r})}const Z=await j(this,zi).start();return await((y=(p=j(this,Ht).config).onSuccess)==null?void 0:y.call(p,Z,a,this.state.context,this)),await((b=(v=this.options).onSuccess)==null?void 0:b.call(v,Z,a,this.state.context)),await((E=(S=j(this,Ht).config).onSettled)==null?void 0:E.call(S,Z,null,this.state.variables,this.state.context,this)),await((C=(N=this.options).onSettled)==null?void 0:C.call(N,Z,null,a,this.state.context)),Te(this,Gn,yi).call(this,{type:"success",data:Z}),Z}catch(Z){try{throw await((z=(R=j(this,Ht).config).onError)==null?void 0:z.call(R,Z,a,this.state.context,this)),await((H=(B=this.options).onError)==null?void 0:H.call(B,Z,a,this.state.context)),await((O=(W=j(this,Ht).config).onSettled)==null?void 0:O.call(W,void 0,Z,this.state.variables,this.state.context,this)),await((J=(X=this.options).onSettled)==null?void 0:J.call(X,void 0,Z,a,this.state.context)),Z}finally{Te(this,Gn,yi).call(this,{type:"error",error:Z})}}finally{j(this,Ht).runNext(this)}}},Fn=new WeakMap,Ht=new WeakMap,zi=new WeakMap,Gn=new WeakSet,yi=function(a){const s=r=>{switch(a.type){case"failed":return{...r,failureCount:a.failureCount,failureReason:a.error};case"pause":return{...r,isPaused:!0};case"continue":return{...r,isPaused:!1};case"pending":return{...r,context:a.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:a.isPaused,status:"pending",variables:a.variables,submittedAt:Date.now()};case"success":return{...r,data:a.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...r,data:void 0,error:a.error,failureCount:r.failureCount+1,failureReason:a.error,isPaused:!1,status:"error"}}};this.state=s(this.state),bt.batch(()=>{j(this,Fn).forEach(r=>{r.onMutationUpdate(a)}),j(this,Ht).notify({mutation:this,type:"updated",action:a})})},r1);function Wm(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0}}var lS=(l1=class extends cs{constructor(a={}){super();pe(this,ia);pe(this,Mn);pe(this,nl);this.config=a,se(this,ia,new Set),se(this,Mn,new Map),se(this,nl,0)}build(a,s,r){const o=new rS({mutationCache:this,mutationId:++cc(this,nl)._,options:a.defaultMutationOptions(s),state:r});return this.add(o),o}add(a){j(this,ia).add(a);const s=so(a);if(typeof s=="string"){const r=j(this,Mn).get(s);r?r.push(a):j(this,Mn).set(s,[a])}this.notify({type:"added",mutation:a})}remove(a){if(j(this,ia).delete(a)){const s=so(a);if(typeof s=="string"){const r=j(this,Mn).get(s);if(r)if(r.length>1){const o=r.indexOf(a);o!==-1&&r.splice(o,1)}else r[0]===a&&j(this,Mn).delete(s)}}this.notify({type:"removed",mutation:a})}canRun(a){const s=so(a);if(typeof s=="string"){const r=j(this,Mn).get(s),o=r==null?void 0:r.find(c=>c.state.status==="pending");return!o||o===a}else return!0}runNext(a){var r;const s=so(a);if(typeof s=="string"){const o=(r=j(this,Mn).get(s))==null?void 0:r.find(c=>c!==a&&c.state.isPaused);return(o==null?void 0:o.continue())??Promise.resolve()}else return Promise.resolve()}clear(){bt.batch(()=>{j(this,ia).forEach(a=>{this.notify({type:"removed",mutation:a})}),j(this,ia).clear(),j(this,Mn).clear()})}getAll(){return Array.from(j(this,ia))}find(a){const s={exact:!0,...a};return this.getAll().find(r=>Hm(s,r))}findAll(a={}){return this.getAll().filter(s=>Hm(a,s))}notify(a){bt.batch(()=>{this.listeners.forEach(s=>{s(a)})})}resumePausedMutations(){const a=this.getAll().filter(s=>s.state.isPaused);return bt.batch(()=>Promise.all(a.map(s=>s.continue().catch(ln))))}},ia=new WeakMap,Mn=new WeakMap,nl=new WeakMap,l1);function so(t){var a;return(a=t.options.scope)==null?void 0:a.id}function $m(t){return{onFetch:(a,s)=>{var v,b,S,E,N;const r=a.options,o=(S=(b=(v=a.fetchOptions)==null?void 0:v.meta)==null?void 0:b.fetchMore)==null?void 0:S.direction,c=((E=a.state.data)==null?void 0:E.pages)||[],d=((N=a.state.data)==null?void 0:N.pageParams)||[];let h={pages:[],pageParams:[]},p=0;const y=async()=>{let C=!1;const R=H=>{Object.defineProperty(H,"signal",{enumerable:!0,get:()=>(a.signal.aborted?C=!0:a.signal.addEventListener("abort",()=>{C=!0}),a.signal)})},z=Gm(a.options,a.fetchOptions),B=async(H,W,O)=>{if(C)return Promise.reject();if(W==null&&H.pages.length)return Promise.resolve(H);const X={client:a.client,queryKey:a.queryKey,pageParam:W,direction:O?"backward":"forward",meta:a.options.meta};R(X);const J=await z(X),{maxPages:Z}=a.options,Y=O?J2:$2;return{pages:Y(H.pages,J,Z),pageParams:Y(H.pageParams,W,Z)}};if(o&&c.length){const H=o==="backward",W=H?oS:Jm,O={pages:c,pageParams:d},X=W(r,O);h=await B(O,X,H)}else{const H=t??c.length;do{const W=p===0?d[0]??r.initialPageParam:Jm(r,h);if(p>0&&W==null)break;h=await B(h,W),p++}while(p<H)}return h};a.options.persister?a.fetchFn=()=>{var C,R;return(R=(C=a.options).persister)==null?void 0:R.call(C,y,{client:a.client,queryKey:a.queryKey,meta:a.options.meta,signal:a.signal},s)}:a.fetchFn=y}}}function Jm(t,{pages:a,pageParams:s}){const r=a.length-1;return a.length>0?t.getNextPageParam(a[r],a,s[r],s):void 0}function oS(t,{pages:a,pageParams:s}){var r;return a.length>0?(r=t.getPreviousPageParam)==null?void 0:r.call(t,a[0],a,s[0],s):void 0}var uS=(o1=class{constructor(t={}){pe(this,at);pe(this,Ba);pe(this,Ha);pe(this,Os);pe(this,Vs);pe(this,Pa);pe(this,zs);pe(this,ks);se(this,at,t.queryCache||new sS),se(this,Ba,t.mutationCache||new lS),se(this,Ha,t.defaultOptions||{}),se(this,Os,new Map),se(this,Vs,new Map),se(this,Pa,0)}mount(){cc(this,Pa)._++,j(this,Pa)===1&&(se(this,zs,gc.subscribe(async t=>{t&&(await this.resumePausedMutations(),j(this,at).onFocus())})),se(this,ks,io.subscribe(async t=>{t&&(await this.resumePausedMutations(),j(this,at).onOnline())})))}unmount(){var t,a;cc(this,Pa)._--,j(this,Pa)===0&&((t=j(this,zs))==null||t.call(this),se(this,zs,void 0),(a=j(this,ks))==null||a.call(this),se(this,ks,void 0))}isFetching(t){return j(this,at).findAll({...t,fetchStatus:"fetching"}).length}isMutating(t){return j(this,Ba).findAll({...t,status:"pending"}).length}getQueryData(t){var s;const a=this.defaultQueryOptions({queryKey:t});return(s=j(this,at).get(a.queryHash))==null?void 0:s.state.data}ensureQueryData(t){const a=this.defaultQueryOptions(t),s=j(this,at).build(this,a),r=s.state.data;return r===void 0?this.fetchQuery(t):(t.revalidateIfStale&&s.isStaleByTime(fs(a.staleTime,s))&&this.prefetchQuery(a),Promise.resolve(r))}getQueriesData(t){return j(this,at).findAll(t).map(({queryKey:a,state:s})=>{const r=s.data;return[a,r]})}setQueryData(t,a,s){const r=this.defaultQueryOptions({queryKey:t}),o=j(this,at).get(r.queryHash),c=o==null?void 0:o.state.data,d=Z2(a,c);if(d!==void 0)return j(this,at).build(this,r).setData(d,{...s,manual:!0})}setQueriesData(t,a,s){return bt.batch(()=>j(this,at).findAll(t).map(({queryKey:r})=>[r,this.setQueryData(r,a,s)]))}getQueryState(t){var s;const a=this.defaultQueryOptions({queryKey:t});return(s=j(this,at).get(a.queryHash))==null?void 0:s.state}removeQueries(t){const a=j(this,at);bt.batch(()=>{a.findAll(t).forEach(s=>{a.remove(s)})})}resetQueries(t,a){const s=j(this,at);return bt.batch(()=>(s.findAll(t).forEach(r=>{r.reset()}),this.refetchQueries({type:"active",...t},a)))}cancelQueries(t,a={}){const s={revert:!0,...a},r=bt.batch(()=>j(this,at).findAll(t).map(o=>o.cancel(s)));return Promise.all(r).then(ln).catch(ln)}invalidateQueries(t,a={}){return bt.batch(()=>(j(this,at).findAll(t).forEach(s=>{s.invalidate()}),(t==null?void 0:t.refetchType)==="none"?Promise.resolve():this.refetchQueries({...t,type:(t==null?void 0:t.refetchType)??(t==null?void 0:t.type)??"active"},a)))}refetchQueries(t,a={}){const s={...a,cancelRefetch:a.cancelRefetch??!0},r=bt.batch(()=>j(this,at).findAll(t).filter(o=>!o.isDisabled()).map(o=>{let c=o.fetch(void 0,s);return s.throwOnError||(c=c.catch(ln)),o.state.fetchStatus==="paused"?Promise.resolve():c}));return Promise.all(r).then(ln)}fetchQuery(t){const a=this.defaultQueryOptions(t);a.retry===void 0&&(a.retry=!1);const s=j(this,at).build(this,a);return s.isStaleByTime(fs(a.staleTime,s))?s.fetch(a):Promise.resolve(s.state.data)}prefetchQuery(t){return this.fetchQuery(t).then(ln).catch(ln)}fetchInfiniteQuery(t){return t.behavior=$m(t.pages),this.fetchQuery(t)}prefetchInfiniteQuery(t){return this.fetchInfiniteQuery(t).then(ln).catch(ln)}ensureInfiniteQueryData(t){return t.behavior=$m(t.pages),this.ensureQueryData(t)}resumePausedMutations(){return io.isOnline()?j(this,Ba).resumePausedMutations():Promise.resolve()}getQueryCache(){return j(this,at)}getMutationCache(){return j(this,Ba)}getDefaultOptions(){return j(this,Ha)}setDefaultOptions(t){se(this,Ha,t)}setQueryDefaults(t,a){j(this,Os).set(vi(t),{queryKey:t,defaultOptions:a})}getQueryDefaults(t){const a=[...j(this,Os).values()],s={};return a.forEach(r=>{Cr(t,r.queryKey)&&Object.assign(s,r.defaultOptions)}),s}setMutationDefaults(t,a){j(this,Vs).set(vi(t),{mutationKey:t,defaultOptions:a})}getMutationDefaults(t){const a=[...j(this,Vs).values()],s={};return a.forEach(r=>{Cr(t,r.mutationKey)&&Object.assign(s,r.defaultOptions)}),s}defaultQueryOptions(t){if(t._defaulted)return t;const a={...j(this,Ha).queries,...this.getQueryDefaults(t.queryKey),...t,_defaulted:!0};return a.queryHash||(a.queryHash=hc(a.queryKey,a)),a.refetchOnReconnect===void 0&&(a.refetchOnReconnect=a.networkMode!=="always"),a.throwOnError===void 0&&(a.throwOnError=!!a.suspense),!a.networkMode&&a.persister&&(a.networkMode="offlineFirst"),a.queryFn===yc&&(a.enabled=!1),a}defaultMutationOptions(t){return t!=null&&t._defaulted?t:{...j(this,Ha).mutations,...(t==null?void 0:t.mutationKey)&&this.getMutationDefaults(t.mutationKey),...t,_defaulted:!0}}clear(){j(this,at).clear(),j(this,Ba).clear()}},at=new WeakMap,Ba=new WeakMap,Ha=new WeakMap,Os=new WeakMap,Vs=new WeakMap,Pa=new WeakMap,zs=new WeakMap,ks=new WeakMap,o1),cS=(u1=class extends cs{constructor(a,s){super();pe(this,Pe);pe(this,Qt);pe(this,Ve);pe(this,al);pe(this,Pt);pe(this,ki);pe(this,Ls);pe(this,qa);pe(this,Yn);pe(this,il);pe(this,Us);pe(this,Bs);pe(this,Li);pe(this,Ui);pe(this,Fa);pe(this,Hs,new Set);this.options=s,se(this,Qt,a),se(this,Yn,null),se(this,qa,vc()),this.options.experimental_prefetchInRender||j(this,qa).reject(new Error("experimental_prefetchInRender feature flag is not enabled")),this.bindMethods(),this.setOptions(s)}bindMethods(){this.refetch=this.refetch.bind(this)}onSubscribe(){this.listeners.size===1&&(j(this,Ve).addObserver(this),Im(j(this,Ve),this.options)?Te(this,Pe,no).call(this):this.updateResult(),Te(this,Pe,Nm).call(this))}onUnsubscribe(){this.hasListeners()||this.destroy()}shouldFetchOnReconnect(){return xc(j(this,Ve),this.options,this.options.refetchOnReconnect)}shouldFetchOnWindowFocus(){return xc(j(this,Ve),this.options,this.options.refetchOnWindowFocus)}destroy(){this.listeners=new Set,Te(this,Pe,Rm).call(this),Te(this,Pe,Om).call(this),j(this,Ve).removeObserver(this)}setOptions(a,s){const r=this.options,o=j(this,Ve);if(this.options=j(this,Qt).defaultQueryOptions(a),this.options.enabled!==void 0&&typeof this.options.enabled!="boolean"&&typeof this.options.enabled!="function"&&typeof Cn(this.options.enabled,j(this,Ve))!="boolean")throw new Error("Expected enabled to be a boolean or a callback that returns a boolean");Te(this,Pe,Vm).call(this),j(this,Ve).setOptions(this.options),r._defaulted&&!ao(this.options,r)&&j(this,Qt).getQueryCache().notify({type:"observerOptionsUpdated",query:j(this,Ve),observer:this});const c=this.hasListeners();c&&e0(j(this,Ve),o,this.options,r)&&Te(this,Pe,no).call(this),this.updateResult(s),c&&(j(this,Ve)!==o||Cn(this.options.enabled,j(this,Ve))!==Cn(r.enabled,j(this,Ve))||fs(this.options.staleTime,j(this,Ve))!==fs(r.staleTime,j(this,Ve)))&&Te(this,Pe,Dm).call(this);const d=Te(this,Pe,_m).call(this);c&&(j(this,Ve)!==o||Cn(this.options.enabled,j(this,Ve))!==Cn(r.enabled,j(this,Ve))||d!==j(this,Fa))&&Te(this,Pe,Mm).call(this,d)}getOptimisticResult(a){const s=j(this,Qt).getQueryCache().build(j(this,Qt),a),r=this.createResult(s,a);return dS(this,r)&&(se(this,Pt,r),se(this,Ls,this.options),se(this,ki,j(this,Ve).state)),r}getCurrentResult(){return j(this,Pt)}trackResult(a,s){const r={};return Object.keys(a).forEach(o=>{Object.defineProperty(r,o,{configurable:!1,enumerable:!0,get:()=>(this.trackProp(o),s==null||s(o),a[o])})}),r}trackProp(a){j(this,Hs).add(a)}getCurrentQuery(){return j(this,Ve)}refetch({...a}={}){return this.fetch({...a})}fetchOptimistic(a){const s=j(this,Qt).defaultQueryOptions(a),r=j(this,Qt).getQueryCache().build(j(this,Qt),s);return r.fetch().then(()=>this.createResult(r,s))}fetch(a){return Te(this,Pe,no).call(this,{...a,cancelRefetch:a.cancelRefetch??!0}).then(()=>(this.updateResult(),j(this,Pt)))}createResult(a,s){var Z;const r=j(this,Ve),o=this.options,c=j(this,Pt),d=j(this,ki),h=j(this,Ls),y=a!==r?a.state:j(this,al),{state:v}=a;let b={...v},S=!1,E;if(s._optimisticResults){const Y=this.hasListeners(),te=!Y&&Im(a,s),je=Y&&e0(a,r,s,o);(te||je)&&(b={...b,...Zm(v.data,a.options)}),s._optimisticResults==="isRestoring"&&(b.fetchStatus="idle")}let{error:N,errorUpdatedAt:C,status:R}=b;if(s.select&&b.data!==void 0)if(c&&b.data===(d==null?void 0:d.data)&&s.select===j(this,il))E=j(this,Us);else try{se(this,il,s.select),E=s.select(b.data),E=pc(c==null?void 0:c.data,E,s),se(this,Us,E),se(this,Yn,null)}catch(Y){se(this,Yn,Y)}else E=b.data;if(s.placeholderData!==void 0&&E===void 0&&R==="pending"){let Y;if(c!=null&&c.isPlaceholderData&&s.placeholderData===(h==null?void 0:h.placeholderData))Y=c.data;else if(Y=typeof s.placeholderData=="function"?s.placeholderData((Z=j(this,Bs))==null?void 0:Z.state.data,j(this,Bs)):s.placeholderData,s.select&&Y!==void 0)try{Y=s.select(Y),se(this,Yn,null)}catch(te){se(this,Yn,te)}Y!==void 0&&(R="success",E=pc(c==null?void 0:c.data,Y,s),S=!0)}j(this,Yn)&&(N=j(this,Yn),E=j(this,Us),C=Date.now(),R="error");const z=b.fetchStatus==="fetching",B=R==="pending",H=R==="error",W=B&&z,O=E!==void 0,J={status:R,fetchStatus:b.fetchStatus,isPending:B,isSuccess:R==="success",isError:H,isInitialLoading:W,isLoading:W,data:E,dataUpdatedAt:b.dataUpdatedAt,error:N,errorUpdatedAt:C,failureCount:b.fetchFailureCount,failureReason:b.fetchFailureReason,errorUpdateCount:b.errorUpdateCount,isFetched:b.dataUpdateCount>0||b.errorUpdateCount>0,isFetchedAfterMount:b.dataUpdateCount>y.dataUpdateCount||b.errorUpdateCount>y.errorUpdateCount,isFetching:z,isRefetching:z&&!B,isLoadingError:H&&!O,isPaused:b.fetchStatus==="paused",isPlaceholderData:S,isRefetchError:H&&O,isStale:Sc(a,s),refetch:this.refetch,promise:j(this,qa)};if(this.options.experimental_prefetchInRender){const Y=le=>{J.status==="error"?le.reject(J.error):J.data!==void 0&&le.resolve(J.data)},te=()=>{const le=se(this,qa,J.promise=vc());Y(le)},je=j(this,qa);switch(je.status){case"pending":a.queryHash===r.queryHash&&Y(je);break;case"fulfilled":(J.status==="error"||J.data!==je.value)&&te();break;case"rejected":(J.status!=="error"||J.error!==je.reason)&&te();break}}return J}updateResult(a){const s=j(this,Pt),r=this.createResult(j(this,Ve),this.options);if(se(this,ki,j(this,Ve).state),se(this,Ls,this.options),j(this,ki).data!==void 0&&se(this,Bs,j(this,Ve)),ao(r,s))return;se(this,Pt,r);const o={},c=()=>{if(!s)return!0;const{notifyOnChangeProps:d}=this.options,h=typeof d=="function"?d():d;if(h==="all"||!h&&!j(this,Hs).size)return!0;const p=new Set(h??j(this,Hs));return this.options.throwOnError&&p.add("error"),Object.keys(j(this,Pt)).some(y=>{const v=y;return j(this,Pt)[v]!==s[v]&&p.has(v)})};(a==null?void 0:a.listeners)!==!1&&c()&&(o.listeners=!0),Te(this,Pe,Q2).call(this,{...o,...a})}onQueryUpdate(){this.updateResult(),this.hasListeners()&&Te(this,Pe,Nm).call(this)}},Qt=new WeakMap,Ve=new WeakMap,al=new WeakMap,Pt=new WeakMap,ki=new WeakMap,Ls=new WeakMap,qa=new WeakMap,Yn=new WeakMap,il=new WeakMap,Us=new WeakMap,Bs=new WeakMap,Li=new WeakMap,Ui=new WeakMap,Fa=new WeakMap,Hs=new WeakMap,Pe=new WeakSet,no=function(a){Te(this,Pe,Vm).call(this);let s=j(this,Ve).fetch(this.options,a);return a!=null&&a.throwOnError||(s=s.catch(ln)),s},Dm=function(){Te(this,Pe,Rm).call(this);const a=fs(this.options.staleTime,j(this,Ve));if(gi||j(this,Pt).isStale||!dc(a))return;const r=Um(j(this,Pt).dataUpdatedAt,a)+1;se(this,Li,setTimeout(()=>{j(this,Pt).isStale||this.updateResult()},r))},_m=function(){return(typeof this.options.refetchInterval=="function"?this.options.refetchInterval(j(this,Ve)):this.options.refetchInterval)??!1},Mm=function(a){Te(this,Pe,Om).call(this),se(this,Fa,a),!(gi||Cn(this.options.enabled,j(this,Ve))===!1||!dc(j(this,Fa))||j(this,Fa)===0)&&se(this,Ui,setInterval(()=>{(this.options.refetchIntervalInBackground||gc.isFocused())&&Te(this,Pe,no).call(this)},j(this,Fa)))},Nm=function(){Te(this,Pe,Dm).call(this),Te(this,Pe,Mm).call(this,Te(this,Pe,_m).call(this))},Rm=function(){j(this,Li)&&(clearTimeout(j(this,Li)),se(this,Li,void 0))},Om=function(){j(this,Ui)&&(clearInterval(j(this,Ui)),se(this,Ui,void 0))},Vm=function(){const a=j(this,Qt).getQueryCache().build(j(this,Qt),this.options);if(a===j(this,Ve))return;const s=j(this,Ve);se(this,Ve,a),se(this,al,a.state),this.hasListeners()&&(s==null||s.removeObserver(this),a.addObserver(this))},Q2=function(a){bt.batch(()=>{a.listeners&&this.listeners.forEach(s=>{s(j(this,Pt))}),j(this,Qt).getQueryCache().notify({query:j(this,Ve),type:"observerResultsUpdated"})})},u1);function fS(t,a){return Cn(a.enabled,t)!==!1&&t.state.data===void 0&&!(t.state.status==="error"&&a.retryOnMount===!1)}function Im(t,a){return fS(t,a)||t.state.data!==void 0&&xc(t,a,a.refetchOnMount)}function xc(t,a,s){if(Cn(a.enabled,t)!==!1){const r=typeof s=="function"?s(t):s;return r==="always"||r!==!1&&Sc(t,a)}return!1}function e0(t,a,s,r){return(t!==a||Cn(r.enabled,t)===!1)&&(!s.suspense||t.state.status!=="error")&&Sc(t,s)}function Sc(t,a){return Cn(a.enabled,t)!==!1&&t.isStaleByTime(fs(a.staleTime,t))}function dS(t,a){return!ao(t.getCurrentResult(),a)}var hS=(c1=class extends cs{constructor(s,r){super();pe(this,ra);pe(this,Ga);pe(this,Ya);pe(this,Xt);pe(this,sa);se(this,Ga,s),this.setOptions(r),this.bindMethods(),Te(this,ra,fc).call(this)}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(s){var o;const r=this.options;this.options=j(this,Ga).defaultMutationOptions(s),ao(this.options,r)||j(this,Ga).getMutationCache().notify({type:"observerOptionsUpdated",mutation:j(this,Xt),observer:this}),r!=null&&r.mutationKey&&this.options.mutationKey&&vi(r.mutationKey)!==vi(this.options.mutationKey)?this.reset():((o=j(this,Xt))==null?void 0:o.state.status)==="pending"&&j(this,Xt).setOptions(this.options)}onUnsubscribe(){var s;this.hasListeners()||(s=j(this,Xt))==null||s.removeObserver(this)}onMutationUpdate(s){Te(this,ra,fc).call(this),Te(this,ra,zm).call(this,s)}getCurrentResult(){return j(this,Ya)}reset(){var s;(s=j(this,Xt))==null||s.removeObserver(this),se(this,Xt,void 0),Te(this,ra,fc).call(this),Te(this,ra,zm).call(this)}mutate(s,r){var o;return se(this,sa,r),(o=j(this,Xt))==null||o.removeObserver(this),se(this,Xt,j(this,Ga).getMutationCache().build(j(this,Ga),this.options)),j(this,Xt).addObserver(this),j(this,Xt).execute(s)}},Ga=new WeakMap,Ya=new WeakMap,Xt=new WeakMap,sa=new WeakMap,ra=new WeakSet,fc=function(){var r;const s=((r=j(this,Xt))==null?void 0:r.state)??Wm();se(this,Ya,{...s,isPending:s.status==="pending",isSuccess:s.status==="success",isError:s.status==="error",isIdle:s.status==="idle",mutate:this.mutate,reset:this.reset})},zm=function(s){bt.batch(()=>{var r,o,c,d,h,p,y,v;if(j(this,sa)&&this.hasListeners()){const b=j(this,Ya).variables,S=j(this,Ya).context;(s==null?void 0:s.type)==="success"?((o=(r=j(this,sa)).onSuccess)==null||o.call(r,s.data,b,S),(d=(c=j(this,sa)).onSettled)==null||d.call(c,s.data,null,b,S)):(s==null?void 0:s.type)==="error"&&((p=(h=j(this,sa)).onError)==null||p.call(h,s.error,b,S),(v=(y=j(this,sa)).onSettled)==null||v.call(y,void 0,s.error,b,S))}this.listeners.forEach(b=>{b(j(this,Ya))})})},c1),wc={exports:{}},we={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var t0;function mS(){if(t0)return we;t0=1;var t=Symbol.for("react.transitional.element"),a=Symbol.for("react.portal"),s=Symbol.for("react.fragment"),r=Symbol.for("react.strict_mode"),o=Symbol.for("react.profiler"),c=Symbol.for("react.consumer"),d=Symbol.for("react.context"),h=Symbol.for("react.forward_ref"),p=Symbol.for("react.suspense"),y=Symbol.for("react.memo"),v=Symbol.for("react.lazy"),b=Symbol.iterator;function S(D){return D===null||typeof D!="object"?null:(D=b&&D[b]||D["@@iterator"],typeof D=="function"?D:null)}var E={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},N=Object.assign,C={};function R(D,K,de){this.props=D,this.context=K,this.refs=C,this.updater=de||E}R.prototype.isReactComponent={},R.prototype.setState=function(D,K){if(typeof D!="object"&&typeof D!="function"&&D!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,D,K,"setState")},R.prototype.forceUpdate=function(D){this.updater.enqueueForceUpdate(this,D,"forceUpdate")};function z(){}z.prototype=R.prototype;function B(D,K,de){this.props=D,this.context=K,this.refs=C,this.updater=de||E}var H=B.prototype=new z;H.constructor=B,N(H,R.prototype),H.isPureReactComponent=!0;var W=Array.isArray,O={H:null,A:null,T:null,S:null},X=Object.prototype.hasOwnProperty;function J(D,K,de,he,ie,_e){return de=_e.ref,{$$typeof:t,type:D,key:K,ref:de!==void 0?de:null,props:_e}}function Z(D,K){return J(D.type,K,void 0,void 0,void 0,D.props)}function Y(D){return typeof D=="object"&&D!==null&&D.$$typeof===t}function te(D){var K={"=":"=0",":":"=2"};return"$"+D.replace(/[=:]/g,function(de){return K[de]})}var je=/\/+/g;function le(D,K){return typeof D=="object"&&D!==null&&D.key!=null?te(""+D.key):K.toString(36)}function ye(){}function Ae(D){switch(D.status){case"fulfilled":return D.value;case"rejected":throw D.reason;default:switch(typeof D.status=="string"?D.then(ye,ye):(D.status="pending",D.then(function(K){D.status==="pending"&&(D.status="fulfilled",D.value=K)},function(K){D.status==="pending"&&(D.status="rejected",D.reason=K)})),D.status){case"fulfilled":return D.value;case"rejected":throw D.reason}}throw D}function ze(D,K,de,he,ie){var _e=typeof D;(_e==="undefined"||_e==="boolean")&&(D=null);var be=!1;if(D===null)be=!0;else switch(_e){case"bigint":case"string":case"number":be=!0;break;case"object":switch(D.$$typeof){case t:case a:be=!0;break;case v:return be=D._init,ze(be(D._payload),K,de,he,ie)}}if(be)return ie=ie(D),be=he===""?"."+le(D,0):he,W(ie)?(de="",be!=null&&(de=be.replace(je,"$&/")+"/"),ze(ie,K,de,"",function(Fe){return Fe})):ie!=null&&(Y(ie)&&(ie=Z(ie,de+(ie.key==null||D&&D.key===ie.key?"":(""+ie.key).replace(je,"$&/")+"/")+be)),K.push(ie)),1;be=0;var Me=he===""?".":he+":";if(W(D))for(var Se=0;Se<D.length;Se++)he=D[Se],_e=Me+le(he,Se),be+=ze(he,K,de,_e,ie);else if(Se=S(D),typeof Se=="function")for(D=Se.call(D),Se=0;!(he=D.next()).done;)he=he.value,_e=Me+le(he,Se++),be+=ze(he,K,de,_e,ie);else if(_e==="object"){if(typeof D.then=="function")return ze(Ae(D),K,de,he,ie);throw K=String(D),Error("Objects are not valid as a React child (found: "+(K==="[object Object]"?"object with keys {"+Object.keys(D).join(", ")+"}":K)+"). If you meant to render a collection of children, use an array instead.")}return be}function $(D,K,de){if(D==null)return D;var he=[],ie=0;return ze(D,he,"","",function(_e){return K.call(de,_e,ie++)}),he}function ne(D){if(D._status===-1){var K=D._result;K=K(),K.then(function(de){(D._status===0||D._status===-1)&&(D._status=1,D._result=de)},function(de){(D._status===0||D._status===-1)&&(D._status=2,D._result=de)}),D._status===-1&&(D._status=0,D._result=K)}if(D._status===1)return D._result.default;throw D._result}var re=typeof reportError=="function"?reportError:function(D){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var K=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof D=="object"&&D!==null&&typeof D.message=="string"?String(D.message):String(D),error:D});if(!window.dispatchEvent(K))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",D);return}console.error(D)};function De(){}return we.Children={map:$,forEach:function(D,K,de){$(D,function(){K.apply(this,arguments)},de)},count:function(D){var K=0;return $(D,function(){K++}),K},toArray:function(D){return $(D,function(K){return K})||[]},only:function(D){if(!Y(D))throw Error("React.Children.only expected to receive a single React element child.");return D}},we.Component=R,we.Fragment=s,we.Profiler=o,we.PureComponent=B,we.StrictMode=r,we.Suspense=p,we.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=O,we.act=function(){throw Error("act(...) is not supported in production builds of React.")},we.cache=function(D){return function(){return D.apply(null,arguments)}},we.cloneElement=function(D,K,de){if(D==null)throw Error("The argument must be a React element, but you passed "+D+".");var he=N({},D.props),ie=D.key,_e=void 0;if(K!=null)for(be in K.ref!==void 0&&(_e=void 0),K.key!==void 0&&(ie=""+K.key),K)!X.call(K,be)||be==="key"||be==="__self"||be==="__source"||be==="ref"&&K.ref===void 0||(he[be]=K[be]);var be=arguments.length-2;if(be===1)he.children=de;else if(1<be){for(var Me=Array(be),Se=0;Se<be;Se++)Me[Se]=arguments[Se+2];he.children=Me}return J(D.type,ie,void 0,void 0,_e,he)},we.createContext=function(D){return D={$$typeof:d,_currentValue:D,_currentValue2:D,_threadCount:0,Provider:null,Consumer:null},D.Provider=D,D.Consumer={$$typeof:c,_context:D},D},we.createElement=function(D,K,de){var he,ie={},_e=null;if(K!=null)for(he in K.key!==void 0&&(_e=""+K.key),K)X.call(K,he)&&he!=="key"&&he!=="__self"&&he!=="__source"&&(ie[he]=K[he]);var be=arguments.length-2;if(be===1)ie.children=de;else if(1<be){for(var Me=Array(be),Se=0;Se<be;Se++)Me[Se]=arguments[Se+2];ie.children=Me}if(D&&D.defaultProps)for(he in be=D.defaultProps,be)ie[he]===void 0&&(ie[he]=be[he]);return J(D,_e,void 0,void 0,null,ie)},we.createRef=function(){return{current:null}},we.forwardRef=function(D){return{$$typeof:h,render:D}},we.isValidElement=Y,we.lazy=function(D){return{$$typeof:v,_payload:{_status:-1,_result:D},_init:ne}},we.memo=function(D,K){return{$$typeof:y,type:D,compare:K===void 0?null:K}},we.startTransition=function(D){var K=O.T,de={};O.T=de;try{var he=D(),ie=O.S;ie!==null&&ie(de,he),typeof he=="object"&&he!==null&&typeof he.then=="function"&&he.then(De,re)}catch(_e){re(_e)}finally{O.T=K}},we.unstable_useCacheRefresh=function(){return O.H.useCacheRefresh()},we.use=function(D){return O.H.use(D)},we.useActionState=function(D,K,de){return O.H.useActionState(D,K,de)},we.useCallback=function(D,K){return O.H.useCallback(D,K)},we.useContext=function(D){return O.H.useContext(D)},we.useDebugValue=function(){},we.useDeferredValue=function(D,K){return O.H.useDeferredValue(D,K)},we.useEffect=function(D,K){return O.H.useEffect(D,K)},we.useId=function(){return O.H.useId()},we.useImperativeHandle=function(D,K,de){return O.H.useImperativeHandle(D,K,de)},we.useInsertionEffect=function(D,K){return O.H.useInsertionEffect(D,K)},we.useLayoutEffect=function(D,K){return O.H.useLayoutEffect(D,K)},we.useMemo=function(D,K){return O.H.useMemo(D,K)},we.useOptimistic=function(D,K){return O.H.useOptimistic(D,K)},we.useReducer=function(D,K,de){return O.H.useReducer(D,K,de)},we.useRef=function(D){return O.H.useRef(D)},we.useState=function(D){return O.H.useState(D)},we.useSyncExternalStore=function(D,K,de){return O.H.useSyncExternalStore(D,K,de)},we.useTransition=function(){return O.H.useTransition()},we.version="19.0.0",we}var n0;function ro(){return n0||(n0=1,wc.exports=mS()),wc.exports}var w=ro();const Lt=Ca(w),a0=gt({__proto__:null,default:Lt},[w]);var i0=w.createContext(void 0),jr=t=>{const a=w.useContext(i0);if(!a)throw new Error("No QueryClient set, use QueryClientProvider to set one");return a},pS=({client:t,children:a})=>(w.useEffect(()=>(t.mount(),()=>{t.unmount()}),[t]),m.jsx(i0.Provider,{value:t,children:a})),s0=w.createContext(!1),yS=()=>w.useContext(s0);s0.Provider;function gS(){let t=!1;return{clearReset:()=>{t=!1},reset:()=>{t=!0},isReset:()=>t}}var vS=w.createContext(gS()),bS=()=>w.useContext(vS);function r0(t,a){return typeof t=="function"?t(...a):!!t}function Ec(){}var xS=(t,a)=>{(t.suspense||t.throwOnError||t.experimental_prefetchInRender)&&(a.isReset()||(t.retryOnMount=!1))},SS=t=>{w.useEffect(()=>{t.clearReset()},[t])},wS=({result:t,errorResetBoundary:a,throwOnError:s,query:r,suspense:o})=>t.isError&&!a.isReset()&&!t.isFetching&&r&&(o&&t.data===void 0||r0(s,[t.error,r])),ES=t=>{const a=t.staleTime;t.suspense&&(t.staleTime=typeof a=="function"?(...s)=>Math.max(a(...s),1e3):Math.max(a??1e3,1e3),typeof t.gcTime=="number"&&(t.gcTime=Math.max(t.gcTime,1e3)))},TS=(t,a)=>t.isLoading&&t.isFetching&&!a,CS=(t,a)=>(t==null?void 0:t.suspense)&&a.isPending,l0=(t,a,s)=>a.fetchOptimistic(t).catch(()=>{s.clearReset()});function jS(t,a,s){var b,S,E,N,C;const r=jr(),o=yS(),c=bS(),d=r.defaultQueryOptions(t);(S=(b=r.getDefaultOptions().queries)==null?void 0:b._experimental_beforeQuery)==null||S.call(b,d),d._optimisticResults=o?"isRestoring":"optimistic",ES(d),xS(d,c),SS(c);const h=!r.getQueryCache().get(d.queryHash),[p]=w.useState(()=>new a(r,d)),y=p.getOptimisticResult(d),v=!o&&t.subscribed!==!1;if(w.useSyncExternalStore(w.useCallback(R=>{const z=v?p.subscribe(bt.batchCalls(R)):Ec;return p.updateResult(),z},[p,v]),()=>p.getCurrentResult(),()=>p.getCurrentResult()),w.useEffect(()=>{p.setOptions(d,{listeners:!1})},[d,p]),CS(d,y))throw l0(d,p,c);if(wS({result:y,errorResetBoundary:c,throwOnError:d.throwOnError,query:r.getQueryCache().get(d.queryHash),suspense:d.suspense}))throw y.error;if((N=(E=r.getDefaultOptions().queries)==null?void 0:E._experimental_afterQuery)==null||N.call(E,d,y),d.experimental_prefetchInRender&&!gi&&TS(y,o)){const R=h?l0(d,p,c):(C=r.getQueryCache().get(d.queryHash))==null?void 0:C.promise;R==null||R.catch(Ec).finally(()=>{p.updateResult()})}return d.notifyOnChangeProps?y:p.trackResult(y)}function o0(t,a){return jS(t,cS)}function Ar(t,a){const s=jr(),[r]=w.useState(()=>new hS(s,t));w.useEffect(()=>{r.setOptions(t)},[r,t]);const o=w.useSyncExternalStore(w.useCallback(d=>r.subscribe(bt.batchCalls(d)),[r]),()=>r.getCurrentResult(),()=>r.getCurrentResult()),c=w.useCallback((d,h)=>{r.mutate(d,h).catch(Ec)},[r]);if(o.error&&r0(r.options.throwOnError,[o.error]))throw o.error;return{...o,mutate:c,mutateAsync:o.mutate}}var Tc={exports:{}},Dr={},Cc={exports:{}},jc={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var u0;function AS(){return u0||(u0=1,function(t){function a($,ne){var re=$.length;$.push(ne);e:for(;0<re;){var De=re-1>>>1,D=$[De];if(0<o(D,ne))$[De]=ne,$[re]=D,re=De;else break e}}function s($){return $.length===0?null:$[0]}function r($){if($.length===0)return null;var ne=$[0],re=$.pop();if(re!==ne){$[0]=re;e:for(var De=0,D=$.length,K=D>>>1;De<K;){var de=2*(De+1)-1,he=$[de],ie=de+1,_e=$[ie];if(0>o(he,re))ie<D&&0>o(_e,he)?($[De]=_e,$[ie]=re,De=ie):($[De]=he,$[de]=re,De=de);else if(ie<D&&0>o(_e,re))$[De]=_e,$[ie]=re,De=ie;else break e}}return ne}function o($,ne){var re=$.sortIndex-ne.sortIndex;return re!==0?re:$.id-ne.id}if(t.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var c=performance;t.unstable_now=function(){return c.now()}}else{var d=Date,h=d.now();t.unstable_now=function(){return d.now()-h}}var p=[],y=[],v=1,b=null,S=3,E=!1,N=!1,C=!1,R=typeof setTimeout=="function"?setTimeout:null,z=typeof clearTimeout=="function"?clearTimeout:null,B=typeof setImmediate<"u"?setImmediate:null;function H($){for(var ne=s(y);ne!==null;){if(ne.callback===null)r(y);else if(ne.startTime<=$)r(y),ne.sortIndex=ne.expirationTime,a(p,ne);else break;ne=s(y)}}function W($){if(C=!1,H($),!N)if(s(p)!==null)N=!0,Ae();else{var ne=s(y);ne!==null&&ze(W,ne.startTime-$)}}var O=!1,X=-1,J=5,Z=-1;function Y(){return!(t.unstable_now()-Z<J)}function te(){if(O){var $=t.unstable_now();Z=$;var ne=!0;try{e:{N=!1,C&&(C=!1,z(X),X=-1),E=!0;var re=S;try{t:{for(H($),b=s(p);b!==null&&!(b.expirationTime>$&&Y());){var De=b.callback;if(typeof De=="function"){b.callback=null,S=b.priorityLevel;var D=De(b.expirationTime<=$);if($=t.unstable_now(),typeof D=="function"){b.callback=D,H($),ne=!0;break t}b===s(p)&&r(p),H($)}else r(p);b=s(p)}if(b!==null)ne=!0;else{var K=s(y);K!==null&&ze(W,K.startTime-$),ne=!1}}break e}finally{b=null,S=re,E=!1}ne=void 0}}finally{ne?je():O=!1}}}var je;if(typeof B=="function")je=function(){B(te)};else if(typeof MessageChannel<"u"){var le=new MessageChannel,ye=le.port2;le.port1.onmessage=te,je=function(){ye.postMessage(null)}}else je=function(){R(te,0)};function Ae(){O||(O=!0,je())}function ze($,ne){X=R(function(){$(t.unstable_now())},ne)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function($){$.callback=null},t.unstable_continueExecution=function(){N||E||(N=!0,Ae())},t.unstable_forceFrameRate=function($){0>$||125<$?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):J=0<$?Math.floor(1e3/$):5},t.unstable_getCurrentPriorityLevel=function(){return S},t.unstable_getFirstCallbackNode=function(){return s(p)},t.unstable_next=function($){switch(S){case 1:case 2:case 3:var ne=3;break;default:ne=S}var re=S;S=ne;try{return $()}finally{S=re}},t.unstable_pauseExecution=function(){},t.unstable_requestPaint=function(){},t.unstable_runWithPriority=function($,ne){switch($){case 1:case 2:case 3:case 4:case 5:break;default:$=3}var re=S;S=$;try{return ne()}finally{S=re}},t.unstable_scheduleCallback=function($,ne,re){var De=t.unstable_now();switch(typeof re=="object"&&re!==null?(re=re.delay,re=typeof re=="number"&&0<re?De+re:De):re=De,$){case 1:var D=-1;break;case 2:D=250;break;case 5:D=1073741823;break;case 4:D=1e4;break;default:D=5e3}return D=re+D,$={id:v++,callback:ne,priorityLevel:$,startTime:re,expirationTime:D,sortIndex:-1},re>De?($.sortIndex=re,a(y,$),s(p)===null&&$===s(y)&&(C?(z(X),X=-1):C=!0,ze(W,re-De))):($.sortIndex=D,a(p,$),N||E||(N=!0,Ae())),$},t.unstable_shouldYield=Y,t.unstable_wrapCallback=function($){var ne=S;return function(){var re=S;S=ne;try{return $.apply(this,arguments)}finally{S=re}}}}(jc)),jc}var c0;function DS(){return c0||(c0=1,Cc.exports=AS()),Cc.exports}var Ac={exports:{}},Nt={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var f0;function _S(){if(f0)return Nt;f0=1;var t=ro();function a(p){var y="https://react.dev/errors/"+p;if(1<arguments.length){y+="?args[]="+encodeURIComponent(arguments[1]);for(var v=2;v<arguments.length;v++)y+="&args[]="+encodeURIComponent(arguments[v])}return"Minified React error #"+p+"; visit "+y+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function s(){}var r={d:{f:s,r:function(){throw Error(a(522))},D:s,C:s,L:s,m:s,X:s,S:s,M:s},p:0,findDOMNode:null},o=Symbol.for("react.portal");function c(p,y,v){var b=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:o,key:b==null?null:""+b,children:p,containerInfo:y,implementation:v}}var d=t.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function h(p,y){if(p==="font")return"";if(typeof y=="string")return y==="use-credentials"?y:""}return Nt.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=r,Nt.createPortal=function(p,y){var v=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!y||y.nodeType!==1&&y.nodeType!==9&&y.nodeType!==11)throw Error(a(299));return c(p,y,null,v)},Nt.flushSync=function(p){var y=d.T,v=r.p;try{if(d.T=null,r.p=2,p)return p()}finally{d.T=y,r.p=v,r.d.f()}},Nt.preconnect=function(p,y){typeof p=="string"&&(y?(y=y.crossOrigin,y=typeof y=="string"?y==="use-credentials"?y:"":void 0):y=null,r.d.C(p,y))},Nt.prefetchDNS=function(p){typeof p=="string"&&r.d.D(p)},Nt.preinit=function(p,y){if(typeof p=="string"&&y&&typeof y.as=="string"){var v=y.as,b=h(v,y.crossOrigin),S=typeof y.integrity=="string"?y.integrity:void 0,E=typeof y.fetchPriority=="string"?y.fetchPriority:void 0;v==="style"?r.d.S(p,typeof y.precedence=="string"?y.precedence:void 0,{crossOrigin:b,integrity:S,fetchPriority:E}):v==="script"&&r.d.X(p,{crossOrigin:b,integrity:S,fetchPriority:E,nonce:typeof y.nonce=="string"?y.nonce:void 0})}},Nt.preinitModule=function(p,y){if(typeof p=="string")if(typeof y=="object"&&y!==null){if(y.as==null||y.as==="script"){var v=h(y.as,y.crossOrigin);r.d.M(p,{crossOrigin:v,integrity:typeof y.integrity=="string"?y.integrity:void 0,nonce:typeof y.nonce=="string"?y.nonce:void 0})}}else y==null&&r.d.M(p)},Nt.preload=function(p,y){if(typeof p=="string"&&typeof y=="object"&&y!==null&&typeof y.as=="string"){var v=y.as,b=h(v,y.crossOrigin);r.d.L(p,v,{crossOrigin:b,integrity:typeof y.integrity=="string"?y.integrity:void 0,nonce:typeof y.nonce=="string"?y.nonce:void 0,type:typeof y.type=="string"?y.type:void 0,fetchPriority:typeof y.fetchPriority=="string"?y.fetchPriority:void 0,referrerPolicy:typeof y.referrerPolicy=="string"?y.referrerPolicy:void 0,imageSrcSet:typeof y.imageSrcSet=="string"?y.imageSrcSet:void 0,imageSizes:typeof y.imageSizes=="string"?y.imageSizes:void 0,media:typeof y.media=="string"?y.media:void 0})}},Nt.preloadModule=function(p,y){if(typeof p=="string")if(y){var v=h(y.as,y.crossOrigin);r.d.m(p,{as:typeof y.as=="string"&&y.as!=="script"?y.as:void 0,crossOrigin:v,integrity:typeof y.integrity=="string"?y.integrity:void 0})}else r.d.m(p)},Nt.requestFormReset=function(p){r.d.r(p)},Nt.unstable_batchedUpdates=function(p,y){return p(y)},Nt.useFormState=function(p,y,v){return d.H.useFormState(p,y,v)},Nt.useFormStatus=function(){return d.H.useHostTransitionStatus()},Nt.version="19.0.0",Nt}var d0;function h0(){if(d0)return Ac.exports;d0=1;function t(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(t)}catch(a){console.error(a)}}return t(),Ac.exports=_S(),Ac.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var m0;function MS(){if(m0)return Dr;m0=1;var t=DS(),a=ro(),s=h0();function r(e){var n="https://react.dev/errors/"+e;if(1<arguments.length){n+="?args[]="+encodeURIComponent(arguments[1]);for(var i=2;i<arguments.length;i++)n+="&args[]="+encodeURIComponent(arguments[i])}return"Minified React error #"+e+"; visit "+n+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function o(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}var c=Symbol.for("react.element"),d=Symbol.for("react.transitional.element"),h=Symbol.for("react.portal"),p=Symbol.for("react.fragment"),y=Symbol.for("react.strict_mode"),v=Symbol.for("react.profiler"),b=Symbol.for("react.provider"),S=Symbol.for("react.consumer"),E=Symbol.for("react.context"),N=Symbol.for("react.forward_ref"),C=Symbol.for("react.suspense"),R=Symbol.for("react.suspense_list"),z=Symbol.for("react.memo"),B=Symbol.for("react.lazy"),H=Symbol.for("react.offscreen"),W=Symbol.for("react.memo_cache_sentinel"),O=Symbol.iterator;function X(e){return e===null||typeof e!="object"?null:(e=O&&e[O]||e["@@iterator"],typeof e=="function"?e:null)}var J=Symbol.for("react.client.reference");function Z(e){if(e==null)return null;if(typeof e=="function")return e.$$typeof===J?null:e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case p:return"Fragment";case h:return"Portal";case v:return"Profiler";case y:return"StrictMode";case C:return"Suspense";case R:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case E:return(e.displayName||"Context")+".Provider";case S:return(e._context.displayName||"Context")+".Consumer";case N:var n=e.render;return e=e.displayName,e||(e=n.displayName||n.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case z:return n=e.displayName||null,n!==null?n:Z(e.type)||"Memo";case B:n=e._payload,e=e._init;try{return Z(e(n))}catch{}}return null}var Y=a.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,te=Object.assign,je,le;function ye(e){if(je===void 0)try{throw Error()}catch(i){var n=i.stack.trim().match(/\n( *(at )?)/);je=n&&n[1]||"",le=-1<i.stack.indexOf(`
    at`)?" (<anonymous>)":-1<i.stack.indexOf("@")?"@unknown:0:0":""}return`
`+je+e+le}var Ae=!1;function ze(e,n){if(!e||Ae)return"";Ae=!0;var i=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var l={DetermineComponentFrameRoot:function(){try{if(n){var Q=function(){throw Error()};if(Object.defineProperty(Q.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(Q,[])}catch(q){var U=q}Reflect.construct(e,[],Q)}else{try{Q.call()}catch(q){U=q}e.call(Q.prototype)}}else{try{throw Error()}catch(q){U=q}(Q=e())&&typeof Q.catch=="function"&&Q.catch(function(){})}}catch(q){if(q&&U&&typeof q.stack=="string")return[q.stack,U.stack]}return[null,null]}};l.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var u=Object.getOwnPropertyDescriptor(l.DetermineComponentFrameRoot,"name");u&&u.configurable&&Object.defineProperty(l.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var f=l.DetermineComponentFrameRoot(),g=f[0],x=f[1];if(g&&x){var T=g.split(`
`),M=x.split(`
`);for(u=l=0;l<T.length&&!T[l].includes("DetermineComponentFrameRoot");)l++;for(;u<M.length&&!M[u].includes("DetermineComponentFrameRoot");)u++;if(l===T.length||u===M.length)for(l=T.length-1,u=M.length-1;1<=l&&0<=u&&T[l]!==M[u];)u--;for(;1<=l&&0<=u;l--,u--)if(T[l]!==M[u]){if(l!==1||u!==1)do if(l--,u--,0>u||T[l]!==M[u]){var F=`
`+T[l].replace(" at new "," at ");return e.displayName&&F.includes("<anonymous>")&&(F=F.replace("<anonymous>",e.displayName)),F}while(1<=l&&0<=u);break}}}finally{Ae=!1,Error.prepareStackTrace=i}return(i=e?e.displayName||e.name:"")?ye(i):""}function $(e){switch(e.tag){case 26:case 27:case 5:return ye(e.type);case 16:return ye("Lazy");case 13:return ye("Suspense");case 19:return ye("SuspenseList");case 0:case 15:return e=ze(e.type,!1),e;case 11:return e=ze(e.type.render,!1),e;case 1:return e=ze(e.type,!0),e;default:return""}}function ne(e){try{var n="";do n+=$(e),e=e.return;while(e);return n}catch(i){return`
Error generating stack: `+i.message+`
`+i.stack}}function re(e){var n=e,i=e;if(e.alternate)for(;n.return;)n=n.return;else{e=n;do n=e,(n.flags&4098)!==0&&(i=n.return),e=n.return;while(e)}return n.tag===3?i:null}function De(e){if(e.tag===13){var n=e.memoizedState;if(n===null&&(e=e.alternate,e!==null&&(n=e.memoizedState)),n!==null)return n.dehydrated}return null}function D(e){if(re(e)!==e)throw Error(r(188))}function K(e){var n=e.alternate;if(!n){if(n=re(e),n===null)throw Error(r(188));return n!==e?null:e}for(var i=e,l=n;;){var u=i.return;if(u===null)break;var f=u.alternate;if(f===null){if(l=u.return,l!==null){i=l;continue}break}if(u.child===f.child){for(f=u.child;f;){if(f===i)return D(u),e;if(f===l)return D(u),n;f=f.sibling}throw Error(r(188))}if(i.return!==l.return)i=u,l=f;else{for(var g=!1,x=u.child;x;){if(x===i){g=!0,i=u,l=f;break}if(x===l){g=!0,l=u,i=f;break}x=x.sibling}if(!g){for(x=f.child;x;){if(x===i){g=!0,i=f,l=u;break}if(x===l){g=!0,l=f,i=u;break}x=x.sibling}if(!g)throw Error(r(189))}}if(i.alternate!==l)throw Error(r(190))}if(i.tag!==3)throw Error(r(188));return i.stateNode.current===i?e:n}function de(e){var n=e.tag;if(n===5||n===26||n===27||n===6)return e;for(e=e.child;e!==null;){if(n=de(e),n!==null)return n;e=e.sibling}return null}var he=Array.isArray,ie=s.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,_e={pending:!1,data:null,method:null,action:null},be=[],Me=-1;function Se(e){return{current:e}}function Fe(e){0>Me||(e.current=be[Me],be[Me]=null,Me--)}function Ge(e,n){Me++,be[Me]=e.current,e.current=n}var Jt=Se(null),Bi=Se(null),Nn=Se(null),Hi=Se(null);function sl(e,n){switch(Ge(Nn,n),Ge(Bi,e),Ge(Jt,null),e=n.nodeType,e){case 9:case 11:n=(n=n.documentElement)&&(n=n.namespaceURI)?x2(n):0;break;default:if(e=e===8?n.parentNode:n,n=e.tagName,e=e.namespaceURI)e=x2(e),n=S2(e,n);else switch(n){case"svg":n=1;break;case"math":n=2;break;default:n=0}}Fe(Jt),Ge(Jt,n)}function Pi(){Fe(Jt),Fe(Bi),Fe(Nn)}function Jo(e){e.memoizedState!==null&&Ge(Hi,e);var n=Jt.current,i=S2(n,e.type);n!==i&&(Ge(Bi,e),Ge(Jt,i))}function A(e){Bi.current===e&&(Fe(Jt),Fe(Bi)),Hi.current===e&&(Fe(Hi),$l._currentValue=_e)}var L=Object.prototype.hasOwnProperty,P=t.unstable_scheduleCallback,ae=t.unstable_cancelCallback,ee=t.unstable_shouldYield,I=t.unstable_requestPaint,ce=t.unstable_now,Ee=t.unstable_getCurrentPriorityLevel,ct=t.unstable_ImmediatePriority,ft=t.unstable_UserBlockingPriority,It=t.unstable_NormalPriority,xd=t.unstable_LowPriority,Ps=t.unstable_IdlePriority,rl=t.log,Sd=t.unstable_setDisableYieldValue,Qa=null,qt=null;function Io(e){if(qt&&typeof qt.onCommitFiberRoot=="function")try{qt.onCommitFiberRoot(Qa,e,void 0,(e.current.flags&128)===128)}catch{}}function Xa(e){if(typeof rl=="function"&&Sd(e),qt&&typeof qt.setStrictMode=="function")try{qt.setStrictMode(Qa,e)}catch{}}var en=Math.clz32?Math.clz32:R8,M8=Math.log,N8=Math.LN2;function R8(e){return e>>>=0,e===0?32:31-(M8(e)/N8|0)|0}var eu=128,tu=4194304;function qi(e){var n=e&42;if(n!==0)return n;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194176;case 4194304:case 8388608:case 16777216:case 33554432:return e&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function nu(e,n){var i=e.pendingLanes;if(i===0)return 0;var l=0,u=e.suspendedLanes,f=e.pingedLanes,g=e.warmLanes;e=e.finishedLanes!==0;var x=i&134217727;return x!==0?(i=x&~u,i!==0?l=qi(i):(f&=x,f!==0?l=qi(f):e||(g=x&~g,g!==0&&(l=qi(g))))):(x=i&~u,x!==0?l=qi(x):f!==0?l=qi(f):e||(g=i&~g,g!==0&&(l=qi(g)))),l===0?0:n!==0&&n!==l&&(n&u)===0&&(u=l&-l,g=n&-n,u>=g||u===32&&(g&4194176)!==0)?n:l}function ll(e,n){return(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&n)===0}function O8(e,n){switch(e){case 1:case 2:case 4:case 8:return n+250;case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return n+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function d1(){var e=eu;return eu<<=1,(eu&4194176)===0&&(eu=128),e}function h1(){var e=tu;return tu<<=1,(tu&62914560)===0&&(tu=4194304),e}function wd(e){for(var n=[],i=0;31>i;i++)n.push(e);return n}function ol(e,n){e.pendingLanes|=n,n!==268435456&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function V8(e,n,i,l,u,f){var g=e.pendingLanes;e.pendingLanes=i,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=i,e.entangledLanes&=i,e.errorRecoveryDisabledLanes&=i,e.shellSuspendCounter=0;var x=e.entanglements,T=e.expirationTimes,M=e.hiddenUpdates;for(i=g&~i;0<i;){var F=31-en(i),Q=1<<F;x[F]=0,T[F]=-1;var U=M[F];if(U!==null)for(M[F]=null,F=0;F<U.length;F++){var q=U[F];q!==null&&(q.lane&=-536870913)}i&=~Q}l!==0&&m1(e,l,0),f!==0&&u===0&&e.tag!==0&&(e.suspendedLanes|=f&~(g&~n))}function m1(e,n,i){e.pendingLanes|=n,e.suspendedLanes&=~n;var l=31-en(n);e.entangledLanes|=n,e.entanglements[l]=e.entanglements[l]|1073741824|i&4194218}function p1(e,n){var i=e.entangledLanes|=n;for(e=e.entanglements;i;){var l=31-en(i),u=1<<l;u&n|e[l]&n&&(e[l]|=n),i&=~u}}function y1(e){return e&=-e,2<e?8<e?(e&134217727)!==0?32:268435456:8:2}function g1(){var e=ie.p;return e!==0?e:(e=window.event,e===void 0?32:B2(e.type))}function z8(e,n){var i=ie.p;try{return ie.p=e,n()}finally{ie.p=i}}var Ka=Math.random().toString(36).slice(2),zt="__reactFiber$"+Ka,Kt="__reactProps$"+Ka,qs="__reactContainer$"+Ka,Ed="__reactEvents$"+Ka,k8="__reactListeners$"+Ka,L8="__reactHandles$"+Ka,v1="__reactResources$"+Ka,ul="__reactMarker$"+Ka;function Td(e){delete e[zt],delete e[Kt],delete e[Ed],delete e[k8],delete e[L8]}function Fi(e){var n=e[zt];if(n)return n;for(var i=e.parentNode;i;){if(n=i[qs]||i[zt]){if(i=n.alternate,n.child!==null||i!==null&&i.child!==null)for(e=T2(e);e!==null;){if(i=e[zt])return i;e=T2(e)}return n}e=i,i=e.parentNode}return null}function Fs(e){if(e=e[zt]||e[qs]){var n=e.tag;if(n===5||n===6||n===13||n===26||n===27||n===3)return e}return null}function cl(e){var n=e.tag;if(n===5||n===26||n===27||n===6)return e.stateNode;throw Error(r(33))}function Gs(e){var n=e[v1];return n||(n=e[v1]={hoistableStyles:new Map,hoistableScripts:new Map}),n}function Et(e){e[ul]=!0}var b1=new Set,x1={};function Gi(e,n){Ys(e,n),Ys(e+"Capture",n)}function Ys(e,n){for(x1[e]=n,e=0;e<n.length;e++)b1.add(n[e])}var la=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),U8=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),S1={},w1={};function B8(e){return L.call(w1,e)?!0:L.call(S1,e)?!1:U8.test(e)?w1[e]=!0:(S1[e]=!0,!1)}function au(e,n,i){if(B8(n))if(i===null)e.removeAttribute(n);else{switch(typeof i){case"undefined":case"function":case"symbol":e.removeAttribute(n);return;case"boolean":var l=n.toLowerCase().slice(0,5);if(l!=="data-"&&l!=="aria-"){e.removeAttribute(n);return}}e.setAttribute(n,""+i)}}function iu(e,n,i){if(i===null)e.removeAttribute(n);else{switch(typeof i){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(n);return}e.setAttribute(n,""+i)}}function oa(e,n,i,l){if(l===null)e.removeAttribute(i);else{switch(typeof l){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(i);return}e.setAttributeNS(n,i,""+l)}}function hn(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function E1(e){var n=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(n==="checkbox"||n==="radio")}function H8(e){var n=E1(e)?"checked":"value",i=Object.getOwnPropertyDescriptor(e.constructor.prototype,n),l=""+e[n];if(!e.hasOwnProperty(n)&&typeof i<"u"&&typeof i.get=="function"&&typeof i.set=="function"){var u=i.get,f=i.set;return Object.defineProperty(e,n,{configurable:!0,get:function(){return u.call(this)},set:function(g){l=""+g,f.call(this,g)}}),Object.defineProperty(e,n,{enumerable:i.enumerable}),{getValue:function(){return l},setValue:function(g){l=""+g},stopTracking:function(){e._valueTracker=null,delete e[n]}}}}function su(e){e._valueTracker||(e._valueTracker=H8(e))}function T1(e){if(!e)return!1;var n=e._valueTracker;if(!n)return!0;var i=n.getValue(),l="";return e&&(l=E1(e)?e.checked?"true":"false":e.value),e=l,e!==i?(n.setValue(e),!0):!1}function ru(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}var P8=/[\n"\\]/g;function mn(e){return e.replace(P8,function(n){return"\\"+n.charCodeAt(0).toString(16)+" "})}function Cd(e,n,i,l,u,f,g,x){e.name="",g!=null&&typeof g!="function"&&typeof g!="symbol"&&typeof g!="boolean"?e.type=g:e.removeAttribute("type"),n!=null?g==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+hn(n)):e.value!==""+hn(n)&&(e.value=""+hn(n)):g!=="submit"&&g!=="reset"||e.removeAttribute("value"),n!=null?jd(e,g,hn(n)):i!=null?jd(e,g,hn(i)):l!=null&&e.removeAttribute("value"),u==null&&f!=null&&(e.defaultChecked=!!f),u!=null&&(e.checked=u&&typeof u!="function"&&typeof u!="symbol"),x!=null&&typeof x!="function"&&typeof x!="symbol"&&typeof x!="boolean"?e.name=""+hn(x):e.removeAttribute("name")}function C1(e,n,i,l,u,f,g,x){if(f!=null&&typeof f!="function"&&typeof f!="symbol"&&typeof f!="boolean"&&(e.type=f),n!=null||i!=null){if(!(f!=="submit"&&f!=="reset"||n!=null))return;i=i!=null?""+hn(i):"",n=n!=null?""+hn(n):i,x||n===e.value||(e.value=n),e.defaultValue=n}l=l??u,l=typeof l!="function"&&typeof l!="symbol"&&!!l,e.checked=x?e.checked:!!l,e.defaultChecked=!!l,g!=null&&typeof g!="function"&&typeof g!="symbol"&&typeof g!="boolean"&&(e.name=g)}function jd(e,n,i){n==="number"&&ru(e.ownerDocument)===e||e.defaultValue===""+i||(e.defaultValue=""+i)}function Qs(e,n,i,l){if(e=e.options,n){n={};for(var u=0;u<i.length;u++)n["$"+i[u]]=!0;for(i=0;i<e.length;i++)u=n.hasOwnProperty("$"+e[i].value),e[i].selected!==u&&(e[i].selected=u),u&&l&&(e[i].defaultSelected=!0)}else{for(i=""+hn(i),n=null,u=0;u<e.length;u++){if(e[u].value===i){e[u].selected=!0,l&&(e[u].defaultSelected=!0);return}n!==null||e[u].disabled||(n=e[u])}n!==null&&(n.selected=!0)}}function j1(e,n,i){if(n!=null&&(n=""+hn(n),n!==e.value&&(e.value=n),i==null)){e.defaultValue!==n&&(e.defaultValue=n);return}e.defaultValue=i!=null?""+hn(i):""}function A1(e,n,i,l){if(n==null){if(l!=null){if(i!=null)throw Error(r(92));if(he(l)){if(1<l.length)throw Error(r(93));l=l[0]}i=l}i==null&&(i=""),n=i}i=hn(n),e.defaultValue=i,l=e.textContent,l===i&&l!==""&&l!==null&&(e.value=l)}function Xs(e,n){if(n){var i=e.firstChild;if(i&&i===e.lastChild&&i.nodeType===3){i.nodeValue=n;return}}e.textContent=n}var q8=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function D1(e,n,i){var l=n.indexOf("--")===0;i==null||typeof i=="boolean"||i===""?l?e.setProperty(n,""):n==="float"?e.cssFloat="":e[n]="":l?e.setProperty(n,i):typeof i!="number"||i===0||q8.has(n)?n==="float"?e.cssFloat=i:e[n]=(""+i).trim():e[n]=i+"px"}function _1(e,n,i){if(n!=null&&typeof n!="object")throw Error(r(62));if(e=e.style,i!=null){for(var l in i)!i.hasOwnProperty(l)||n!=null&&n.hasOwnProperty(l)||(l.indexOf("--")===0?e.setProperty(l,""):l==="float"?e.cssFloat="":e[l]="");for(var u in n)l=n[u],n.hasOwnProperty(u)&&i[u]!==l&&D1(e,u,l)}else for(var f in n)n.hasOwnProperty(f)&&D1(e,f,n[f])}function Ad(e){if(e.indexOf("-")===-1)return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var F8=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),G8=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function lu(e){return G8.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}var Dd=null;function _d(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Ks=null,Zs=null;function M1(e){var n=Fs(e);if(n&&(e=n.stateNode)){var i=e[Kt]||null;e:switch(e=n.stateNode,n.type){case"input":if(Cd(e,i.value,i.defaultValue,i.defaultValue,i.checked,i.defaultChecked,i.type,i.name),n=i.name,i.type==="radio"&&n!=null){for(i=e;i.parentNode;)i=i.parentNode;for(i=i.querySelectorAll('input[name="'+mn(""+n)+'"][type="radio"]'),n=0;n<i.length;n++){var l=i[n];if(l!==e&&l.form===e.form){var u=l[Kt]||null;if(!u)throw Error(r(90));Cd(l,u.value,u.defaultValue,u.defaultValue,u.checked,u.defaultChecked,u.type,u.name)}}for(n=0;n<i.length;n++)l=i[n],l.form===e.form&&T1(l)}break e;case"textarea":j1(e,i.value,i.defaultValue);break e;case"select":n=i.value,n!=null&&Qs(e,!!i.multiple,n,!1)}}}var Md=!1;function N1(e,n,i){if(Md)return e(n,i);Md=!0;try{var l=e(n);return l}finally{if(Md=!1,(Ks!==null||Zs!==null)&&(Gu(),Ks&&(n=Ks,e=Zs,Zs=Ks=null,M1(n),e)))for(n=0;n<e.length;n++)M1(e[n])}}function fl(e,n){var i=e.stateNode;if(i===null)return null;var l=i[Kt]||null;if(l===null)return null;i=l[n];e:switch(n){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(l=!l.disabled)||(e=e.type,l=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!l;break e;default:e=!1}if(e)return null;if(i&&typeof i!="function")throw Error(r(231,n,typeof i));return i}var Nd=!1;if(la)try{var dl={};Object.defineProperty(dl,"passive",{get:function(){Nd=!0}}),window.addEventListener("test",dl,dl),window.removeEventListener("test",dl,dl)}catch{Nd=!1}var Za=null,Rd=null,ou=null;function R1(){if(ou)return ou;var e,n=Rd,i=n.length,l,u="value"in Za?Za.value:Za.textContent,f=u.length;for(e=0;e<i&&n[e]===u[e];e++);var g=i-e;for(l=1;l<=g&&n[i-l]===u[f-l];l++);return ou=u.slice(e,1<l?1-l:void 0)}function uu(e){var n=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&n===13&&(e=13)):e=n,e===10&&(e=13),32<=e||e===13?e:0}function cu(){return!0}function O1(){return!1}function Zt(e){function n(i,l,u,f,g){this._reactName=i,this._targetInst=u,this.type=l,this.nativeEvent=f,this.target=g,this.currentTarget=null;for(var x in e)e.hasOwnProperty(x)&&(i=e[x],this[x]=i?i(f):f[x]);return this.isDefaultPrevented=(f.defaultPrevented!=null?f.defaultPrevented:f.returnValue===!1)?cu:O1,this.isPropagationStopped=O1,this}return te(n.prototype,{preventDefault:function(){this.defaultPrevented=!0;var i=this.nativeEvent;i&&(i.preventDefault?i.preventDefault():typeof i.returnValue!="unknown"&&(i.returnValue=!1),this.isDefaultPrevented=cu)},stopPropagation:function(){var i=this.nativeEvent;i&&(i.stopPropagation?i.stopPropagation():typeof i.cancelBubble!="unknown"&&(i.cancelBubble=!0),this.isPropagationStopped=cu)},persist:function(){},isPersistent:cu}),n}var Yi={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},fu=Zt(Yi),hl=te({},Yi,{view:0,detail:0}),Y8=Zt(hl),Od,Vd,ml,du=te({},hl,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:kd,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==ml&&(ml&&e.type==="mousemove"?(Od=e.screenX-ml.screenX,Vd=e.screenY-ml.screenY):Vd=Od=0,ml=e),Od)},movementY:function(e){return"movementY"in e?e.movementY:Vd}}),V1=Zt(du),Q8=te({},du,{dataTransfer:0}),X8=Zt(Q8),K8=te({},hl,{relatedTarget:0}),zd=Zt(K8),Z8=te({},Yi,{animationName:0,elapsedTime:0,pseudoElement:0}),W8=Zt(Z8),$8=te({},Yi,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),J8=Zt($8),I8=te({},Yi,{data:0}),z1=Zt(I8),ej={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},tj={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},nj={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function aj(e){var n=this.nativeEvent;return n.getModifierState?n.getModifierState(e):(e=nj[e])?!!n[e]:!1}function kd(){return aj}var ij=te({},hl,{key:function(e){if(e.key){var n=ej[e.key]||e.key;if(n!=="Unidentified")return n}return e.type==="keypress"?(e=uu(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?tj[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:kd,charCode:function(e){return e.type==="keypress"?uu(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?uu(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),sj=Zt(ij),rj=te({},du,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),k1=Zt(rj),lj=te({},hl,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:kd}),oj=Zt(lj),uj=te({},Yi,{propertyName:0,elapsedTime:0,pseudoElement:0}),cj=Zt(uj),fj=te({},du,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),dj=Zt(fj),hj=te({},Yi,{newState:0,oldState:0}),mj=Zt(hj),pj=[9,13,27,32],Ld=la&&"CompositionEvent"in window,pl=null;la&&"documentMode"in document&&(pl=document.documentMode);var yj=la&&"TextEvent"in window&&!pl,L1=la&&(!Ld||pl&&8<pl&&11>=pl),U1=" ",B1=!1;function H1(e,n){switch(e){case"keyup":return pj.indexOf(n.keyCode)!==-1;case"keydown":return n.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function P1(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Ws=!1;function gj(e,n){switch(e){case"compositionend":return P1(n);case"keypress":return n.which!==32?null:(B1=!0,U1);case"textInput":return e=n.data,e===U1&&B1?null:e;default:return null}}function vj(e,n){if(Ws)return e==="compositionend"||!Ld&&H1(e,n)?(e=R1(),ou=Rd=Za=null,Ws=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(n.ctrlKey||n.altKey||n.metaKey)||n.ctrlKey&&n.altKey){if(n.char&&1<n.char.length)return n.char;if(n.which)return String.fromCharCode(n.which)}return null;case"compositionend":return L1&&n.locale!=="ko"?null:n.data;default:return null}}var bj={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function q1(e){var n=e&&e.nodeName&&e.nodeName.toLowerCase();return n==="input"?!!bj[e.type]:n==="textarea"}function F1(e,n,i,l){Ks?Zs?Zs.push(l):Zs=[l]:Ks=l,n=Zu(n,"onChange"),0<n.length&&(i=new fu("onChange","change",null,i,l),e.push({event:i,listeners:n}))}var yl=null,gl=null;function xj(e){p2(e,0)}function hu(e){var n=cl(e);if(T1(n))return e}function G1(e,n){if(e==="change")return n}var Y1=!1;if(la){var Ud;if(la){var Bd="oninput"in document;if(!Bd){var Q1=document.createElement("div");Q1.setAttribute("oninput","return;"),Bd=typeof Q1.oninput=="function"}Ud=Bd}else Ud=!1;Y1=Ud&&(!document.documentMode||9<document.documentMode)}function X1(){yl&&(yl.detachEvent("onpropertychange",K1),gl=yl=null)}function K1(e){if(e.propertyName==="value"&&hu(gl)){var n=[];F1(n,gl,e,_d(e)),N1(xj,n)}}function Sj(e,n,i){e==="focusin"?(X1(),yl=n,gl=i,yl.attachEvent("onpropertychange",K1)):e==="focusout"&&X1()}function wj(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return hu(gl)}function Ej(e,n){if(e==="click")return hu(n)}function Tj(e,n){if(e==="input"||e==="change")return hu(n)}function Cj(e,n){return e===n&&(e!==0||1/e===1/n)||e!==e&&n!==n}var tn=typeof Object.is=="function"?Object.is:Cj;function vl(e,n){if(tn(e,n))return!0;if(typeof e!="object"||e===null||typeof n!="object"||n===null)return!1;var i=Object.keys(e),l=Object.keys(n);if(i.length!==l.length)return!1;for(l=0;l<i.length;l++){var u=i[l];if(!L.call(n,u)||!tn(e[u],n[u]))return!1}return!0}function Z1(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function W1(e,n){var i=Z1(e);e=0;for(var l;i;){if(i.nodeType===3){if(l=e+i.textContent.length,e<=n&&l>=n)return{node:i,offset:n-e};e=l}e:{for(;i;){if(i.nextSibling){i=i.nextSibling;break e}i=i.parentNode}i=void 0}i=Z1(i)}}function $1(e,n){return e&&n?e===n?!0:e&&e.nodeType===3?!1:n&&n.nodeType===3?$1(e,n.parentNode):"contains"in e?e.contains(n):e.compareDocumentPosition?!!(e.compareDocumentPosition(n)&16):!1:!1}function J1(e){e=e!=null&&e.ownerDocument!=null&&e.ownerDocument.defaultView!=null?e.ownerDocument.defaultView:window;for(var n=ru(e.document);n instanceof e.HTMLIFrameElement;){try{var i=typeof n.contentWindow.location.href=="string"}catch{i=!1}if(i)e=n.contentWindow;else break;n=ru(e.document)}return n}function Hd(e){var n=e&&e.nodeName&&e.nodeName.toLowerCase();return n&&(n==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||n==="textarea"||e.contentEditable==="true")}function jj(e,n){var i=J1(n);n=e.focusedElem;var l=e.selectionRange;if(i!==n&&n&&n.ownerDocument&&$1(n.ownerDocument.documentElement,n)){if(l!==null&&Hd(n)){if(e=l.start,i=l.end,i===void 0&&(i=e),"selectionStart"in n)n.selectionStart=e,n.selectionEnd=Math.min(i,n.value.length);else if(i=(e=n.ownerDocument||document)&&e.defaultView||window,i.getSelection){i=i.getSelection();var u=n.textContent.length,f=Math.min(l.start,u);l=l.end===void 0?f:Math.min(l.end,u),!i.extend&&f>l&&(u=l,l=f,f=u),u=W1(n,f);var g=W1(n,l);u&&g&&(i.rangeCount!==1||i.anchorNode!==u.node||i.anchorOffset!==u.offset||i.focusNode!==g.node||i.focusOffset!==g.offset)&&(e=e.createRange(),e.setStart(u.node,u.offset),i.removeAllRanges(),f>l?(i.addRange(e),i.extend(g.node,g.offset)):(e.setEnd(g.node,g.offset),i.addRange(e)))}}for(e=[],i=n;i=i.parentNode;)i.nodeType===1&&e.push({element:i,left:i.scrollLeft,top:i.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<e.length;n++)i=e[n],i.element.scrollLeft=i.left,i.element.scrollTop=i.top}}var Aj=la&&"documentMode"in document&&11>=document.documentMode,$s=null,Pd=null,bl=null,qd=!1;function I1(e,n,i){var l=i.window===i?i.document:i.nodeType===9?i:i.ownerDocument;qd||$s==null||$s!==ru(l)||(l=$s,"selectionStart"in l&&Hd(l)?l={start:l.selectionStart,end:l.selectionEnd}:(l=(l.ownerDocument&&l.ownerDocument.defaultView||window).getSelection(),l={anchorNode:l.anchorNode,anchorOffset:l.anchorOffset,focusNode:l.focusNode,focusOffset:l.focusOffset}),bl&&vl(bl,l)||(bl=l,l=Zu(Pd,"onSelect"),0<l.length&&(n=new fu("onSelect","select",null,n,i),e.push({event:n,listeners:l}),n.target=$s)))}function Qi(e,n){var i={};return i[e.toLowerCase()]=n.toLowerCase(),i["Webkit"+e]="webkit"+n,i["Moz"+e]="moz"+n,i}var Js={animationend:Qi("Animation","AnimationEnd"),animationiteration:Qi("Animation","AnimationIteration"),animationstart:Qi("Animation","AnimationStart"),transitionrun:Qi("Transition","TransitionRun"),transitionstart:Qi("Transition","TransitionStart"),transitioncancel:Qi("Transition","TransitionCancel"),transitionend:Qi("Transition","TransitionEnd")},Fd={},eb={};la&&(eb=document.createElement("div").style,"AnimationEvent"in window||(delete Js.animationend.animation,delete Js.animationiteration.animation,delete Js.animationstart.animation),"TransitionEvent"in window||delete Js.transitionend.transition);function Xi(e){if(Fd[e])return Fd[e];if(!Js[e])return e;var n=Js[e],i;for(i in n)if(n.hasOwnProperty(i)&&i in eb)return Fd[e]=n[i];return e}var tb=Xi("animationend"),nb=Xi("animationiteration"),ab=Xi("animationstart"),Dj=Xi("transitionrun"),_j=Xi("transitionstart"),Mj=Xi("transitioncancel"),ib=Xi("transitionend"),sb=new Map,rb="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll scrollEnd toggle touchMove waiting wheel".split(" ");function Rn(e,n){sb.set(e,n),Gi(n,[e])}var pn=[],Is=0,Gd=0;function mu(){for(var e=Is,n=Gd=Is=0;n<e;){var i=pn[n];pn[n++]=null;var l=pn[n];pn[n++]=null;var u=pn[n];pn[n++]=null;var f=pn[n];if(pn[n++]=null,l!==null&&u!==null){var g=l.pending;g===null?u.next=u:(u.next=g.next,g.next=u),l.pending=u}f!==0&&lb(i,u,f)}}function pu(e,n,i,l){pn[Is++]=e,pn[Is++]=n,pn[Is++]=i,pn[Is++]=l,Gd|=l,e.lanes|=l,e=e.alternate,e!==null&&(e.lanes|=l)}function Yd(e,n,i,l){return pu(e,n,i,l),yu(e)}function Wa(e,n){return pu(e,null,null,n),yu(e)}function lb(e,n,i){e.lanes|=i;var l=e.alternate;l!==null&&(l.lanes|=i);for(var u=!1,f=e.return;f!==null;)f.childLanes|=i,l=f.alternate,l!==null&&(l.childLanes|=i),f.tag===22&&(e=f.stateNode,e===null||e._visibility&1||(u=!0)),e=f,f=f.return;u&&n!==null&&e.tag===3&&(f=e.stateNode,u=31-en(i),f=f.hiddenUpdates,e=f[u],e===null?f[u]=[n]:e.push(n),n.lane=i|536870912)}function yu(e){if(50<Gl)throw Gl=0,$h=null,Error(r(185));for(var n=e.return;n!==null;)e=n,n=e.return;return e.tag===3?e.stateNode:null}var er={},ob=new WeakMap;function yn(e,n){if(typeof e=="object"&&e!==null){var i=ob.get(e);return i!==void 0?i:(n={value:e,source:n,stack:ne(n)},ob.set(e,n),n)}return{value:e,source:n,stack:ne(n)}}var tr=[],nr=0,gu=null,vu=0,gn=[],vn=0,Ki=null,ua=1,ca="";function Zi(e,n){tr[nr++]=vu,tr[nr++]=gu,gu=e,vu=n}function ub(e,n,i){gn[vn++]=ua,gn[vn++]=ca,gn[vn++]=Ki,Ki=e;var l=ua;e=ca;var u=32-en(l)-1;l&=~(1<<u),i+=1;var f=32-en(n)+u;if(30<f){var g=u-u%5;f=(l&(1<<g)-1).toString(32),l>>=g,u-=g,ua=1<<32-en(n)+u|i<<u|l,ca=f+e}else ua=1<<f|i<<u|l,ca=e}function Qd(e){e.return!==null&&(Zi(e,1),ub(e,1,0))}function Xd(e){for(;e===gu;)gu=tr[--nr],tr[nr]=null,vu=tr[--nr],tr[nr]=null;for(;e===Ki;)Ki=gn[--vn],gn[vn]=null,ca=gn[--vn],gn[vn]=null,ua=gn[--vn],gn[vn]=null}var Ft=null,Dt=null,Ue=!1,On=null,Qn=!1,Kd=Error(r(519));function Wi(e){var n=Error(r(418,""));throw wl(yn(n,e)),Kd}function cb(e){var n=e.stateNode,i=e.type,l=e.memoizedProps;switch(n[zt]=e,n[Kt]=l,i){case"dialog":ke("cancel",n),ke("close",n);break;case"iframe":case"object":case"embed":ke("load",n);break;case"video":case"audio":for(i=0;i<Ql.length;i++)ke(Ql[i],n);break;case"source":ke("error",n);break;case"img":case"image":case"link":ke("error",n),ke("load",n);break;case"details":ke("toggle",n);break;case"input":ke("invalid",n),C1(n,l.value,l.defaultValue,l.checked,l.defaultChecked,l.type,l.name,!0),su(n);break;case"select":ke("invalid",n);break;case"textarea":ke("invalid",n),A1(n,l.value,l.defaultValue,l.children),su(n)}i=l.children,typeof i!="string"&&typeof i!="number"&&typeof i!="bigint"||n.textContent===""+i||l.suppressHydrationWarning===!0||b2(n.textContent,i)?(l.popover!=null&&(ke("beforetoggle",n),ke("toggle",n)),l.onScroll!=null&&ke("scroll",n),l.onScrollEnd!=null&&ke("scrollend",n),l.onClick!=null&&(n.onclick=Wu),n=!0):n=!1,n||Wi(e)}function fb(e){for(Ft=e.return;Ft;)switch(Ft.tag){case 3:case 27:Qn=!0;return;case 5:case 13:Qn=!1;return;default:Ft=Ft.return}}function xl(e){if(e!==Ft)return!1;if(!Ue)return fb(e),Ue=!0,!1;var n=!1,i;if((i=e.tag!==3&&e.tag!==27)&&((i=e.tag===5)&&(i=e.type,i=!(i!=="form"&&i!=="button")||mm(e.type,e.memoizedProps)),i=!i),i&&(n=!0),n&&Dt&&Wi(e),fb(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(r(317));e:{for(e=e.nextSibling,n=0;e;){if(e.nodeType===8)if(i=e.data,i==="/$"){if(n===0){Dt=zn(e.nextSibling);break e}n--}else i!=="$"&&i!=="$!"&&i!=="$?"||n++;e=e.nextSibling}Dt=null}}else Dt=Ft?zn(e.stateNode.nextSibling):null;return!0}function Sl(){Dt=Ft=null,Ue=!1}function wl(e){On===null?On=[e]:On.push(e)}var El=Error(r(460)),db=Error(r(474)),Zd={then:function(){}};function hb(e){return e=e.status,e==="fulfilled"||e==="rejected"}function bu(){}function mb(e,n,i){switch(i=e[i],i===void 0?e.push(n):i!==n&&(n.then(bu,bu),n=i),n.status){case"fulfilled":return n.value;case"rejected":throw e=n.reason,e===El?Error(r(483)):e;default:if(typeof n.status=="string")n.then(bu,bu);else{if(e=Ze,e!==null&&100<e.shellSuspendCounter)throw Error(r(482));e=n,e.status="pending",e.then(function(l){if(n.status==="pending"){var u=n;u.status="fulfilled",u.value=l}},function(l){if(n.status==="pending"){var u=n;u.status="rejected",u.reason=l}})}switch(n.status){case"fulfilled":return n.value;case"rejected":throw e=n.reason,e===El?Error(r(483)):e}throw Tl=n,El}}var Tl=null;function pb(){if(Tl===null)throw Error(r(459));var e=Tl;return Tl=null,e}var ar=null,Cl=0;function xu(e){var n=Cl;return Cl+=1,ar===null&&(ar=[]),mb(ar,e,n)}function jl(e,n){n=n.props.ref,e.ref=n!==void 0?n:null}function Su(e,n){throw n.$$typeof===c?Error(r(525)):(e=Object.prototype.toString.call(n),Error(r(31,e==="[object Object]"?"object with keys {"+Object.keys(n).join(", ")+"}":e)))}function yb(e){var n=e._init;return n(e._payload)}function gb(e){function n(V,_){if(e){var k=V.deletions;k===null?(V.deletions=[_],V.flags|=16):k.push(_)}}function i(V,_){if(!e)return null;for(;_!==null;)n(V,_),_=_.sibling;return null}function l(V){for(var _=new Map;V!==null;)V.key!==null?_.set(V.key,V):_.set(V.index,V),V=V.sibling;return _}function u(V,_){return V=oi(V,_),V.index=0,V.sibling=null,V}function f(V,_,k){return V.index=k,e?(k=V.alternate,k!==null?(k=k.index,k<_?(V.flags|=33554434,_):k):(V.flags|=33554434,_)):(V.flags|=1048576,_)}function g(V){return e&&V.alternate===null&&(V.flags|=33554434),V}function x(V,_,k,G){return _===null||_.tag!==6?(_=Fh(k,V.mode,G),_.return=V,_):(_=u(_,k),_.return=V,_)}function T(V,_,k,G){var ue=k.type;return ue===p?F(V,_,k.props.children,G,k.key):_!==null&&(_.elementType===ue||typeof ue=="object"&&ue!==null&&ue.$$typeof===B&&yb(ue)===_.type)?(_=u(_,k.props),jl(_,k),_.return=V,_):(_=Bu(k.type,k.key,k.props,null,V.mode,G),jl(_,k),_.return=V,_)}function M(V,_,k,G){return _===null||_.tag!==4||_.stateNode.containerInfo!==k.containerInfo||_.stateNode.implementation!==k.implementation?(_=Gh(k,V.mode,G),_.return=V,_):(_=u(_,k.children||[]),_.return=V,_)}function F(V,_,k,G,ue){return _===null||_.tag!==7?(_=rs(k,V.mode,G,ue),_.return=V,_):(_=u(_,k),_.return=V,_)}function Q(V,_,k){if(typeof _=="string"&&_!==""||typeof _=="number"||typeof _=="bigint")return _=Fh(""+_,V.mode,k),_.return=V,_;if(typeof _=="object"&&_!==null){switch(_.$$typeof){case d:return k=Bu(_.type,_.key,_.props,null,V.mode,k),jl(k,_),k.return=V,k;case h:return _=Gh(_,V.mode,k),_.return=V,_;case B:var G=_._init;return _=G(_._payload),Q(V,_,k)}if(he(_)||X(_))return _=rs(_,V.mode,k,null),_.return=V,_;if(typeof _.then=="function")return Q(V,xu(_),k);if(_.$$typeof===E)return Q(V,ku(V,_),k);Su(V,_)}return null}function U(V,_,k,G){var ue=_!==null?_.key:null;if(typeof k=="string"&&k!==""||typeof k=="number"||typeof k=="bigint")return ue!==null?null:x(V,_,""+k,G);if(typeof k=="object"&&k!==null){switch(k.$$typeof){case d:return k.key===ue?T(V,_,k,G):null;case h:return k.key===ue?M(V,_,k,G):null;case B:return ue=k._init,k=ue(k._payload),U(V,_,k,G)}if(he(k)||X(k))return ue!==null?null:F(V,_,k,G,null);if(typeof k.then=="function")return U(V,_,xu(k),G);if(k.$$typeof===E)return U(V,_,ku(V,k),G);Su(V,k)}return null}function q(V,_,k,G,ue){if(typeof G=="string"&&G!==""||typeof G=="number"||typeof G=="bigint")return V=V.get(k)||null,x(_,V,""+G,ue);if(typeof G=="object"&&G!==null){switch(G.$$typeof){case d:return V=V.get(G.key===null?k:G.key)||null,T(_,V,G,ue);case h:return V=V.get(G.key===null?k:G.key)||null,M(_,V,G,ue);case B:var Ne=G._init;return G=Ne(G._payload),q(V,_,k,G,ue)}if(he(G)||X(G))return V=V.get(k)||null,F(_,V,G,ue,null);if(typeof G.then=="function")return q(V,_,k,xu(G),ue);if(G.$$typeof===E)return q(V,_,k,ku(_,G),ue);Su(_,G)}return null}function fe(V,_,k,G){for(var ue=null,Ne=null,me=_,ve=_=0,jt=null;me!==null&&ve<k.length;ve++){me.index>ve?(jt=me,me=null):jt=me.sibling;var Be=U(V,me,k[ve],G);if(Be===null){me===null&&(me=jt);break}e&&me&&Be.alternate===null&&n(V,me),_=f(Be,_,ve),Ne===null?ue=Be:Ne.sibling=Be,Ne=Be,me=jt}if(ve===k.length)return i(V,me),Ue&&Zi(V,ve),ue;if(me===null){for(;ve<k.length;ve++)me=Q(V,k[ve],G),me!==null&&(_=f(me,_,ve),Ne===null?ue=me:Ne.sibling=me,Ne=me);return Ue&&Zi(V,ve),ue}for(me=l(me);ve<k.length;ve++)jt=q(me,V,ve,k[ve],G),jt!==null&&(e&&jt.alternate!==null&&me.delete(jt.key===null?ve:jt.key),_=f(jt,_,ve),Ne===null?ue=jt:Ne.sibling=jt,Ne=jt);return e&&me.forEach(function(pi){return n(V,pi)}),Ue&&Zi(V,ve),ue}function xe(V,_,k,G){if(k==null)throw Error(r(151));for(var ue=null,Ne=null,me=_,ve=_=0,jt=null,Be=k.next();me!==null&&!Be.done;ve++,Be=k.next()){me.index>ve?(jt=me,me=null):jt=me.sibling;var pi=U(V,me,Be.value,G);if(pi===null){me===null&&(me=jt);break}e&&me&&pi.alternate===null&&n(V,me),_=f(pi,_,ve),Ne===null?ue=pi:Ne.sibling=pi,Ne=pi,me=jt}if(Be.done)return i(V,me),Ue&&Zi(V,ve),ue;if(me===null){for(;!Be.done;ve++,Be=k.next())Be=Q(V,Be.value,G),Be!==null&&(_=f(Be,_,ve),Ne===null?ue=Be:Ne.sibling=Be,Ne=Be);return Ue&&Zi(V,ve),ue}for(me=l(me);!Be.done;ve++,Be=k.next())Be=q(me,V,ve,Be.value,G),Be!==null&&(e&&Be.alternate!==null&&me.delete(Be.key===null?ve:Be.key),_=f(Be,_,ve),Ne===null?ue=Be:Ne.sibling=Be,Ne=Be);return e&&me.forEach(function(G9){return n(V,G9)}),Ue&&Zi(V,ve),ue}function rt(V,_,k,G){if(typeof k=="object"&&k!==null&&k.type===p&&k.key===null&&(k=k.props.children),typeof k=="object"&&k!==null){switch(k.$$typeof){case d:e:{for(var ue=k.key;_!==null;){if(_.key===ue){if(ue=k.type,ue===p){if(_.tag===7){i(V,_.sibling),G=u(_,k.props.children),G.return=V,V=G;break e}}else if(_.elementType===ue||typeof ue=="object"&&ue!==null&&ue.$$typeof===B&&yb(ue)===_.type){i(V,_.sibling),G=u(_,k.props),jl(G,k),G.return=V,V=G;break e}i(V,_);break}else n(V,_);_=_.sibling}k.type===p?(G=rs(k.props.children,V.mode,G,k.key),G.return=V,V=G):(G=Bu(k.type,k.key,k.props,null,V.mode,G),jl(G,k),G.return=V,V=G)}return g(V);case h:e:{for(ue=k.key;_!==null;){if(_.key===ue)if(_.tag===4&&_.stateNode.containerInfo===k.containerInfo&&_.stateNode.implementation===k.implementation){i(V,_.sibling),G=u(_,k.children||[]),G.return=V,V=G;break e}else{i(V,_);break}else n(V,_);_=_.sibling}G=Gh(k,V.mode,G),G.return=V,V=G}return g(V);case B:return ue=k._init,k=ue(k._payload),rt(V,_,k,G)}if(he(k))return fe(V,_,k,G);if(X(k)){if(ue=X(k),typeof ue!="function")throw Error(r(150));return k=ue.call(k),xe(V,_,k,G)}if(typeof k.then=="function")return rt(V,_,xu(k),G);if(k.$$typeof===E)return rt(V,_,ku(V,k),G);Su(V,k)}return typeof k=="string"&&k!==""||typeof k=="number"||typeof k=="bigint"?(k=""+k,_!==null&&_.tag===6?(i(V,_.sibling),G=u(_,k),G.return=V,V=G):(i(V,_),G=Fh(k,V.mode,G),G.return=V,V=G),g(V)):i(V,_)}return function(V,_,k,G){try{Cl=0;var ue=rt(V,_,k,G);return ar=null,ue}catch(me){if(me===El)throw me;var Ne=wn(29,me,null,V.mode);return Ne.lanes=G,Ne.return=V,Ne}finally{}}}var $i=gb(!0),vb=gb(!1),ir=Se(null),wu=Se(0);function bb(e,n){e=Sa,Ge(wu,e),Ge(ir,n),Sa=e|n.baseLanes}function Wd(){Ge(wu,Sa),Ge(ir,ir.current)}function $d(){Sa=wu.current,Fe(ir),Fe(wu)}var bn=Se(null),Xn=null;function $a(e){var n=e.alternate;Ge(St,St.current&1),Ge(bn,e),Xn===null&&(n===null||ir.current!==null||n.memoizedState!==null)&&(Xn=e)}function xb(e){if(e.tag===22){if(Ge(St,St.current),Ge(bn,e),Xn===null){var n=e.alternate;n!==null&&n.memoizedState!==null&&(Xn=e)}}else Ja()}function Ja(){Ge(St,St.current),Ge(bn,bn.current)}function fa(e){Fe(bn),Xn===e&&(Xn=null),Fe(St)}var St=Se(0);function Eu(e){for(var n=e;n!==null;){if(n.tag===13){var i=n.memoizedState;if(i!==null&&(i=i.dehydrated,i===null||i.data==="$?"||i.data==="$!"))return n}else if(n.tag===19&&n.memoizedProps.revealOrder!==void 0){if((n.flags&128)!==0)return n}else if(n.child!==null){n.child.return=n,n=n.child;continue}if(n===e)break;for(;n.sibling===null;){if(n.return===null||n.return===e)return null;n=n.return}n.sibling.return=n.return,n=n.sibling}return null}var Nj=typeof AbortController<"u"?AbortController:function(){var e=[],n=this.signal={aborted:!1,addEventListener:function(i,l){e.push(l)}};this.abort=function(){n.aborted=!0,e.forEach(function(i){return i()})}},Rj=t.unstable_scheduleCallback,Oj=t.unstable_NormalPriority,wt={$$typeof:E,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function Jd(){return{controller:new Nj,data:new Map,refCount:0}}function Al(e){e.refCount--,e.refCount===0&&Rj(Oj,function(){e.controller.abort()})}var Dl=null,Id=0,sr=0,rr=null;function Vj(e,n){if(Dl===null){var i=Dl=[];Id=0,sr=sm(),rr={status:"pending",value:void 0,then:function(l){i.push(l)}}}return Id++,n.then(Sb,Sb),n}function Sb(){if(--Id===0&&Dl!==null){rr!==null&&(rr.status="fulfilled");var e=Dl;Dl=null,sr=0,rr=null;for(var n=0;n<e.length;n++)(0,e[n])()}}function zj(e,n){var i=[],l={status:"pending",value:null,reason:null,then:function(u){i.push(u)}};return e.then(function(){l.status="fulfilled",l.value=n;for(var u=0;u<i.length;u++)(0,i[u])(n)},function(u){for(l.status="rejected",l.reason=u,u=0;u<i.length;u++)(0,i[u])(void 0)}),l}var wb=Y.S;Y.S=function(e,n){typeof n=="object"&&n!==null&&typeof n.then=="function"&&Vj(e,n),wb!==null&&wb(e,n)};var Ji=Se(null);function eh(){var e=Ji.current;return e!==null?e:Ze.pooledCache}function Tu(e,n){n===null?Ge(Ji,Ji.current):Ge(Ji,n.pool)}function Eb(){var e=eh();return e===null?null:{parent:wt._currentValue,pool:e}}var Ia=0,Ce=null,Ye=null,mt=null,Cu=!1,lr=!1,Ii=!1,ju=0,_l=0,or=null,kj=0;function dt(){throw Error(r(321))}function th(e,n){if(n===null)return!1;for(var i=0;i<n.length&&i<e.length;i++)if(!tn(e[i],n[i]))return!1;return!0}function nh(e,n,i,l,u,f){return Ia=f,Ce=n,n.memoizedState=null,n.updateQueue=null,n.lanes=0,Y.H=e===null||e.memoizedState===null?es:ei,Ii=!1,f=i(l,u),Ii=!1,lr&&(f=Cb(n,i,l,u)),Tb(e),f}function Tb(e){Y.H=Kn;var n=Ye!==null&&Ye.next!==null;if(Ia=0,mt=Ye=Ce=null,Cu=!1,_l=0,or=null,n)throw Error(r(300));e===null||Tt||(e=e.dependencies,e!==null&&zu(e)&&(Tt=!0))}function Cb(e,n,i,l){Ce=e;var u=0;do{if(lr&&(or=null),_l=0,lr=!1,25<=u)throw Error(r(301));if(u+=1,mt=Ye=null,e.updateQueue!=null){var f=e.updateQueue;f.lastEffect=null,f.events=null,f.stores=null,f.memoCache!=null&&(f.memoCache.index=0)}Y.H=ts,f=n(i,l)}while(lr);return f}function Lj(){var e=Y.H,n=e.useState()[0];return n=typeof n.then=="function"?Ml(n):n,e=e.useState()[0],(Ye!==null?Ye.memoizedState:null)!==e&&(Ce.flags|=1024),n}function ah(){var e=ju!==0;return ju=0,e}function ih(e,n,i){n.updateQueue=e.updateQueue,n.flags&=-2053,e.lanes&=~i}function sh(e){if(Cu){for(e=e.memoizedState;e!==null;){var n=e.queue;n!==null&&(n.pending=null),e=e.next}Cu=!1}Ia=0,mt=Ye=Ce=null,lr=!1,_l=ju=0,or=null}function Wt(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return mt===null?Ce.memoizedState=mt=e:mt=mt.next=e,mt}function pt(){if(Ye===null){var e=Ce.alternate;e=e!==null?e.memoizedState:null}else e=Ye.next;var n=mt===null?Ce.memoizedState:mt.next;if(n!==null)mt=n,Ye=e;else{if(e===null)throw Ce.alternate===null?Error(r(467)):Error(r(310));Ye=e,e={memoizedState:Ye.memoizedState,baseState:Ye.baseState,baseQueue:Ye.baseQueue,queue:Ye.queue,next:null},mt===null?Ce.memoizedState=mt=e:mt=mt.next=e}return mt}var Au;Au=function(){return{lastEffect:null,events:null,stores:null,memoCache:null}};function Ml(e){var n=_l;return _l+=1,or===null&&(or=[]),e=mb(or,e,n),n=Ce,(mt===null?n.memoizedState:mt.next)===null&&(n=n.alternate,Y.H=n===null||n.memoizedState===null?es:ei),e}function Du(e){if(e!==null&&typeof e=="object"){if(typeof e.then=="function")return Ml(e);if(e.$$typeof===E)return kt(e)}throw Error(r(438,String(e)))}function rh(e){var n=null,i=Ce.updateQueue;if(i!==null&&(n=i.memoCache),n==null){var l=Ce.alternate;l!==null&&(l=l.updateQueue,l!==null&&(l=l.memoCache,l!=null&&(n={data:l.data.map(function(u){return u.slice()}),index:0})))}if(n==null&&(n={data:[],index:0}),i===null&&(i=Au(),Ce.updateQueue=i),i.memoCache=n,i=n.data[n.index],i===void 0)for(i=n.data[n.index]=Array(e),l=0;l<e;l++)i[l]=W;return n.index++,i}function da(e,n){return typeof n=="function"?n(e):n}function _u(e){var n=pt();return lh(n,Ye,e)}function lh(e,n,i){var l=e.queue;if(l===null)throw Error(r(311));l.lastRenderedReducer=i;var u=e.baseQueue,f=l.pending;if(f!==null){if(u!==null){var g=u.next;u.next=f.next,f.next=g}n.baseQueue=u=f,l.pending=null}if(f=e.baseState,u===null)e.memoizedState=f;else{n=u.next;var x=g=null,T=null,M=n,F=!1;do{var Q=M.lane&-536870913;if(Q!==M.lane?(Le&Q)===Q:(Ia&Q)===Q){var U=M.revertLane;if(U===0)T!==null&&(T=T.next={lane:0,revertLane:0,action:M.action,hasEagerState:M.hasEagerState,eagerState:M.eagerState,next:null}),Q===sr&&(F=!0);else if((Ia&U)===U){M=M.next,U===sr&&(F=!0);continue}else Q={lane:0,revertLane:M.revertLane,action:M.action,hasEagerState:M.hasEagerState,eagerState:M.eagerState,next:null},T===null?(x=T=Q,g=f):T=T.next=Q,Ce.lanes|=U,ui|=U;Q=M.action,Ii&&i(f,Q),f=M.hasEagerState?M.eagerState:i(f,Q)}else U={lane:Q,revertLane:M.revertLane,action:M.action,hasEagerState:M.hasEagerState,eagerState:M.eagerState,next:null},T===null?(x=T=U,g=f):T=T.next=U,Ce.lanes|=Q,ui|=Q;M=M.next}while(M!==null&&M!==n);if(T===null?g=f:T.next=x,!tn(f,e.memoizedState)&&(Tt=!0,F&&(i=rr,i!==null)))throw i;e.memoizedState=f,e.baseState=g,e.baseQueue=T,l.lastRenderedState=f}return u===null&&(l.lanes=0),[e.memoizedState,l.dispatch]}function oh(e){var n=pt(),i=n.queue;if(i===null)throw Error(r(311));i.lastRenderedReducer=e;var l=i.dispatch,u=i.pending,f=n.memoizedState;if(u!==null){i.pending=null;var g=u=u.next;do f=e(f,g.action),g=g.next;while(g!==u);tn(f,n.memoizedState)||(Tt=!0),n.memoizedState=f,n.baseQueue===null&&(n.baseState=f),i.lastRenderedState=f}return[f,l]}function jb(e,n,i){var l=Ce,u=pt(),f=Ue;if(f){if(i===void 0)throw Error(r(407));i=i()}else i=n();var g=!tn((Ye||u).memoizedState,i);if(g&&(u.memoizedState=i,Tt=!0),u=u.queue,fh(_b.bind(null,l,u,e),[e]),u.getSnapshot!==n||g||mt!==null&&mt.memoizedState.tag&1){if(l.flags|=2048,ur(9,Db.bind(null,l,u,i,n),{destroy:void 0},null),Ze===null)throw Error(r(349));f||(Ia&60)!==0||Ab(l,n,i)}return i}function Ab(e,n,i){e.flags|=16384,e={getSnapshot:n,value:i},n=Ce.updateQueue,n===null?(n=Au(),Ce.updateQueue=n,n.stores=[e]):(i=n.stores,i===null?n.stores=[e]:i.push(e))}function Db(e,n,i,l){n.value=i,n.getSnapshot=l,Mb(n)&&Nb(e)}function _b(e,n,i){return i(function(){Mb(n)&&Nb(e)})}function Mb(e){var n=e.getSnapshot;e=e.value;try{var i=n();return!tn(e,i)}catch{return!0}}function Nb(e){var n=Wa(e,2);n!==null&&Gt(n,e,2)}function uh(e){var n=Wt();if(typeof e=="function"){var i=e;if(e=i(),Ii){Xa(!0);try{i()}finally{Xa(!1)}}}return n.memoizedState=n.baseState=e,n.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:da,lastRenderedState:e},n}function Rb(e,n,i,l){return e.baseState=i,lh(e,Ye,typeof l=="function"?l:da)}function Uj(e,n,i,l,u){if(Ru(e))throw Error(r(485));if(e=n.action,e!==null){var f={payload:u,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(g){f.listeners.push(g)}};Y.T!==null?i(!0):f.isTransition=!1,l(f),i=n.pending,i===null?(f.next=n.pending=f,Ob(n,f)):(f.next=i.next,n.pending=i.next=f)}}function Ob(e,n){var i=n.action,l=n.payload,u=e.state;if(n.isTransition){var f=Y.T,g={};Y.T=g;try{var x=i(u,l),T=Y.S;T!==null&&T(g,x),Vb(e,n,x)}catch(M){ch(e,n,M)}finally{Y.T=f}}else try{f=i(u,l),Vb(e,n,f)}catch(M){ch(e,n,M)}}function Vb(e,n,i){i!==null&&typeof i=="object"&&typeof i.then=="function"?i.then(function(l){zb(e,n,l)},function(l){return ch(e,n,l)}):zb(e,n,i)}function zb(e,n,i){n.status="fulfilled",n.value=i,kb(n),e.state=i,n=e.pending,n!==null&&(i=n.next,i===n?e.pending=null:(i=i.next,n.next=i,Ob(e,i)))}function ch(e,n,i){var l=e.pending;if(e.pending=null,l!==null){l=l.next;do n.status="rejected",n.reason=i,kb(n),n=n.next;while(n!==l)}e.action=null}function kb(e){e=e.listeners;for(var n=0;n<e.length;n++)(0,e[n])()}function Lb(e,n){return n}function Ub(e,n){if(Ue){var i=Ze.formState;if(i!==null){e:{var l=Ce;if(Ue){if(Dt){t:{for(var u=Dt,f=Qn;u.nodeType!==8;){if(!f){u=null;break t}if(u=zn(u.nextSibling),u===null){u=null;break t}}f=u.data,u=f==="F!"||f==="F"?u:null}if(u){Dt=zn(u.nextSibling),l=u.data==="F!";break e}}Wi(l)}l=!1}l&&(n=i[0])}}return i=Wt(),i.memoizedState=i.baseState=n,l={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Lb,lastRenderedState:n},i.queue=l,i=nx.bind(null,Ce,l),l.dispatch=i,l=uh(!1),f=yh.bind(null,Ce,!1,l.queue),l=Wt(),u={state:n,dispatch:null,action:e,pending:null},l.queue=u,i=Uj.bind(null,Ce,u,f,i),u.dispatch=i,l.memoizedState=e,[n,i,!1]}function Bb(e){var n=pt();return Hb(n,Ye,e)}function Hb(e,n,i){n=lh(e,n,Lb)[0],e=_u(da)[0],n=typeof n=="object"&&n!==null&&typeof n.then=="function"?Ml(n):n;var l=pt(),u=l.queue,f=u.dispatch;return i!==l.memoizedState&&(Ce.flags|=2048,ur(9,Bj.bind(null,u,i),{destroy:void 0},null)),[n,f,e]}function Bj(e,n){e.action=n}function Pb(e){var n=pt(),i=Ye;if(i!==null)return Hb(n,i,e);pt(),n=n.memoizedState,i=pt();var l=i.queue.dispatch;return i.memoizedState=e,[n,l,!1]}function ur(e,n,i,l){return e={tag:e,create:n,inst:i,deps:l,next:null},n=Ce.updateQueue,n===null&&(n=Au(),Ce.updateQueue=n),i=n.lastEffect,i===null?n.lastEffect=e.next=e:(l=i.next,i.next=e,e.next=l,n.lastEffect=e),e}function qb(){return pt().memoizedState}function Mu(e,n,i,l){var u=Wt();Ce.flags|=e,u.memoizedState=ur(1|n,i,{destroy:void 0},l===void 0?null:l)}function Nu(e,n,i,l){var u=pt();l=l===void 0?null:l;var f=u.memoizedState.inst;Ye!==null&&l!==null&&th(l,Ye.memoizedState.deps)?u.memoizedState=ur(n,i,f,l):(Ce.flags|=e,u.memoizedState=ur(1|n,i,f,l))}function Fb(e,n){Mu(8390656,8,e,n)}function fh(e,n){Nu(2048,8,e,n)}function Gb(e,n){return Nu(4,2,e,n)}function Yb(e,n){return Nu(4,4,e,n)}function Qb(e,n){if(typeof n=="function"){e=e();var i=n(e);return function(){typeof i=="function"?i():n(null)}}if(n!=null)return e=e(),n.current=e,function(){n.current=null}}function Xb(e,n,i){i=i!=null?i.concat([e]):null,Nu(4,4,Qb.bind(null,n,e),i)}function dh(){}function Kb(e,n){var i=pt();n=n===void 0?null:n;var l=i.memoizedState;return n!==null&&th(n,l[1])?l[0]:(i.memoizedState=[e,n],e)}function Zb(e,n){var i=pt();n=n===void 0?null:n;var l=i.memoizedState;if(n!==null&&th(n,l[1]))return l[0];if(l=e(),Ii){Xa(!0);try{e()}finally{Xa(!1)}}return i.memoizedState=[l,n],l}function hh(e,n,i){return i===void 0||(Ia&1073741824)!==0?e.memoizedState=n:(e.memoizedState=i,e=$x(),Ce.lanes|=e,ui|=e,i)}function Wb(e,n,i,l){return tn(i,n)?i:ir.current!==null?(e=hh(e,i,l),tn(e,n)||(Tt=!0),e):(Ia&42)===0?(Tt=!0,e.memoizedState=i):(e=$x(),Ce.lanes|=e,ui|=e,n)}function $b(e,n,i,l,u){var f=ie.p;ie.p=f!==0&&8>f?f:8;var g=Y.T,x={};Y.T=x,yh(e,!1,n,i);try{var T=u(),M=Y.S;if(M!==null&&M(x,T),T!==null&&typeof T=="object"&&typeof T.then=="function"){var F=zj(T,l);Nl(e,n,F,rn(e))}else Nl(e,n,l,rn(e))}catch(Q){Nl(e,n,{then:function(){},status:"rejected",reason:Q},rn())}finally{ie.p=f,Y.T=g}}function Hj(){}function mh(e,n,i,l){if(e.tag!==5)throw Error(r(476));var u=Jb(e).queue;$b(e,u,n,_e,i===null?Hj:function(){return Ib(e),i(l)})}function Jb(e){var n=e.memoizedState;if(n!==null)return n;n={memoizedState:_e,baseState:_e,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:da,lastRenderedState:_e},next:null};var i={};return n.next={memoizedState:i,baseState:i,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:da,lastRenderedState:i},next:null},e.memoizedState=n,e=e.alternate,e!==null&&(e.memoizedState=n),n}function Ib(e){var n=Jb(e).next.queue;Nl(e,n,{},rn())}function ph(){return kt($l)}function ex(){return pt().memoizedState}function tx(){return pt().memoizedState}function Pj(e){for(var n=e.return;n!==null;){switch(n.tag){case 24:case 3:var i=rn();e=ai(i);var l=ii(n,e,i);l!==null&&(Gt(l,n,i),Vl(l,n,i)),n={cache:Jd()},e.payload=n;return}n=n.return}}function qj(e,n,i){var l=rn();i={lane:l,revertLane:0,action:i,hasEagerState:!1,eagerState:null,next:null},Ru(e)?ax(n,i):(i=Yd(e,n,i,l),i!==null&&(Gt(i,e,l),ix(i,n,l)))}function nx(e,n,i){var l=rn();Nl(e,n,i,l)}function Nl(e,n,i,l){var u={lane:l,revertLane:0,action:i,hasEagerState:!1,eagerState:null,next:null};if(Ru(e))ax(n,u);else{var f=e.alternate;if(e.lanes===0&&(f===null||f.lanes===0)&&(f=n.lastRenderedReducer,f!==null))try{var g=n.lastRenderedState,x=f(g,i);if(u.hasEagerState=!0,u.eagerState=x,tn(x,g))return pu(e,n,u,0),Ze===null&&mu(),!1}catch{}finally{}if(i=Yd(e,n,u,l),i!==null)return Gt(i,e,l),ix(i,n,l),!0}return!1}function yh(e,n,i,l){if(l={lane:2,revertLane:sm(),action:l,hasEagerState:!1,eagerState:null,next:null},Ru(e)){if(n)throw Error(r(479))}else n=Yd(e,i,l,2),n!==null&&Gt(n,e,2)}function Ru(e){var n=e.alternate;return e===Ce||n!==null&&n===Ce}function ax(e,n){lr=Cu=!0;var i=e.pending;i===null?n.next=n:(n.next=i.next,i.next=n),e.pending=n}function ix(e,n,i){if((i&4194176)!==0){var l=n.lanes;l&=e.pendingLanes,i|=l,n.lanes=i,p1(e,i)}}var Kn={readContext:kt,use:Du,useCallback:dt,useContext:dt,useEffect:dt,useImperativeHandle:dt,useLayoutEffect:dt,useInsertionEffect:dt,useMemo:dt,useReducer:dt,useRef:dt,useState:dt,useDebugValue:dt,useDeferredValue:dt,useTransition:dt,useSyncExternalStore:dt,useId:dt};Kn.useCacheRefresh=dt,Kn.useMemoCache=dt,Kn.useHostTransitionStatus=dt,Kn.useFormState=dt,Kn.useActionState=dt,Kn.useOptimistic=dt;var es={readContext:kt,use:Du,useCallback:function(e,n){return Wt().memoizedState=[e,n===void 0?null:n],e},useContext:kt,useEffect:Fb,useImperativeHandle:function(e,n,i){i=i!=null?i.concat([e]):null,Mu(4194308,4,Qb.bind(null,n,e),i)},useLayoutEffect:function(e,n){return Mu(4194308,4,e,n)},useInsertionEffect:function(e,n){Mu(4,2,e,n)},useMemo:function(e,n){var i=Wt();n=n===void 0?null:n;var l=e();if(Ii){Xa(!0);try{e()}finally{Xa(!1)}}return i.memoizedState=[l,n],l},useReducer:function(e,n,i){var l=Wt();if(i!==void 0){var u=i(n);if(Ii){Xa(!0);try{i(n)}finally{Xa(!1)}}}else u=n;return l.memoizedState=l.baseState=u,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:u},l.queue=e,e=e.dispatch=qj.bind(null,Ce,e),[l.memoizedState,e]},useRef:function(e){var n=Wt();return e={current:e},n.memoizedState=e},useState:function(e){e=uh(e);var n=e.queue,i=nx.bind(null,Ce,n);return n.dispatch=i,[e.memoizedState,i]},useDebugValue:dh,useDeferredValue:function(e,n){var i=Wt();return hh(i,e,n)},useTransition:function(){var e=uh(!1);return e=$b.bind(null,Ce,e.queue,!0,!1),Wt().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,n,i){var l=Ce,u=Wt();if(Ue){if(i===void 0)throw Error(r(407));i=i()}else{if(i=n(),Ze===null)throw Error(r(349));(Le&60)!==0||Ab(l,n,i)}u.memoizedState=i;var f={value:i,getSnapshot:n};return u.queue=f,Fb(_b.bind(null,l,f,e),[e]),l.flags|=2048,ur(9,Db.bind(null,l,f,i,n),{destroy:void 0},null),i},useId:function(){var e=Wt(),n=Ze.identifierPrefix;if(Ue){var i=ca,l=ua;i=(l&~(1<<32-en(l)-1)).toString(32)+i,n=":"+n+"R"+i,i=ju++,0<i&&(n+="H"+i.toString(32)),n+=":"}else i=kj++,n=":"+n+"r"+i.toString(32)+":";return e.memoizedState=n},useCacheRefresh:function(){return Wt().memoizedState=Pj.bind(null,Ce)}};es.useMemoCache=rh,es.useHostTransitionStatus=ph,es.useFormState=Ub,es.useActionState=Ub,es.useOptimistic=function(e){var n=Wt();n.memoizedState=n.baseState=e;var i={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return n.queue=i,n=yh.bind(null,Ce,!0,i),i.dispatch=n,[e,n]};var ei={readContext:kt,use:Du,useCallback:Kb,useContext:kt,useEffect:fh,useImperativeHandle:Xb,useInsertionEffect:Gb,useLayoutEffect:Yb,useMemo:Zb,useReducer:_u,useRef:qb,useState:function(){return _u(da)},useDebugValue:dh,useDeferredValue:function(e,n){var i=pt();return Wb(i,Ye.memoizedState,e,n)},useTransition:function(){var e=_u(da)[0],n=pt().memoizedState;return[typeof e=="boolean"?e:Ml(e),n]},useSyncExternalStore:jb,useId:ex};ei.useCacheRefresh=tx,ei.useMemoCache=rh,ei.useHostTransitionStatus=ph,ei.useFormState=Bb,ei.useActionState=Bb,ei.useOptimistic=function(e,n){var i=pt();return Rb(i,Ye,e,n)};var ts={readContext:kt,use:Du,useCallback:Kb,useContext:kt,useEffect:fh,useImperativeHandle:Xb,useInsertionEffect:Gb,useLayoutEffect:Yb,useMemo:Zb,useReducer:oh,useRef:qb,useState:function(){return oh(da)},useDebugValue:dh,useDeferredValue:function(e,n){var i=pt();return Ye===null?hh(i,e,n):Wb(i,Ye.memoizedState,e,n)},useTransition:function(){var e=oh(da)[0],n=pt().memoizedState;return[typeof e=="boolean"?e:Ml(e),n]},useSyncExternalStore:jb,useId:ex};ts.useCacheRefresh=tx,ts.useMemoCache=rh,ts.useHostTransitionStatus=ph,ts.useFormState=Pb,ts.useActionState=Pb,ts.useOptimistic=function(e,n){var i=pt();return Ye!==null?Rb(i,Ye,e,n):(i.baseState=e,[e,i.queue.dispatch])};function gh(e,n,i,l){n=e.memoizedState,i=i(l,n),i=i==null?n:te({},n,i),e.memoizedState=i,e.lanes===0&&(e.updateQueue.baseState=i)}var vh={isMounted:function(e){return(e=e._reactInternals)?re(e)===e:!1},enqueueSetState:function(e,n,i){e=e._reactInternals;var l=rn(),u=ai(l);u.payload=n,i!=null&&(u.callback=i),n=ii(e,u,l),n!==null&&(Gt(n,e,l),Vl(n,e,l))},enqueueReplaceState:function(e,n,i){e=e._reactInternals;var l=rn(),u=ai(l);u.tag=1,u.payload=n,i!=null&&(u.callback=i),n=ii(e,u,l),n!==null&&(Gt(n,e,l),Vl(n,e,l))},enqueueForceUpdate:function(e,n){e=e._reactInternals;var i=rn(),l=ai(i);l.tag=2,n!=null&&(l.callback=n),n=ii(e,l,i),n!==null&&(Gt(n,e,i),Vl(n,e,i))}};function sx(e,n,i,l,u,f,g){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(l,f,g):n.prototype&&n.prototype.isPureReactComponent?!vl(i,l)||!vl(u,f):!0}function rx(e,n,i,l){e=n.state,typeof n.componentWillReceiveProps=="function"&&n.componentWillReceiveProps(i,l),typeof n.UNSAFE_componentWillReceiveProps=="function"&&n.UNSAFE_componentWillReceiveProps(i,l),n.state!==e&&vh.enqueueReplaceState(n,n.state,null)}function ns(e,n){var i=n;if("ref"in n){i={};for(var l in n)l!=="ref"&&(i[l]=n[l])}if(e=e.defaultProps){i===n&&(i=te({},i));for(var u in e)i[u]===void 0&&(i[u]=e[u])}return i}var Ou=typeof reportError=="function"?reportError:function(e){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var n=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof e=="object"&&e!==null&&typeof e.message=="string"?String(e.message):String(e),error:e});if(!window.dispatchEvent(n))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",e);return}console.error(e)};function lx(e){Ou(e)}function ox(e){console.error(e)}function ux(e){Ou(e)}function Vu(e,n){try{var i=e.onUncaughtError;i(n.value,{componentStack:n.stack})}catch(l){setTimeout(function(){throw l})}}function cx(e,n,i){try{var l=e.onCaughtError;l(i.value,{componentStack:i.stack,errorBoundary:n.tag===1?n.stateNode:null})}catch(u){setTimeout(function(){throw u})}}function bh(e,n,i){return i=ai(i),i.tag=3,i.payload={element:null},i.callback=function(){Vu(e,n)},i}function fx(e){return e=ai(e),e.tag=3,e}function dx(e,n,i,l){var u=i.type.getDerivedStateFromError;if(typeof u=="function"){var f=l.value;e.payload=function(){return u(f)},e.callback=function(){cx(n,i,l)}}var g=i.stateNode;g!==null&&typeof g.componentDidCatch=="function"&&(e.callback=function(){cx(n,i,l),typeof u!="function"&&(ci===null?ci=new Set([this]):ci.add(this));var x=l.stack;this.componentDidCatch(l.value,{componentStack:x!==null?x:""})})}function Fj(e,n,i,l,u){if(i.flags|=32768,l!==null&&typeof l=="object"&&typeof l.then=="function"){if(n=i.alternate,n!==null&&Ol(n,i,u,!0),i=bn.current,i!==null){switch(i.tag){case 13:return Xn===null?em():i.alternate===null&&st===0&&(st=3),i.flags&=-257,i.flags|=65536,i.lanes=u,l===Zd?i.flags|=16384:(n=i.updateQueue,n===null?i.updateQueue=new Set([l]):n.add(l),nm(e,l,u)),!1;case 22:return i.flags|=65536,l===Zd?i.flags|=16384:(n=i.updateQueue,n===null?(n={transitions:null,markerInstances:null,retryQueue:new Set([l])},i.updateQueue=n):(i=n.retryQueue,i===null?n.retryQueue=new Set([l]):i.add(l)),nm(e,l,u)),!1}throw Error(r(435,i.tag))}return nm(e,l,u),em(),!1}if(Ue)return n=bn.current,n!==null?((n.flags&65536)===0&&(n.flags|=256),n.flags|=65536,n.lanes=u,l!==Kd&&(e=Error(r(422),{cause:l}),wl(yn(e,i)))):(l!==Kd&&(n=Error(r(423),{cause:l}),wl(yn(n,i))),e=e.current.alternate,e.flags|=65536,u&=-u,e.lanes|=u,l=yn(l,i),u=bh(e.stateNode,l,u),Vh(e,u),st!==4&&(st=2)),!1;var f=Error(r(520),{cause:l});if(f=yn(f,i),ql===null?ql=[f]:ql.push(f),st!==4&&(st=2),n===null)return!0;l=yn(l,i),i=n;do{switch(i.tag){case 3:return i.flags|=65536,e=u&-u,i.lanes|=e,e=bh(i.stateNode,l,e),Vh(i,e),!1;case 1:if(n=i.type,f=i.stateNode,(i.flags&128)===0&&(typeof n.getDerivedStateFromError=="function"||f!==null&&typeof f.componentDidCatch=="function"&&(ci===null||!ci.has(f))))return i.flags|=65536,u&=-u,i.lanes|=u,u=fx(u),dx(u,e,i,l),Vh(i,u),!1}i=i.return}while(i!==null);return!1}var hx=Error(r(461)),Tt=!1;function _t(e,n,i,l){n.child=e===null?vb(n,null,i,l):$i(n,e.child,i,l)}function mx(e,n,i,l,u){i=i.render;var f=n.ref;if("ref"in l){var g={};for(var x in l)x!=="ref"&&(g[x]=l[x])}else g=l;return is(n),l=nh(e,n,i,g,f,u),x=ah(),e!==null&&!Tt?(ih(e,n,u),ha(e,n,u)):(Ue&&x&&Qd(n),n.flags|=1,_t(e,n,l,u),n.child)}function px(e,n,i,l,u){if(e===null){var f=i.type;return typeof f=="function"&&!qh(f)&&f.defaultProps===void 0&&i.compare===null?(n.tag=15,n.type=f,yx(e,n,f,l,u)):(e=Bu(i.type,null,l,n,n.mode,u),e.ref=n.ref,e.return=n,n.child=e)}if(f=e.child,!Dh(e,u)){var g=f.memoizedProps;if(i=i.compare,i=i!==null?i:vl,i(g,l)&&e.ref===n.ref)return ha(e,n,u)}return n.flags|=1,e=oi(f,l),e.ref=n.ref,e.return=n,n.child=e}function yx(e,n,i,l,u){if(e!==null){var f=e.memoizedProps;if(vl(f,l)&&e.ref===n.ref)if(Tt=!1,n.pendingProps=l=f,Dh(e,u))(e.flags&131072)!==0&&(Tt=!0);else return n.lanes=e.lanes,ha(e,n,u)}return xh(e,n,i,l,u)}function gx(e,n,i){var l=n.pendingProps,u=l.children,f=(n.stateNode._pendingVisibility&2)!==0,g=e!==null?e.memoizedState:null;if(Rl(e,n),l.mode==="hidden"||f){if((n.flags&128)!==0){if(l=g!==null?g.baseLanes|i:i,e!==null){for(u=n.child=e.child,f=0;u!==null;)f=f|u.lanes|u.childLanes,u=u.sibling;n.childLanes=f&~l}else n.childLanes=0,n.child=null;return vx(e,n,l,i)}if((i&536870912)!==0)n.memoizedState={baseLanes:0,cachePool:null},e!==null&&Tu(n,g!==null?g.cachePool:null),g!==null?bb(n,g):Wd(),xb(n);else return n.lanes=n.childLanes=536870912,vx(e,n,g!==null?g.baseLanes|i:i,i)}else g!==null?(Tu(n,g.cachePool),bb(n,g),Ja(),n.memoizedState=null):(e!==null&&Tu(n,null),Wd(),Ja());return _t(e,n,u,i),n.child}function vx(e,n,i,l){var u=eh();return u=u===null?null:{parent:wt._currentValue,pool:u},n.memoizedState={baseLanes:i,cachePool:u},e!==null&&Tu(n,null),Wd(),xb(n),e!==null&&Ol(e,n,l,!0),null}function Rl(e,n){var i=n.ref;if(i===null)e!==null&&e.ref!==null&&(n.flags|=2097664);else{if(typeof i!="function"&&typeof i!="object")throw Error(r(284));(e===null||e.ref!==i)&&(n.flags|=2097664)}}function xh(e,n,i,l,u){return is(n),i=nh(e,n,i,l,void 0,u),l=ah(),e!==null&&!Tt?(ih(e,n,u),ha(e,n,u)):(Ue&&l&&Qd(n),n.flags|=1,_t(e,n,i,u),n.child)}function bx(e,n,i,l,u,f){return is(n),n.updateQueue=null,i=Cb(n,l,i,u),Tb(e),l=ah(),e!==null&&!Tt?(ih(e,n,f),ha(e,n,f)):(Ue&&l&&Qd(n),n.flags|=1,_t(e,n,i,f),n.child)}function xx(e,n,i,l,u){if(is(n),n.stateNode===null){var f=er,g=i.contextType;typeof g=="object"&&g!==null&&(f=kt(g)),f=new i(l,f),n.memoizedState=f.state!==null&&f.state!==void 0?f.state:null,f.updater=vh,n.stateNode=f,f._reactInternals=n,f=n.stateNode,f.props=l,f.state=n.memoizedState,f.refs={},Rh(n),g=i.contextType,f.context=typeof g=="object"&&g!==null?kt(g):er,f.state=n.memoizedState,g=i.getDerivedStateFromProps,typeof g=="function"&&(gh(n,i,g,l),f.state=n.memoizedState),typeof i.getDerivedStateFromProps=="function"||typeof f.getSnapshotBeforeUpdate=="function"||typeof f.UNSAFE_componentWillMount!="function"&&typeof f.componentWillMount!="function"||(g=f.state,typeof f.componentWillMount=="function"&&f.componentWillMount(),typeof f.UNSAFE_componentWillMount=="function"&&f.UNSAFE_componentWillMount(),g!==f.state&&vh.enqueueReplaceState(f,f.state,null),kl(n,l,f,u),zl(),f.state=n.memoizedState),typeof f.componentDidMount=="function"&&(n.flags|=4194308),l=!0}else if(e===null){f=n.stateNode;var x=n.memoizedProps,T=ns(i,x);f.props=T;var M=f.context,F=i.contextType;g=er,typeof F=="object"&&F!==null&&(g=kt(F));var Q=i.getDerivedStateFromProps;F=typeof Q=="function"||typeof f.getSnapshotBeforeUpdate=="function",x=n.pendingProps!==x,F||typeof f.UNSAFE_componentWillReceiveProps!="function"&&typeof f.componentWillReceiveProps!="function"||(x||M!==g)&&rx(n,f,l,g),ni=!1;var U=n.memoizedState;f.state=U,kl(n,l,f,u),zl(),M=n.memoizedState,x||U!==M||ni?(typeof Q=="function"&&(gh(n,i,Q,l),M=n.memoizedState),(T=ni||sx(n,i,T,l,U,M,g))?(F||typeof f.UNSAFE_componentWillMount!="function"&&typeof f.componentWillMount!="function"||(typeof f.componentWillMount=="function"&&f.componentWillMount(),typeof f.UNSAFE_componentWillMount=="function"&&f.UNSAFE_componentWillMount()),typeof f.componentDidMount=="function"&&(n.flags|=4194308)):(typeof f.componentDidMount=="function"&&(n.flags|=4194308),n.memoizedProps=l,n.memoizedState=M),f.props=l,f.state=M,f.context=g,l=T):(typeof f.componentDidMount=="function"&&(n.flags|=4194308),l=!1)}else{f=n.stateNode,Oh(e,n),g=n.memoizedProps,F=ns(i,g),f.props=F,Q=n.pendingProps,U=f.context,M=i.contextType,T=er,typeof M=="object"&&M!==null&&(T=kt(M)),x=i.getDerivedStateFromProps,(M=typeof x=="function"||typeof f.getSnapshotBeforeUpdate=="function")||typeof f.UNSAFE_componentWillReceiveProps!="function"&&typeof f.componentWillReceiveProps!="function"||(g!==Q||U!==T)&&rx(n,f,l,T),ni=!1,U=n.memoizedState,f.state=U,kl(n,l,f,u),zl();var q=n.memoizedState;g!==Q||U!==q||ni||e!==null&&e.dependencies!==null&&zu(e.dependencies)?(typeof x=="function"&&(gh(n,i,x,l),q=n.memoizedState),(F=ni||sx(n,i,F,l,U,q,T)||e!==null&&e.dependencies!==null&&zu(e.dependencies))?(M||typeof f.UNSAFE_componentWillUpdate!="function"&&typeof f.componentWillUpdate!="function"||(typeof f.componentWillUpdate=="function"&&f.componentWillUpdate(l,q,T),typeof f.UNSAFE_componentWillUpdate=="function"&&f.UNSAFE_componentWillUpdate(l,q,T)),typeof f.componentDidUpdate=="function"&&(n.flags|=4),typeof f.getSnapshotBeforeUpdate=="function"&&(n.flags|=1024)):(typeof f.componentDidUpdate!="function"||g===e.memoizedProps&&U===e.memoizedState||(n.flags|=4),typeof f.getSnapshotBeforeUpdate!="function"||g===e.memoizedProps&&U===e.memoizedState||(n.flags|=1024),n.memoizedProps=l,n.memoizedState=q),f.props=l,f.state=q,f.context=T,l=F):(typeof f.componentDidUpdate!="function"||g===e.memoizedProps&&U===e.memoizedState||(n.flags|=4),typeof f.getSnapshotBeforeUpdate!="function"||g===e.memoizedProps&&U===e.memoizedState||(n.flags|=1024),l=!1)}return f=l,Rl(e,n),l=(n.flags&128)!==0,f||l?(f=n.stateNode,i=l&&typeof i.getDerivedStateFromError!="function"?null:f.render(),n.flags|=1,e!==null&&l?(n.child=$i(n,e.child,null,u),n.child=$i(n,null,i,u)):_t(e,n,i,u),n.memoizedState=f.state,e=n.child):e=ha(e,n,u),e}function Sx(e,n,i,l){return Sl(),n.flags|=256,_t(e,n,i,l),n.child}var Sh={dehydrated:null,treeContext:null,retryLane:0};function wh(e){return{baseLanes:e,cachePool:Eb()}}function Eh(e,n,i){return e=e!==null?e.childLanes&~i:0,n&&(e|=En),e}function wx(e,n,i){var l=n.pendingProps,u=!1,f=(n.flags&128)!==0,g;if((g=f)||(g=e!==null&&e.memoizedState===null?!1:(St.current&2)!==0),g&&(u=!0,n.flags&=-129),g=(n.flags&32)!==0,n.flags&=-33,e===null){if(Ue){if(u?$a(n):Ja(),Ue){var x=Dt,T;if(T=x){e:{for(T=x,x=Qn;T.nodeType!==8;){if(!x){x=null;break e}if(T=zn(T.nextSibling),T===null){x=null;break e}}x=T}x!==null?(n.memoizedState={dehydrated:x,treeContext:Ki!==null?{id:ua,overflow:ca}:null,retryLane:536870912},T=wn(18,null,null,0),T.stateNode=x,T.return=n,n.child=T,Ft=n,Dt=null,T=!0):T=!1}T||Wi(n)}if(x=n.memoizedState,x!==null&&(x=x.dehydrated,x!==null))return x.data==="$!"?n.lanes=16:n.lanes=536870912,null;fa(n)}return x=l.children,l=l.fallback,u?(Ja(),u=n.mode,x=Ch({mode:"hidden",children:x},u),l=rs(l,u,i,null),x.return=n,l.return=n,x.sibling=l,n.child=x,u=n.child,u.memoizedState=wh(i),u.childLanes=Eh(e,g,i),n.memoizedState=Sh,l):($a(n),Th(n,x))}if(T=e.memoizedState,T!==null&&(x=T.dehydrated,x!==null)){if(f)n.flags&256?($a(n),n.flags&=-257,n=jh(e,n,i)):n.memoizedState!==null?(Ja(),n.child=e.child,n.flags|=128,n=null):(Ja(),u=l.fallback,x=n.mode,l=Ch({mode:"visible",children:l.children},x),u=rs(u,x,i,null),u.flags|=2,l.return=n,u.return=n,l.sibling=u,n.child=l,$i(n,e.child,null,i),l=n.child,l.memoizedState=wh(i),l.childLanes=Eh(e,g,i),n.memoizedState=Sh,n=u);else if($a(n),x.data==="$!"){if(g=x.nextSibling&&x.nextSibling.dataset,g)var M=g.dgst;g=M,l=Error(r(419)),l.stack="",l.digest=g,wl({value:l,source:null,stack:null}),n=jh(e,n,i)}else if(Tt||Ol(e,n,i,!1),g=(i&e.childLanes)!==0,Tt||g){if(g=Ze,g!==null){if(l=i&-i,(l&42)!==0)l=1;else switch(l){case 2:l=1;break;case 8:l=4;break;case 32:l=16;break;case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:l=64;break;case 268435456:l=134217728;break;default:l=0}if(l=(l&(g.suspendedLanes|i))!==0?0:l,l!==0&&l!==T.retryLane)throw T.retryLane=l,Wa(e,l),Gt(g,e,l),hx}x.data==="$?"||em(),n=jh(e,n,i)}else x.data==="$?"?(n.flags|=128,n.child=e.child,n=i9.bind(null,e),x._reactRetry=n,n=null):(e=T.treeContext,Dt=zn(x.nextSibling),Ft=n,Ue=!0,On=null,Qn=!1,e!==null&&(gn[vn++]=ua,gn[vn++]=ca,gn[vn++]=Ki,ua=e.id,ca=e.overflow,Ki=n),n=Th(n,l.children),n.flags|=4096);return n}return u?(Ja(),u=l.fallback,x=n.mode,T=e.child,M=T.sibling,l=oi(T,{mode:"hidden",children:l.children}),l.subtreeFlags=T.subtreeFlags&31457280,M!==null?u=oi(M,u):(u=rs(u,x,i,null),u.flags|=2),u.return=n,l.return=n,l.sibling=u,n.child=l,l=u,u=n.child,x=e.child.memoizedState,x===null?x=wh(i):(T=x.cachePool,T!==null?(M=wt._currentValue,T=T.parent!==M?{parent:M,pool:M}:T):T=Eb(),x={baseLanes:x.baseLanes|i,cachePool:T}),u.memoizedState=x,u.childLanes=Eh(e,g,i),n.memoizedState=Sh,l):($a(n),i=e.child,e=i.sibling,i=oi(i,{mode:"visible",children:l.children}),i.return=n,i.sibling=null,e!==null&&(g=n.deletions,g===null?(n.deletions=[e],n.flags|=16):g.push(e)),n.child=i,n.memoizedState=null,i)}function Th(e,n){return n=Ch({mode:"visible",children:n},e.mode),n.return=e,e.child=n}function Ch(e,n){return Kx(e,n,0,null)}function jh(e,n,i){return $i(n,e.child,null,i),e=Th(n,n.pendingProps.children),e.flags|=2,n.memoizedState=null,e}function Ex(e,n,i){e.lanes|=n;var l=e.alternate;l!==null&&(l.lanes|=n),Mh(e.return,n,i)}function Ah(e,n,i,l,u){var f=e.memoizedState;f===null?e.memoizedState={isBackwards:n,rendering:null,renderingStartTime:0,last:l,tail:i,tailMode:u}:(f.isBackwards=n,f.rendering=null,f.renderingStartTime=0,f.last=l,f.tail=i,f.tailMode=u)}function Tx(e,n,i){var l=n.pendingProps,u=l.revealOrder,f=l.tail;if(_t(e,n,l.children,i),l=St.current,(l&2)!==0)l=l&1|2,n.flags|=128;else{if(e!==null&&(e.flags&128)!==0)e:for(e=n.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Ex(e,i,n);else if(e.tag===19)Ex(e,i,n);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===n)break e;for(;e.sibling===null;){if(e.return===null||e.return===n)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}l&=1}switch(Ge(St,l),u){case"forwards":for(i=n.child,u=null;i!==null;)e=i.alternate,e!==null&&Eu(e)===null&&(u=i),i=i.sibling;i=u,i===null?(u=n.child,n.child=null):(u=i.sibling,i.sibling=null),Ah(n,!1,u,i,f);break;case"backwards":for(i=null,u=n.child,n.child=null;u!==null;){if(e=u.alternate,e!==null&&Eu(e)===null){n.child=u;break}e=u.sibling,u.sibling=i,i=u,u=e}Ah(n,!0,i,null,f);break;case"together":Ah(n,!1,null,null,void 0);break;default:n.memoizedState=null}return n.child}function ha(e,n,i){if(e!==null&&(n.dependencies=e.dependencies),ui|=n.lanes,(i&n.childLanes)===0)if(e!==null){if(Ol(e,n,i,!1),(i&n.childLanes)===0)return null}else return null;if(e!==null&&n.child!==e.child)throw Error(r(153));if(n.child!==null){for(e=n.child,i=oi(e,e.pendingProps),n.child=i,i.return=n;e.sibling!==null;)e=e.sibling,i=i.sibling=oi(e,e.pendingProps),i.return=n;i.sibling=null}return n.child}function Dh(e,n){return(e.lanes&n)!==0?!0:(e=e.dependencies,!!(e!==null&&zu(e)))}function Gj(e,n,i){switch(n.tag){case 3:sl(n,n.stateNode.containerInfo),ti(n,wt,e.memoizedState.cache),Sl();break;case 27:case 5:Jo(n);break;case 4:sl(n,n.stateNode.containerInfo);break;case 10:ti(n,n.type,n.memoizedProps.value);break;case 13:var l=n.memoizedState;if(l!==null)return l.dehydrated!==null?($a(n),n.flags|=128,null):(i&n.child.childLanes)!==0?wx(e,n,i):($a(n),e=ha(e,n,i),e!==null?e.sibling:null);$a(n);break;case 19:var u=(e.flags&128)!==0;if(l=(i&n.childLanes)!==0,l||(Ol(e,n,i,!1),l=(i&n.childLanes)!==0),u){if(l)return Tx(e,n,i);n.flags|=128}if(u=n.memoizedState,u!==null&&(u.rendering=null,u.tail=null,u.lastEffect=null),Ge(St,St.current),l)break;return null;case 22:case 23:return n.lanes=0,gx(e,n,i);case 24:ti(n,wt,e.memoizedState.cache)}return ha(e,n,i)}function Cx(e,n,i){if(e!==null)if(e.memoizedProps!==n.pendingProps)Tt=!0;else{if(!Dh(e,i)&&(n.flags&128)===0)return Tt=!1,Gj(e,n,i);Tt=(e.flags&131072)!==0}else Tt=!1,Ue&&(n.flags&1048576)!==0&&ub(n,vu,n.index);switch(n.lanes=0,n.tag){case 16:e:{e=n.pendingProps;var l=n.elementType,u=l._init;if(l=u(l._payload),n.type=l,typeof l=="function")qh(l)?(e=ns(l,e),n.tag=1,n=xx(null,n,l,e,i)):(n.tag=0,n=xh(null,n,l,e,i));else{if(l!=null){if(u=l.$$typeof,u===N){n.tag=11,n=mx(null,n,l,e,i);break e}else if(u===z){n.tag=14,n=px(null,n,l,e,i);break e}}throw n=Z(l)||l,Error(r(306,n,""))}}return n;case 0:return xh(e,n,n.type,n.pendingProps,i);case 1:return l=n.type,u=ns(l,n.pendingProps),xx(e,n,l,u,i);case 3:e:{if(sl(n,n.stateNode.containerInfo),e===null)throw Error(r(387));var f=n.pendingProps;u=n.memoizedState,l=u.element,Oh(e,n),kl(n,f,null,i);var g=n.memoizedState;if(f=g.cache,ti(n,wt,f),f!==u.cache&&Nh(n,[wt],i,!0),zl(),f=g.element,u.isDehydrated)if(u={element:f,isDehydrated:!1,cache:g.cache},n.updateQueue.baseState=u,n.memoizedState=u,n.flags&256){n=Sx(e,n,f,i);break e}else if(f!==l){l=yn(Error(r(424)),n),wl(l),n=Sx(e,n,f,i);break e}else for(Dt=zn(n.stateNode.containerInfo.firstChild),Ft=n,Ue=!0,On=null,Qn=!0,i=vb(n,null,f,i),n.child=i;i;)i.flags=i.flags&-3|4096,i=i.sibling;else{if(Sl(),f===l){n=ha(e,n,i);break e}_t(e,n,f,i)}n=n.child}return n;case 26:return Rl(e,n),e===null?(i=D2(n.type,null,n.pendingProps,null))?n.memoizedState=i:Ue||(i=n.type,e=n.pendingProps,l=$u(Nn.current).createElement(i),l[zt]=n,l[Kt]=e,Mt(l,i,e),Et(l),n.stateNode=l):n.memoizedState=D2(n.type,e.memoizedProps,n.pendingProps,e.memoizedState),null;case 27:return Jo(n),e===null&&Ue&&(l=n.stateNode=C2(n.type,n.pendingProps,Nn.current),Ft=n,Qn=!0,Dt=zn(l.firstChild)),l=n.pendingProps.children,e!==null||Ue?_t(e,n,l,i):n.child=$i(n,null,l,i),Rl(e,n),n.child;case 5:return e===null&&Ue&&((u=l=Dt)&&(l=x9(l,n.type,n.pendingProps,Qn),l!==null?(n.stateNode=l,Ft=n,Dt=zn(l.firstChild),Qn=!1,u=!0):u=!1),u||Wi(n)),Jo(n),u=n.type,f=n.pendingProps,g=e!==null?e.memoizedProps:null,l=f.children,mm(u,f)?l=null:g!==null&&mm(u,g)&&(n.flags|=32),n.memoizedState!==null&&(u=nh(e,n,Lj,null,null,i),$l._currentValue=u),Rl(e,n),_t(e,n,l,i),n.child;case 6:return e===null&&Ue&&((e=i=Dt)&&(i=S9(i,n.pendingProps,Qn),i!==null?(n.stateNode=i,Ft=n,Dt=null,e=!0):e=!1),e||Wi(n)),null;case 13:return wx(e,n,i);case 4:return sl(n,n.stateNode.containerInfo),l=n.pendingProps,e===null?n.child=$i(n,null,l,i):_t(e,n,l,i),n.child;case 11:return mx(e,n,n.type,n.pendingProps,i);case 7:return _t(e,n,n.pendingProps,i),n.child;case 8:return _t(e,n,n.pendingProps.children,i),n.child;case 12:return _t(e,n,n.pendingProps.children,i),n.child;case 10:return l=n.pendingProps,ti(n,n.type,l.value),_t(e,n,l.children,i),n.child;case 9:return u=n.type._context,l=n.pendingProps.children,is(n),u=kt(u),l=l(u),n.flags|=1,_t(e,n,l,i),n.child;case 14:return px(e,n,n.type,n.pendingProps,i);case 15:return yx(e,n,n.type,n.pendingProps,i);case 19:return Tx(e,n,i);case 22:return gx(e,n,i);case 24:return is(n),l=kt(wt),e===null?(u=eh(),u===null&&(u=Ze,f=Jd(),u.pooledCache=f,f.refCount++,f!==null&&(u.pooledCacheLanes|=i),u=f),n.memoizedState={parent:l,cache:u},Rh(n),ti(n,wt,u)):((e.lanes&i)!==0&&(Oh(e,n),kl(n,null,null,i),zl()),u=e.memoizedState,f=n.memoizedState,u.parent!==l?(u={parent:l,cache:l},n.memoizedState=u,n.lanes===0&&(n.memoizedState=n.updateQueue.baseState=u),ti(n,wt,l)):(l=f.cache,ti(n,wt,l),l!==u.cache&&Nh(n,[wt],i,!0))),_t(e,n,n.pendingProps.children,i),n.child;case 29:throw n.pendingProps}throw Error(r(156,n.tag))}var _h=Se(null),as=null,ma=null;function ti(e,n,i){Ge(_h,n._currentValue),n._currentValue=i}function pa(e){e._currentValue=_h.current,Fe(_h)}function Mh(e,n,i){for(;e!==null;){var l=e.alternate;if((e.childLanes&n)!==n?(e.childLanes|=n,l!==null&&(l.childLanes|=n)):l!==null&&(l.childLanes&n)!==n&&(l.childLanes|=n),e===i)break;e=e.return}}function Nh(e,n,i,l){var u=e.child;for(u!==null&&(u.return=e);u!==null;){var f=u.dependencies;if(f!==null){var g=u.child;f=f.firstContext;e:for(;f!==null;){var x=f;f=u;for(var T=0;T<n.length;T++)if(x.context===n[T]){f.lanes|=i,x=f.alternate,x!==null&&(x.lanes|=i),Mh(f.return,i,e),l||(g=null);break e}f=x.next}}else if(u.tag===18){if(g=u.return,g===null)throw Error(r(341));g.lanes|=i,f=g.alternate,f!==null&&(f.lanes|=i),Mh(g,i,e),g=null}else g=u.child;if(g!==null)g.return=u;else for(g=u;g!==null;){if(g===e){g=null;break}if(u=g.sibling,u!==null){u.return=g.return,g=u;break}g=g.return}u=g}}function Ol(e,n,i,l){e=null;for(var u=n,f=!1;u!==null;){if(!f){if((u.flags&524288)!==0)f=!0;else if((u.flags&262144)!==0)break}if(u.tag===10){var g=u.alternate;if(g===null)throw Error(r(387));if(g=g.memoizedProps,g!==null){var x=u.type;tn(u.pendingProps.value,g.value)||(e!==null?e.push(x):e=[x])}}else if(u===Hi.current){if(g=u.alternate,g===null)throw Error(r(387));g.memoizedState.memoizedState!==u.memoizedState.memoizedState&&(e!==null?e.push($l):e=[$l])}u=u.return}e!==null&&Nh(n,e,i,l),n.flags|=262144}function zu(e){for(e=e.firstContext;e!==null;){if(!tn(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function is(e){as=e,ma=null,e=e.dependencies,e!==null&&(e.firstContext=null)}function kt(e){return jx(as,e)}function ku(e,n){return as===null&&is(e),jx(e,n)}function jx(e,n){var i=n._currentValue;if(n={context:n,memoizedValue:i,next:null},ma===null){if(e===null)throw Error(r(308));ma=n,e.dependencies={lanes:0,firstContext:n},e.flags|=524288}else ma=ma.next=n;return i}var ni=!1;function Rh(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function Oh(e,n){e=e.updateQueue,n.updateQueue===e&&(n.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function ai(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function ii(e,n,i){var l=e.updateQueue;if(l===null)return null;if(l=l.shared,(tt&2)!==0){var u=l.pending;return u===null?n.next=n:(n.next=u.next,u.next=n),l.pending=n,n=yu(e),lb(e,null,i),n}return pu(e,l,n,i),yu(e)}function Vl(e,n,i){if(n=n.updateQueue,n!==null&&(n=n.shared,(i&4194176)!==0)){var l=n.lanes;l&=e.pendingLanes,i|=l,n.lanes=i,p1(e,i)}}function Vh(e,n){var i=e.updateQueue,l=e.alternate;if(l!==null&&(l=l.updateQueue,i===l)){var u=null,f=null;if(i=i.firstBaseUpdate,i!==null){do{var g={lane:i.lane,tag:i.tag,payload:i.payload,callback:null,next:null};f===null?u=f=g:f=f.next=g,i=i.next}while(i!==null);f===null?u=f=n:f=f.next=n}else u=f=n;i={baseState:l.baseState,firstBaseUpdate:u,lastBaseUpdate:f,shared:l.shared,callbacks:l.callbacks},e.updateQueue=i;return}e=i.lastBaseUpdate,e===null?i.firstBaseUpdate=n:e.next=n,i.lastBaseUpdate=n}var zh=!1;function zl(){if(zh){var e=rr;if(e!==null)throw e}}function kl(e,n,i,l){zh=!1;var u=e.updateQueue;ni=!1;var f=u.firstBaseUpdate,g=u.lastBaseUpdate,x=u.shared.pending;if(x!==null){u.shared.pending=null;var T=x,M=T.next;T.next=null,g===null?f=M:g.next=M,g=T;var F=e.alternate;F!==null&&(F=F.updateQueue,x=F.lastBaseUpdate,x!==g&&(x===null?F.firstBaseUpdate=M:x.next=M,F.lastBaseUpdate=T))}if(f!==null){var Q=u.baseState;g=0,F=M=T=null,x=f;do{var U=x.lane&-536870913,q=U!==x.lane;if(q?(Le&U)===U:(l&U)===U){U!==0&&U===sr&&(zh=!0),F!==null&&(F=F.next={lane:0,tag:x.tag,payload:x.payload,callback:null,next:null});e:{var fe=e,xe=x;U=n;var rt=i;switch(xe.tag){case 1:if(fe=xe.payload,typeof fe=="function"){Q=fe.call(rt,Q,U);break e}Q=fe;break e;case 3:fe.flags=fe.flags&-65537|128;case 0:if(fe=xe.payload,U=typeof fe=="function"?fe.call(rt,Q,U):fe,U==null)break e;Q=te({},Q,U);break e;case 2:ni=!0}}U=x.callback,U!==null&&(e.flags|=64,q&&(e.flags|=8192),q=u.callbacks,q===null?u.callbacks=[U]:q.push(U))}else q={lane:U,tag:x.tag,payload:x.payload,callback:x.callback,next:null},F===null?(M=F=q,T=Q):F=F.next=q,g|=U;if(x=x.next,x===null){if(x=u.shared.pending,x===null)break;q=x,x=q.next,q.next=null,u.lastBaseUpdate=q,u.shared.pending=null}}while(!0);F===null&&(T=Q),u.baseState=T,u.firstBaseUpdate=M,u.lastBaseUpdate=F,f===null&&(u.shared.lanes=0),ui|=g,e.lanes=g,e.memoizedState=Q}}function Ax(e,n){if(typeof e!="function")throw Error(r(191,e));e.call(n)}function Dx(e,n){var i=e.callbacks;if(i!==null)for(e.callbacks=null,e=0;e<i.length;e++)Ax(i[e],n)}function Ll(e,n){try{var i=n.updateQueue,l=i!==null?i.lastEffect:null;if(l!==null){var u=l.next;i=u;do{if((i.tag&e)===e){l=void 0;var f=i.create,g=i.inst;l=f(),g.destroy=l}i=i.next}while(i!==u)}}catch(x){Xe(n,n.return,x)}}function si(e,n,i){try{var l=n.updateQueue,u=l!==null?l.lastEffect:null;if(u!==null){var f=u.next;l=f;do{if((l.tag&e)===e){var g=l.inst,x=g.destroy;if(x!==void 0){g.destroy=void 0,u=n;var T=i;try{x()}catch(M){Xe(u,T,M)}}}l=l.next}while(l!==f)}}catch(M){Xe(n,n.return,M)}}function _x(e){var n=e.updateQueue;if(n!==null){var i=e.stateNode;try{Dx(n,i)}catch(l){Xe(e,e.return,l)}}}function Mx(e,n,i){i.props=ns(e.type,e.memoizedProps),i.state=e.memoizedState;try{i.componentWillUnmount()}catch(l){Xe(e,n,l)}}function ss(e,n){try{var i=e.ref;if(i!==null){var l=e.stateNode;switch(e.tag){case 26:case 27:case 5:var u=l;break;default:u=l}typeof i=="function"?e.refCleanup=i(u):i.current=u}}catch(f){Xe(e,n,f)}}function nn(e,n){var i=e.ref,l=e.refCleanup;if(i!==null)if(typeof l=="function")try{l()}catch(u){Xe(e,n,u)}finally{e.refCleanup=null,e=e.alternate,e!=null&&(e.refCleanup=null)}else if(typeof i=="function")try{i(null)}catch(u){Xe(e,n,u)}else i.current=null}function Nx(e){var n=e.type,i=e.memoizedProps,l=e.stateNode;try{e:switch(n){case"button":case"input":case"select":case"textarea":i.autoFocus&&l.focus();break e;case"img":i.src?l.src=i.src:i.srcSet&&(l.srcset=i.srcSet)}}catch(u){Xe(e,e.return,u)}}function Rx(e,n,i){try{var l=e.stateNode;p9(l,e.type,i,n),l[Kt]=n}catch(u){Xe(e,e.return,u)}}function Ox(e){return e.tag===5||e.tag===3||e.tag===26||e.tag===27||e.tag===4}function kh(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||Ox(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==27&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Lh(e,n,i){var l=e.tag;if(l===5||l===6)e=e.stateNode,n?i.nodeType===8?i.parentNode.insertBefore(e,n):i.insertBefore(e,n):(i.nodeType===8?(n=i.parentNode,n.insertBefore(e,i)):(n=i,n.appendChild(e)),i=i._reactRootContainer,i!=null||n.onclick!==null||(n.onclick=Wu));else if(l!==4&&l!==27&&(e=e.child,e!==null))for(Lh(e,n,i),e=e.sibling;e!==null;)Lh(e,n,i),e=e.sibling}function Lu(e,n,i){var l=e.tag;if(l===5||l===6)e=e.stateNode,n?i.insertBefore(e,n):i.appendChild(e);else if(l!==4&&l!==27&&(e=e.child,e!==null))for(Lu(e,n,i),e=e.sibling;e!==null;)Lu(e,n,i),e=e.sibling}var ya=!1,it=!1,Uh=!1,Vx=typeof WeakSet=="function"?WeakSet:Set,Ct=null,zx=!1;function Yj(e,n){if(e=e.containerInfo,dm=ac,e=J1(e),Hd(e)){if("selectionStart"in e)var i={start:e.selectionStart,end:e.selectionEnd};else e:{i=(i=e.ownerDocument)&&i.defaultView||window;var l=i.getSelection&&i.getSelection();if(l&&l.rangeCount!==0){i=l.anchorNode;var u=l.anchorOffset,f=l.focusNode;l=l.focusOffset;try{i.nodeType,f.nodeType}catch{i=null;break e}var g=0,x=-1,T=-1,M=0,F=0,Q=e,U=null;t:for(;;){for(var q;Q!==i||u!==0&&Q.nodeType!==3||(x=g+u),Q!==f||l!==0&&Q.nodeType!==3||(T=g+l),Q.nodeType===3&&(g+=Q.nodeValue.length),(q=Q.firstChild)!==null;)U=Q,Q=q;for(;;){if(Q===e)break t;if(U===i&&++M===u&&(x=g),U===f&&++F===l&&(T=g),(q=Q.nextSibling)!==null)break;Q=U,U=Q.parentNode}Q=q}i=x===-1||T===-1?null:{start:x,end:T}}else i=null}i=i||{start:0,end:0}}else i=null;for(hm={focusedElem:e,selectionRange:i},ac=!1,Ct=n;Ct!==null;)if(n=Ct,e=n.child,(n.subtreeFlags&1028)!==0&&e!==null)e.return=n,Ct=e;else for(;Ct!==null;){switch(n=Ct,f=n.alternate,e=n.flags,n.tag){case 0:break;case 11:case 15:break;case 1:if((e&1024)!==0&&f!==null){e=void 0,i=n,u=f.memoizedProps,f=f.memoizedState,l=i.stateNode;try{var fe=ns(i.type,u,i.elementType===i.type);e=l.getSnapshotBeforeUpdate(fe,f),l.__reactInternalSnapshotBeforeUpdate=e}catch(xe){Xe(i,i.return,xe)}}break;case 3:if((e&1024)!==0){if(e=n.stateNode.containerInfo,i=e.nodeType,i===9)gm(e);else if(i===1)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":gm(e);break;default:e.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((e&1024)!==0)throw Error(r(163))}if(e=n.sibling,e!==null){e.return=n.return,Ct=e;break}Ct=n.return}return fe=zx,zx=!1,fe}function kx(e,n,i){var l=i.flags;switch(i.tag){case 0:case 11:case 15:va(e,i),l&4&&Ll(5,i);break;case 1:if(va(e,i),l&4)if(e=i.stateNode,n===null)try{e.componentDidMount()}catch(x){Xe(i,i.return,x)}else{var u=ns(i.type,n.memoizedProps);n=n.memoizedState;try{e.componentDidUpdate(u,n,e.__reactInternalSnapshotBeforeUpdate)}catch(x){Xe(i,i.return,x)}}l&64&&_x(i),l&512&&ss(i,i.return);break;case 3:if(va(e,i),l&64&&(l=i.updateQueue,l!==null)){if(e=null,i.child!==null)switch(i.child.tag){case 27:case 5:e=i.child.stateNode;break;case 1:e=i.child.stateNode}try{Dx(l,e)}catch(x){Xe(i,i.return,x)}}break;case 26:va(e,i),l&512&&ss(i,i.return);break;case 27:case 5:va(e,i),n===null&&l&4&&Nx(i),l&512&&ss(i,i.return);break;case 12:va(e,i);break;case 13:va(e,i),l&4&&Bx(e,i);break;case 22:if(u=i.memoizedState!==null||ya,!u){n=n!==null&&n.memoizedState!==null||it;var f=ya,g=it;ya=u,(it=n)&&!g?ri(e,i,(i.subtreeFlags&8772)!==0):va(e,i),ya=f,it=g}l&512&&(i.memoizedProps.mode==="manual"?ss(i,i.return):nn(i,i.return));break;default:va(e,i)}}function Lx(e){var n=e.alternate;n!==null&&(e.alternate=null,Lx(n)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(n=e.stateNode,n!==null&&Td(n)),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var yt=null,an=!1;function ga(e,n,i){for(i=i.child;i!==null;)Ux(e,n,i),i=i.sibling}function Ux(e,n,i){if(qt&&typeof qt.onCommitFiberUnmount=="function")try{qt.onCommitFiberUnmount(Qa,i)}catch{}switch(i.tag){case 26:it||nn(i,n),ga(e,n,i),i.memoizedState?i.memoizedState.count--:i.stateNode&&(i=i.stateNode,i.parentNode.removeChild(i));break;case 27:it||nn(i,n);var l=yt,u=an;for(yt=i.stateNode,ga(e,n,i),i=i.stateNode,n=i.attributes;n.length;)i.removeAttributeNode(n[0]);Td(i),yt=l,an=u;break;case 5:it||nn(i,n);case 6:u=yt;var f=an;if(yt=null,ga(e,n,i),yt=u,an=f,yt!==null)if(an)try{e=yt,l=i.stateNode,e.nodeType===8?e.parentNode.removeChild(l):e.removeChild(l)}catch(g){Xe(i,n,g)}else try{yt.removeChild(i.stateNode)}catch(g){Xe(i,n,g)}break;case 18:yt!==null&&(an?(n=yt,i=i.stateNode,n.nodeType===8?ym(n.parentNode,i):n.nodeType===1&&ym(n,i),to(n)):ym(yt,i.stateNode));break;case 4:l=yt,u=an,yt=i.stateNode.containerInfo,an=!0,ga(e,n,i),yt=l,an=u;break;case 0:case 11:case 14:case 15:it||si(2,i,n),it||si(4,i,n),ga(e,n,i);break;case 1:it||(nn(i,n),l=i.stateNode,typeof l.componentWillUnmount=="function"&&Mx(i,n,l)),ga(e,n,i);break;case 21:ga(e,n,i);break;case 22:it||nn(i,n),it=(l=it)||i.memoizedState!==null,ga(e,n,i),it=l;break;default:ga(e,n,i)}}function Bx(e,n){if(n.memoizedState===null&&(e=n.alternate,e!==null&&(e=e.memoizedState,e!==null&&(e=e.dehydrated,e!==null))))try{to(e)}catch(i){Xe(n,n.return,i)}}function Qj(e){switch(e.tag){case 13:case 19:var n=e.stateNode;return n===null&&(n=e.stateNode=new Vx),n;case 22:return e=e.stateNode,n=e._retryCache,n===null&&(n=e._retryCache=new Vx),n;default:throw Error(r(435,e.tag))}}function Bh(e,n){var i=Qj(e);n.forEach(function(l){var u=s9.bind(null,e,l);i.has(l)||(i.add(l),l.then(u,u))})}function xn(e,n){var i=n.deletions;if(i!==null)for(var l=0;l<i.length;l++){var u=i[l],f=e,g=n,x=g;e:for(;x!==null;){switch(x.tag){case 27:case 5:yt=x.stateNode,an=!1;break e;case 3:yt=x.stateNode.containerInfo,an=!0;break e;case 4:yt=x.stateNode.containerInfo,an=!0;break e}x=x.return}if(yt===null)throw Error(r(160));Ux(f,g,u),yt=null,an=!1,f=u.alternate,f!==null&&(f.return=null),u.return=null}if(n.subtreeFlags&13878)for(n=n.child;n!==null;)Hx(n,e),n=n.sibling}var Vn=null;function Hx(e,n){var i=e.alternate,l=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:xn(n,e),Sn(e),l&4&&(si(3,e,e.return),Ll(3,e),si(5,e,e.return));break;case 1:xn(n,e),Sn(e),l&512&&(it||i===null||nn(i,i.return)),l&64&&ya&&(e=e.updateQueue,e!==null&&(l=e.callbacks,l!==null&&(i=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=i===null?l:i.concat(l))));break;case 26:var u=Vn;if(xn(n,e),Sn(e),l&512&&(it||i===null||nn(i,i.return)),l&4){var f=i!==null?i.memoizedState:null;if(l=e.memoizedState,i===null)if(l===null)if(e.stateNode===null){e:{l=e.type,i=e.memoizedProps,u=u.ownerDocument||u;t:switch(l){case"title":f=u.getElementsByTagName("title")[0],(!f||f[ul]||f[zt]||f.namespaceURI==="http://www.w3.org/2000/svg"||f.hasAttribute("itemprop"))&&(f=u.createElement(l),u.head.insertBefore(f,u.querySelector("head > title"))),Mt(f,l,i),f[zt]=e,Et(f),l=f;break e;case"link":var g=N2("link","href",u).get(l+(i.href||""));if(g){for(var x=0;x<g.length;x++)if(f=g[x],f.getAttribute("href")===(i.href==null?null:i.href)&&f.getAttribute("rel")===(i.rel==null?null:i.rel)&&f.getAttribute("title")===(i.title==null?null:i.title)&&f.getAttribute("crossorigin")===(i.crossOrigin==null?null:i.crossOrigin)){g.splice(x,1);break t}}f=u.createElement(l),Mt(f,l,i),u.head.appendChild(f);break;case"meta":if(g=N2("meta","content",u).get(l+(i.content||""))){for(x=0;x<g.length;x++)if(f=g[x],f.getAttribute("content")===(i.content==null?null:""+i.content)&&f.getAttribute("name")===(i.name==null?null:i.name)&&f.getAttribute("property")===(i.property==null?null:i.property)&&f.getAttribute("http-equiv")===(i.httpEquiv==null?null:i.httpEquiv)&&f.getAttribute("charset")===(i.charSet==null?null:i.charSet)){g.splice(x,1);break t}}f=u.createElement(l),Mt(f,l,i),u.head.appendChild(f);break;default:throw Error(r(468,l))}f[zt]=e,Et(f),l=f}e.stateNode=l}else R2(u,e.type,e.stateNode);else e.stateNode=M2(u,l,e.memoizedProps);else f!==l?(f===null?i.stateNode!==null&&(i=i.stateNode,i.parentNode.removeChild(i)):f.count--,l===null?R2(u,e.type,e.stateNode):M2(u,l,e.memoizedProps)):l===null&&e.stateNode!==null&&Rx(e,e.memoizedProps,i.memoizedProps)}break;case 27:if(l&4&&e.alternate===null){u=e.stateNode,f=e.memoizedProps;try{for(var T=u.firstChild;T;){var M=T.nextSibling,F=T.nodeName;T[ul]||F==="HEAD"||F==="BODY"||F==="SCRIPT"||F==="STYLE"||F==="LINK"&&T.rel.toLowerCase()==="stylesheet"||u.removeChild(T),T=M}for(var Q=e.type,U=u.attributes;U.length;)u.removeAttributeNode(U[0]);Mt(u,Q,f),u[zt]=e,u[Kt]=f}catch(fe){Xe(e,e.return,fe)}}case 5:if(xn(n,e),Sn(e),l&512&&(it||i===null||nn(i,i.return)),e.flags&32){u=e.stateNode;try{Xs(u,"")}catch(fe){Xe(e,e.return,fe)}}l&4&&e.stateNode!=null&&(u=e.memoizedProps,Rx(e,u,i!==null?i.memoizedProps:u)),l&1024&&(Uh=!0);break;case 6:if(xn(n,e),Sn(e),l&4){if(e.stateNode===null)throw Error(r(162));l=e.memoizedProps,i=e.stateNode;try{i.nodeValue=l}catch(fe){Xe(e,e.return,fe)}}break;case 3:if(ec=null,u=Vn,Vn=Ju(n.containerInfo),xn(n,e),Vn=u,Sn(e),l&4&&i!==null&&i.memoizedState.isDehydrated)try{to(n.containerInfo)}catch(fe){Xe(e,e.return,fe)}Uh&&(Uh=!1,Px(e));break;case 4:l=Vn,Vn=Ju(e.stateNode.containerInfo),xn(n,e),Sn(e),Vn=l;break;case 12:xn(n,e),Sn(e);break;case 13:xn(n,e),Sn(e),e.child.flags&8192&&e.memoizedState!==null!=(i!==null&&i.memoizedState!==null)&&(Kh=ce()),l&4&&(l=e.updateQueue,l!==null&&(e.updateQueue=null,Bh(e,l)));break;case 22:if(l&512&&(it||i===null||nn(i,i.return)),T=e.memoizedState!==null,M=i!==null&&i.memoizedState!==null,F=ya,Q=it,ya=F||T,it=Q||M,xn(n,e),it=Q,ya=F,Sn(e),n=e.stateNode,n._current=e,n._visibility&=-3,n._visibility|=n._pendingVisibility&2,l&8192&&(n._visibility=T?n._visibility&-2:n._visibility|1,T&&(n=ya||it,i===null||M||n||cr(e)),e.memoizedProps===null||e.memoizedProps.mode!=="manual"))e:for(i=null,n=e;;){if(n.tag===5||n.tag===26||n.tag===27){if(i===null){M=i=n;try{if(u=M.stateNode,T)f=u.style,typeof f.setProperty=="function"?f.setProperty("display","none","important"):f.display="none";else{g=M.stateNode,x=M.memoizedProps.style;var q=x!=null&&x.hasOwnProperty("display")?x.display:null;g.style.display=q==null||typeof q=="boolean"?"":(""+q).trim()}}catch(fe){Xe(M,M.return,fe)}}}else if(n.tag===6){if(i===null){M=n;try{M.stateNode.nodeValue=T?"":M.memoizedProps}catch(fe){Xe(M,M.return,fe)}}}else if((n.tag!==22&&n.tag!==23||n.memoizedState===null||n===e)&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===e)break e;for(;n.sibling===null;){if(n.return===null||n.return===e)break e;i===n&&(i=null),n=n.return}i===n&&(i=null),n.sibling.return=n.return,n=n.sibling}l&4&&(l=e.updateQueue,l!==null&&(i=l.retryQueue,i!==null&&(l.retryQueue=null,Bh(e,i))));break;case 19:xn(n,e),Sn(e),l&4&&(l=e.updateQueue,l!==null&&(e.updateQueue=null,Bh(e,l)));break;case 21:break;default:xn(n,e),Sn(e)}}function Sn(e){var n=e.flags;if(n&2){try{if(e.tag!==27){e:{for(var i=e.return;i!==null;){if(Ox(i)){var l=i;break e}i=i.return}throw Error(r(160))}switch(l.tag){case 27:var u=l.stateNode,f=kh(e);Lu(e,f,u);break;case 5:var g=l.stateNode;l.flags&32&&(Xs(g,""),l.flags&=-33);var x=kh(e);Lu(e,x,g);break;case 3:case 4:var T=l.stateNode.containerInfo,M=kh(e);Lh(e,M,T);break;default:throw Error(r(161))}}}catch(F){Xe(e,e.return,F)}e.flags&=-3}n&4096&&(e.flags&=-4097)}function Px(e){if(e.subtreeFlags&1024)for(e=e.child;e!==null;){var n=e;Px(n),n.tag===5&&n.flags&1024&&n.stateNode.reset(),e=e.sibling}}function va(e,n){if(n.subtreeFlags&8772)for(n=n.child;n!==null;)kx(e,n.alternate,n),n=n.sibling}function cr(e){for(e=e.child;e!==null;){var n=e;switch(n.tag){case 0:case 11:case 14:case 15:si(4,n,n.return),cr(n);break;case 1:nn(n,n.return);var i=n.stateNode;typeof i.componentWillUnmount=="function"&&Mx(n,n.return,i),cr(n);break;case 26:case 27:case 5:nn(n,n.return),cr(n);break;case 22:nn(n,n.return),n.memoizedState===null&&cr(n);break;default:cr(n)}e=e.sibling}}function ri(e,n,i){for(i=i&&(n.subtreeFlags&8772)!==0,n=n.child;n!==null;){var l=n.alternate,u=e,f=n,g=f.flags;switch(f.tag){case 0:case 11:case 15:ri(u,f,i),Ll(4,f);break;case 1:if(ri(u,f,i),l=f,u=l.stateNode,typeof u.componentDidMount=="function")try{u.componentDidMount()}catch(M){Xe(l,l.return,M)}if(l=f,u=l.updateQueue,u!==null){var x=l.stateNode;try{var T=u.shared.hiddenCallbacks;if(T!==null)for(u.shared.hiddenCallbacks=null,u=0;u<T.length;u++)Ax(T[u],x)}catch(M){Xe(l,l.return,M)}}i&&g&64&&_x(f),ss(f,f.return);break;case 26:case 27:case 5:ri(u,f,i),i&&l===null&&g&4&&Nx(f),ss(f,f.return);break;case 12:ri(u,f,i);break;case 13:ri(u,f,i),i&&g&4&&Bx(u,f);break;case 22:f.memoizedState===null&&ri(u,f,i),ss(f,f.return);break;default:ri(u,f,i)}n=n.sibling}}function Hh(e,n){var i=null;e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(i=e.memoizedState.cachePool.pool),e=null,n.memoizedState!==null&&n.memoizedState.cachePool!==null&&(e=n.memoizedState.cachePool.pool),e!==i&&(e!=null&&e.refCount++,i!=null&&Al(i))}function Ph(e,n){e=null,n.alternate!==null&&(e=n.alternate.memoizedState.cache),n=n.memoizedState.cache,n!==e&&(n.refCount++,e!=null&&Al(e))}function li(e,n,i,l){if(n.subtreeFlags&10256)for(n=n.child;n!==null;)qx(e,n,i,l),n=n.sibling}function qx(e,n,i,l){var u=n.flags;switch(n.tag){case 0:case 11:case 15:li(e,n,i,l),u&2048&&Ll(9,n);break;case 3:li(e,n,i,l),u&2048&&(e=null,n.alternate!==null&&(e=n.alternate.memoizedState.cache),n=n.memoizedState.cache,n!==e&&(n.refCount++,e!=null&&Al(e)));break;case 12:if(u&2048){li(e,n,i,l),e=n.stateNode;try{var f=n.memoizedProps,g=f.id,x=f.onPostCommit;typeof x=="function"&&x(g,n.alternate===null?"mount":"update",e.passiveEffectDuration,-0)}catch(T){Xe(n,n.return,T)}}else li(e,n,i,l);break;case 23:break;case 22:f=n.stateNode,n.memoizedState!==null?f._visibility&4?li(e,n,i,l):Ul(e,n):f._visibility&4?li(e,n,i,l):(f._visibility|=4,fr(e,n,i,l,(n.subtreeFlags&10256)!==0)),u&2048&&Hh(n.alternate,n);break;case 24:li(e,n,i,l),u&2048&&Ph(n.alternate,n);break;default:li(e,n,i,l)}}function fr(e,n,i,l,u){for(u=u&&(n.subtreeFlags&10256)!==0,n=n.child;n!==null;){var f=e,g=n,x=i,T=l,M=g.flags;switch(g.tag){case 0:case 11:case 15:fr(f,g,x,T,u),Ll(8,g);break;case 23:break;case 22:var F=g.stateNode;g.memoizedState!==null?F._visibility&4?fr(f,g,x,T,u):Ul(f,g):(F._visibility|=4,fr(f,g,x,T,u)),u&&M&2048&&Hh(g.alternate,g);break;case 24:fr(f,g,x,T,u),u&&M&2048&&Ph(g.alternate,g);break;default:fr(f,g,x,T,u)}n=n.sibling}}function Ul(e,n){if(n.subtreeFlags&10256)for(n=n.child;n!==null;){var i=e,l=n,u=l.flags;switch(l.tag){case 22:Ul(i,l),u&2048&&Hh(l.alternate,l);break;case 24:Ul(i,l),u&2048&&Ph(l.alternate,l);break;default:Ul(i,l)}n=n.sibling}}var Bl=8192;function dr(e){if(e.subtreeFlags&Bl)for(e=e.child;e!==null;)Fx(e),e=e.sibling}function Fx(e){switch(e.tag){case 26:dr(e),e.flags&Bl&&e.memoizedState!==null&&V9(Vn,e.memoizedState,e.memoizedProps);break;case 5:dr(e);break;case 3:case 4:var n=Vn;Vn=Ju(e.stateNode.containerInfo),dr(e),Vn=n;break;case 22:e.memoizedState===null&&(n=e.alternate,n!==null&&n.memoizedState!==null?(n=Bl,Bl=16777216,dr(e),Bl=n):dr(e));break;default:dr(e)}}function Gx(e){var n=e.alternate;if(n!==null&&(e=n.child,e!==null)){n.child=null;do n=e.sibling,e.sibling=null,e=n;while(e!==null)}}function Hl(e){var n=e.deletions;if((e.flags&16)!==0){if(n!==null)for(var i=0;i<n.length;i++){var l=n[i];Ct=l,Qx(l,e)}Gx(e)}if(e.subtreeFlags&10256)for(e=e.child;e!==null;)Yx(e),e=e.sibling}function Yx(e){switch(e.tag){case 0:case 11:case 15:Hl(e),e.flags&2048&&si(9,e,e.return);break;case 3:Hl(e);break;case 12:Hl(e);break;case 22:var n=e.stateNode;e.memoizedState!==null&&n._visibility&4&&(e.return===null||e.return.tag!==13)?(n._visibility&=-5,Uu(e)):Hl(e);break;default:Hl(e)}}function Uu(e){var n=e.deletions;if((e.flags&16)!==0){if(n!==null)for(var i=0;i<n.length;i++){var l=n[i];Ct=l,Qx(l,e)}Gx(e)}for(e=e.child;e!==null;){switch(n=e,n.tag){case 0:case 11:case 15:si(8,n,n.return),Uu(n);break;case 22:i=n.stateNode,i._visibility&4&&(i._visibility&=-5,Uu(n));break;default:Uu(n)}e=e.sibling}}function Qx(e,n){for(;Ct!==null;){var i=Ct;switch(i.tag){case 0:case 11:case 15:si(8,i,n);break;case 23:case 22:if(i.memoizedState!==null&&i.memoizedState.cachePool!==null){var l=i.memoizedState.cachePool.pool;l!=null&&l.refCount++}break;case 24:Al(i.memoizedState.cache)}if(l=i.child,l!==null)l.return=i,Ct=l;else e:for(i=e;Ct!==null;){l=Ct;var u=l.sibling,f=l.return;if(Lx(l),l===i){Ct=null;break e}if(u!==null){u.return=f,Ct=u;break e}Ct=f}}}function Xj(e,n,i,l){this.tag=e,this.key=i,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=n,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=l,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function wn(e,n,i,l){return new Xj(e,n,i,l)}function qh(e){return e=e.prototype,!(!e||!e.isReactComponent)}function oi(e,n){var i=e.alternate;return i===null?(i=wn(e.tag,n,e.key,e.mode),i.elementType=e.elementType,i.type=e.type,i.stateNode=e.stateNode,i.alternate=e,e.alternate=i):(i.pendingProps=n,i.type=e.type,i.flags=0,i.subtreeFlags=0,i.deletions=null),i.flags=e.flags&31457280,i.childLanes=e.childLanes,i.lanes=e.lanes,i.child=e.child,i.memoizedProps=e.memoizedProps,i.memoizedState=e.memoizedState,i.updateQueue=e.updateQueue,n=e.dependencies,i.dependencies=n===null?null:{lanes:n.lanes,firstContext:n.firstContext},i.sibling=e.sibling,i.index=e.index,i.ref=e.ref,i.refCleanup=e.refCleanup,i}function Xx(e,n){e.flags&=31457282;var i=e.alternate;return i===null?(e.childLanes=0,e.lanes=n,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=i.childLanes,e.lanes=i.lanes,e.child=i.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=i.memoizedProps,e.memoizedState=i.memoizedState,e.updateQueue=i.updateQueue,e.type=i.type,n=i.dependencies,e.dependencies=n===null?null:{lanes:n.lanes,firstContext:n.firstContext}),e}function Bu(e,n,i,l,u,f){var g=0;if(l=e,typeof e=="function")qh(e)&&(g=1);else if(typeof e=="string")g=R9(e,i,Jt.current)?26:e==="html"||e==="head"||e==="body"?27:5;else e:switch(e){case p:return rs(i.children,u,f,n);case y:g=8,u|=24;break;case v:return e=wn(12,i,n,u|2),e.elementType=v,e.lanes=f,e;case C:return e=wn(13,i,n,u),e.elementType=C,e.lanes=f,e;case R:return e=wn(19,i,n,u),e.elementType=R,e.lanes=f,e;case H:return Kx(i,u,f,n);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case b:case E:g=10;break e;case S:g=9;break e;case N:g=11;break e;case z:g=14;break e;case B:g=16,l=null;break e}g=29,i=Error(r(130,e===null?"null":typeof e,"")),l=null}return n=wn(g,i,n,u),n.elementType=e,n.type=l,n.lanes=f,n}function rs(e,n,i,l){return e=wn(7,e,l,n),e.lanes=i,e}function Kx(e,n,i,l){e=wn(22,e,l,n),e.elementType=H,e.lanes=i;var u={_visibility:1,_pendingVisibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null,_current:null,detach:function(){var f=u._current;if(f===null)throw Error(r(456));if((u._pendingVisibility&2)===0){var g=Wa(f,2);g!==null&&(u._pendingVisibility|=2,Gt(g,f,2))}},attach:function(){var f=u._current;if(f===null)throw Error(r(456));if((u._pendingVisibility&2)!==0){var g=Wa(f,2);g!==null&&(u._pendingVisibility&=-3,Gt(g,f,2))}}};return e.stateNode=u,e}function Fh(e,n,i){return e=wn(6,e,null,n),e.lanes=i,e}function Gh(e,n,i){return n=wn(4,e.children!==null?e.children:[],e.key,n),n.lanes=i,n.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},n}function ba(e){e.flags|=4}function Zx(e,n){if(n.type!=="stylesheet"||(n.state.loading&4)!==0)e.flags&=-16777217;else if(e.flags|=16777216,!O2(n)){if(n=bn.current,n!==null&&((Le&4194176)===Le?Xn!==null:(Le&62914560)!==Le&&(Le&536870912)===0||n!==Xn))throw Tl=Zd,db;e.flags|=8192}}function Hu(e,n){n!==null&&(e.flags|=4),e.flags&16384&&(n=e.tag!==22?h1():536870912,e.lanes|=n,mr|=n)}function Pl(e,n){if(!Ue)switch(e.tailMode){case"hidden":n=e.tail;for(var i=null;n!==null;)n.alternate!==null&&(i=n),n=n.sibling;i===null?e.tail=null:i.sibling=null;break;case"collapsed":i=e.tail;for(var l=null;i!==null;)i.alternate!==null&&(l=i),i=i.sibling;l===null?n||e.tail===null?e.tail=null:e.tail.sibling=null:l.sibling=null}}function et(e){var n=e.alternate!==null&&e.alternate.child===e.child,i=0,l=0;if(n)for(var u=e.child;u!==null;)i|=u.lanes|u.childLanes,l|=u.subtreeFlags&31457280,l|=u.flags&31457280,u.return=e,u=u.sibling;else for(u=e.child;u!==null;)i|=u.lanes|u.childLanes,l|=u.subtreeFlags,l|=u.flags,u.return=e,u=u.sibling;return e.subtreeFlags|=l,e.childLanes=i,n}function Kj(e,n,i){var l=n.pendingProps;switch(Xd(n),n.tag){case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return et(n),null;case 1:return et(n),null;case 3:return i=n.stateNode,l=null,e!==null&&(l=e.memoizedState.cache),n.memoizedState.cache!==l&&(n.flags|=2048),pa(wt),Pi(),i.pendingContext&&(i.context=i.pendingContext,i.pendingContext=null),(e===null||e.child===null)&&(xl(n)?ba(n):e===null||e.memoizedState.isDehydrated&&(n.flags&256)===0||(n.flags|=1024,On!==null&&(Jh(On),On=null))),et(n),null;case 26:return i=n.memoizedState,e===null?(ba(n),i!==null?(et(n),Zx(n,i)):(et(n),n.flags&=-16777217)):i?i!==e.memoizedState?(ba(n),et(n),Zx(n,i)):(et(n),n.flags&=-16777217):(e.memoizedProps!==l&&ba(n),et(n),n.flags&=-16777217),null;case 27:A(n),i=Nn.current;var u=n.type;if(e!==null&&n.stateNode!=null)e.memoizedProps!==l&&ba(n);else{if(!l){if(n.stateNode===null)throw Error(r(166));return et(n),null}e=Jt.current,xl(n)?cb(n):(e=C2(u,l,i),n.stateNode=e,ba(n))}return et(n),null;case 5:if(A(n),i=n.type,e!==null&&n.stateNode!=null)e.memoizedProps!==l&&ba(n);else{if(!l){if(n.stateNode===null)throw Error(r(166));return et(n),null}if(e=Jt.current,xl(n))cb(n);else{switch(u=$u(Nn.current),e){case 1:e=u.createElementNS("http://www.w3.org/2000/svg",i);break;case 2:e=u.createElementNS("http://www.w3.org/1998/Math/MathML",i);break;default:switch(i){case"svg":e=u.createElementNS("http://www.w3.org/2000/svg",i);break;case"math":e=u.createElementNS("http://www.w3.org/1998/Math/MathML",i);break;case"script":e=u.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e=typeof l.is=="string"?u.createElement("select",{is:l.is}):u.createElement("select"),l.multiple?e.multiple=!0:l.size&&(e.size=l.size);break;default:e=typeof l.is=="string"?u.createElement(i,{is:l.is}):u.createElement(i)}}e[zt]=n,e[Kt]=l;e:for(u=n.child;u!==null;){if(u.tag===5||u.tag===6)e.appendChild(u.stateNode);else if(u.tag!==4&&u.tag!==27&&u.child!==null){u.child.return=u,u=u.child;continue}if(u===n)break e;for(;u.sibling===null;){if(u.return===null||u.return===n)break e;u=u.return}u.sibling.return=u.return,u=u.sibling}n.stateNode=e;e:switch(Mt(e,i,l),i){case"button":case"input":case"select":case"textarea":e=!!l.autoFocus;break e;case"img":e=!0;break e;default:e=!1}e&&ba(n)}}return et(n),n.flags&=-16777217,null;case 6:if(e&&n.stateNode!=null)e.memoizedProps!==l&&ba(n);else{if(typeof l!="string"&&n.stateNode===null)throw Error(r(166));if(e=Nn.current,xl(n)){if(e=n.stateNode,i=n.memoizedProps,l=null,u=Ft,u!==null)switch(u.tag){case 27:case 5:l=u.memoizedProps}e[zt]=n,e=!!(e.nodeValue===i||l!==null&&l.suppressHydrationWarning===!0||b2(e.nodeValue,i)),e||Wi(n)}else e=$u(e).createTextNode(l),e[zt]=n,n.stateNode=e}return et(n),null;case 13:if(l=n.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(u=xl(n),l!==null&&l.dehydrated!==null){if(e===null){if(!u)throw Error(r(318));if(u=n.memoizedState,u=u!==null?u.dehydrated:null,!u)throw Error(r(317));u[zt]=n}else Sl(),(n.flags&128)===0&&(n.memoizedState=null),n.flags|=4;et(n),u=!1}else On!==null&&(Jh(On),On=null),u=!0;if(!u)return n.flags&256?(fa(n),n):(fa(n),null)}if(fa(n),(n.flags&128)!==0)return n.lanes=i,n;if(i=l!==null,e=e!==null&&e.memoizedState!==null,i){l=n.child,u=null,l.alternate!==null&&l.alternate.memoizedState!==null&&l.alternate.memoizedState.cachePool!==null&&(u=l.alternate.memoizedState.cachePool.pool);var f=null;l.memoizedState!==null&&l.memoizedState.cachePool!==null&&(f=l.memoizedState.cachePool.pool),f!==u&&(l.flags|=2048)}return i!==e&&i&&(n.child.flags|=8192),Hu(n,n.updateQueue),et(n),null;case 4:return Pi(),e===null&&um(n.stateNode.containerInfo),et(n),null;case 10:return pa(n.type),et(n),null;case 19:if(Fe(St),u=n.memoizedState,u===null)return et(n),null;if(l=(n.flags&128)!==0,f=u.rendering,f===null)if(l)Pl(u,!1);else{if(st!==0||e!==null&&(e.flags&128)!==0)for(e=n.child;e!==null;){if(f=Eu(e),f!==null){for(n.flags|=128,Pl(u,!1),e=f.updateQueue,n.updateQueue=e,Hu(n,e),n.subtreeFlags=0,e=i,i=n.child;i!==null;)Xx(i,e),i=i.sibling;return Ge(St,St.current&1|2),n.child}e=e.sibling}u.tail!==null&&ce()>Pu&&(n.flags|=128,l=!0,Pl(u,!1),n.lanes=4194304)}else{if(!l)if(e=Eu(f),e!==null){if(n.flags|=128,l=!0,e=e.updateQueue,n.updateQueue=e,Hu(n,e),Pl(u,!0),u.tail===null&&u.tailMode==="hidden"&&!f.alternate&&!Ue)return et(n),null}else 2*ce()-u.renderingStartTime>Pu&&i!==536870912&&(n.flags|=128,l=!0,Pl(u,!1),n.lanes=4194304);u.isBackwards?(f.sibling=n.child,n.child=f):(e=u.last,e!==null?e.sibling=f:n.child=f,u.last=f)}return u.tail!==null?(n=u.tail,u.rendering=n,u.tail=n.sibling,u.renderingStartTime=ce(),n.sibling=null,e=St.current,Ge(St,l?e&1|2:e&1),n):(et(n),null);case 22:case 23:return fa(n),$d(),l=n.memoizedState!==null,e!==null?e.memoizedState!==null!==l&&(n.flags|=8192):l&&(n.flags|=8192),l?(i&536870912)!==0&&(n.flags&128)===0&&(et(n),n.subtreeFlags&6&&(n.flags|=8192)):et(n),i=n.updateQueue,i!==null&&Hu(n,i.retryQueue),i=null,e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(i=e.memoizedState.cachePool.pool),l=null,n.memoizedState!==null&&n.memoizedState.cachePool!==null&&(l=n.memoizedState.cachePool.pool),l!==i&&(n.flags|=2048),e!==null&&Fe(Ji),null;case 24:return i=null,e!==null&&(i=e.memoizedState.cache),n.memoizedState.cache!==i&&(n.flags|=2048),pa(wt),et(n),null;case 25:return null}throw Error(r(156,n.tag))}function Zj(e,n){switch(Xd(n),n.tag){case 1:return e=n.flags,e&65536?(n.flags=e&-65537|128,n):null;case 3:return pa(wt),Pi(),e=n.flags,(e&65536)!==0&&(e&128)===0?(n.flags=e&-65537|128,n):null;case 26:case 27:case 5:return A(n),null;case 13:if(fa(n),e=n.memoizedState,e!==null&&e.dehydrated!==null){if(n.alternate===null)throw Error(r(340));Sl()}return e=n.flags,e&65536?(n.flags=e&-65537|128,n):null;case 19:return Fe(St),null;case 4:return Pi(),null;case 10:return pa(n.type),null;case 22:case 23:return fa(n),$d(),e!==null&&Fe(Ji),e=n.flags,e&65536?(n.flags=e&-65537|128,n):null;case 24:return pa(wt),null;case 25:return null;default:return null}}function Wx(e,n){switch(Xd(n),n.tag){case 3:pa(wt),Pi();break;case 26:case 27:case 5:A(n);break;case 4:Pi();break;case 13:fa(n);break;case 19:Fe(St);break;case 10:pa(n.type);break;case 22:case 23:fa(n),$d(),e!==null&&Fe(Ji);break;case 24:pa(wt)}}var Wj={getCacheForType:function(e){var n=kt(wt),i=n.data.get(e);return i===void 0&&(i=e(),n.data.set(e,i)),i}},$j=typeof WeakMap=="function"?WeakMap:Map,tt=0,Ze=null,Re=null,Le=0,We=0,sn=null,xa=!1,hr=!1,Yh=!1,Sa=0,st=0,ui=0,ls=0,Qh=0,En=0,mr=0,ql=null,Zn=null,Xh=!1,Kh=0,Pu=1/0,qu=null,ci=null,Fu=!1,os=null,Fl=0,Zh=0,Wh=null,Gl=0,$h=null;function rn(){if((tt&2)!==0&&Le!==0)return Le&-Le;if(Y.T!==null){var e=sr;return e!==0?e:sm()}return g1()}function $x(){En===0&&(En=(Le&536870912)===0||Ue?d1():536870912);var e=bn.current;return e!==null&&(e.flags|=32),En}function Gt(e,n,i){(e===Ze&&We===2||e.cancelPendingCommit!==null)&&(pr(e,0),wa(e,Le,En,!1)),ol(e,i),((tt&2)===0||e!==Ze)&&(e===Ze&&((tt&2)===0&&(ls|=i),st===4&&wa(e,Le,En,!1)),Wn(e))}function Jx(e,n,i){if((tt&6)!==0)throw Error(r(327));var l=!i&&(n&60)===0&&(n&e.expiredLanes)===0||ll(e,n),u=l?e9(e,n):tm(e,n,!0),f=l;do{if(u===0){hr&&!l&&wa(e,n,0,!1);break}else if(u===6)wa(e,n,0,!xa);else{if(i=e.current.alternate,f&&!Jj(i)){u=tm(e,n,!1),f=!1;continue}if(u===2){if(f=n,e.errorRecoveryDisabledLanes&f)var g=0;else g=e.pendingLanes&-536870913,g=g!==0?g:g&536870912?536870912:0;if(g!==0){n=g;e:{var x=e;u=ql;var T=x.current.memoizedState.isDehydrated;if(T&&(pr(x,g).flags|=256),g=tm(x,g,!1),g!==2){if(Yh&&!T){x.errorRecoveryDisabledLanes|=f,ls|=f,u=4;break e}f=Zn,Zn=u,f!==null&&Jh(f)}u=g}if(f=!1,u!==2)continue}}if(u===1){pr(e,0),wa(e,n,0,!0);break}e:{switch(l=e,u){case 0:case 1:throw Error(r(345));case 4:if((n&4194176)===n){wa(l,n,En,!xa);break e}break;case 2:Zn=null;break;case 3:case 5:break;default:throw Error(r(329))}if(l.finishedWork=i,l.finishedLanes=n,(n&62914560)===n&&(f=Kh+300-ce(),10<f)){if(wa(l,n,En,!xa),nu(l,0)!==0)break e;l.timeoutHandle=w2(Ix.bind(null,l,i,Zn,qu,Xh,n,En,ls,mr,xa,2,-0,0),f);break e}Ix(l,i,Zn,qu,Xh,n,En,ls,mr,xa,0,-0,0)}}break}while(!0);Wn(e)}function Jh(e){Zn===null?Zn=e:Zn.push.apply(Zn,e)}function Ix(e,n,i,l,u,f,g,x,T,M,F,Q,U){var q=n.subtreeFlags;if((q&8192||(q&16785408)===16785408)&&(Wl={stylesheets:null,count:0,unsuspend:O9},Fx(n),n=z9(),n!==null)){e.cancelPendingCommit=n(r2.bind(null,e,i,l,u,g,x,T,1,Q,U)),wa(e,f,g,!M);return}r2(e,i,l,u,g,x,T,F,Q,U)}function Jj(e){for(var n=e;;){var i=n.tag;if((i===0||i===11||i===15)&&n.flags&16384&&(i=n.updateQueue,i!==null&&(i=i.stores,i!==null)))for(var l=0;l<i.length;l++){var u=i[l],f=u.getSnapshot;u=u.value;try{if(!tn(f(),u))return!1}catch{return!1}}if(i=n.child,n.subtreeFlags&16384&&i!==null)i.return=n,n=i;else{if(n===e)break;for(;n.sibling===null;){if(n.return===null||n.return===e)return!0;n=n.return}n.sibling.return=n.return,n=n.sibling}}return!0}function wa(e,n,i,l){n&=~Qh,n&=~ls,e.suspendedLanes|=n,e.pingedLanes&=~n,l&&(e.warmLanes|=n),l=e.expirationTimes;for(var u=n;0<u;){var f=31-en(u),g=1<<f;l[f]=-1,u&=~g}i!==0&&m1(e,i,n)}function Gu(){return(tt&6)===0?(Yl(0),!1):!0}function Ih(){if(Re!==null){if(We===0)var e=Re.return;else e=Re,ma=as=null,sh(e),ar=null,Cl=0,e=Re;for(;e!==null;)Wx(e.alternate,e),e=e.return;Re=null}}function pr(e,n){e.finishedWork=null,e.finishedLanes=0;var i=e.timeoutHandle;i!==-1&&(e.timeoutHandle=-1,g9(i)),i=e.cancelPendingCommit,i!==null&&(e.cancelPendingCommit=null,i()),Ih(),Ze=e,Re=i=oi(e.current,null),Le=n,We=0,sn=null,xa=!1,hr=ll(e,n),Yh=!1,mr=En=Qh=ls=ui=st=0,Zn=ql=null,Xh=!1,(n&8)!==0&&(n|=n&32);var l=e.entangledLanes;if(l!==0)for(e=e.entanglements,l&=n;0<l;){var u=31-en(l),f=1<<u;n|=e[u],l&=~f}return Sa=n,mu(),i}function e2(e,n){Ce=null,Y.H=Kn,n===El?(n=pb(),We=3):n===db?(n=pb(),We=4):We=n===hx?8:n!==null&&typeof n=="object"&&typeof n.then=="function"?6:1,sn=n,Re===null&&(st=1,Vu(e,yn(n,e.current)))}function t2(){var e=Y.H;return Y.H=Kn,e===null?Kn:e}function n2(){var e=Y.A;return Y.A=Wj,e}function em(){st=4,xa||(Le&4194176)!==Le&&bn.current!==null||(hr=!0),(ui&134217727)===0&&(ls&134217727)===0||Ze===null||wa(Ze,Le,En,!1)}function tm(e,n,i){var l=tt;tt|=2;var u=t2(),f=n2();(Ze!==e||Le!==n)&&(qu=null,pr(e,n)),n=!1;var g=st;e:do try{if(We!==0&&Re!==null){var x=Re,T=sn;switch(We){case 8:Ih(),g=6;break e;case 3:case 2:case 6:bn.current===null&&(n=!0);var M=We;if(We=0,sn=null,yr(e,x,T,M),i&&hr){g=0;break e}break;default:M=We,We=0,sn=null,yr(e,x,T,M)}}Ij(),g=st;break}catch(F){e2(e,F)}while(!0);return n&&e.shellSuspendCounter++,ma=as=null,tt=l,Y.H=u,Y.A=f,Re===null&&(Ze=null,Le=0,mu()),g}function Ij(){for(;Re!==null;)a2(Re)}function e9(e,n){var i=tt;tt|=2;var l=t2(),u=n2();Ze!==e||Le!==n?(qu=null,Pu=ce()+500,pr(e,n)):hr=ll(e,n);e:do try{if(We!==0&&Re!==null){n=Re;var f=sn;t:switch(We){case 1:We=0,sn=null,yr(e,n,f,1);break;case 2:if(hb(f)){We=0,sn=null,i2(n);break}n=function(){We===2&&Ze===e&&(We=7),Wn(e)},f.then(n,n);break e;case 3:We=7;break e;case 4:We=5;break e;case 7:hb(f)?(We=0,sn=null,i2(n)):(We=0,sn=null,yr(e,n,f,7));break;case 5:var g=null;switch(Re.tag){case 26:g=Re.memoizedState;case 5:case 27:var x=Re;if(!g||O2(g)){We=0,sn=null;var T=x.sibling;if(T!==null)Re=T;else{var M=x.return;M!==null?(Re=M,Yu(M)):Re=null}break t}}We=0,sn=null,yr(e,n,f,5);break;case 6:We=0,sn=null,yr(e,n,f,6);break;case 8:Ih(),st=6;break e;default:throw Error(r(462))}}t9();break}catch(F){e2(e,F)}while(!0);return ma=as=null,Y.H=l,Y.A=u,tt=i,Re!==null?0:(Ze=null,Le=0,mu(),st)}function t9(){for(;Re!==null&&!ee();)a2(Re)}function a2(e){var n=Cx(e.alternate,e,Sa);e.memoizedProps=e.pendingProps,n===null?Yu(e):Re=n}function i2(e){var n=e,i=n.alternate;switch(n.tag){case 15:case 0:n=bx(i,n,n.pendingProps,n.type,void 0,Le);break;case 11:n=bx(i,n,n.pendingProps,n.type.render,n.ref,Le);break;case 5:sh(n);default:Wx(i,n),n=Re=Xx(n,Sa),n=Cx(i,n,Sa)}e.memoizedProps=e.pendingProps,n===null?Yu(e):Re=n}function yr(e,n,i,l){ma=as=null,sh(n),ar=null,Cl=0;var u=n.return;try{if(Fj(e,u,n,i,Le)){st=1,Vu(e,yn(i,e.current)),Re=null;return}}catch(f){if(u!==null)throw Re=u,f;st=1,Vu(e,yn(i,e.current)),Re=null;return}n.flags&32768?(Ue||l===1?e=!0:hr||(Le&536870912)!==0?e=!1:(xa=e=!0,(l===2||l===3||l===6)&&(l=bn.current,l!==null&&l.tag===13&&(l.flags|=16384))),s2(n,e)):Yu(n)}function Yu(e){var n=e;do{if((n.flags&32768)!==0){s2(n,xa);return}e=n.return;var i=Kj(n.alternate,n,Sa);if(i!==null){Re=i;return}if(n=n.sibling,n!==null){Re=n;return}Re=n=e}while(n!==null);st===0&&(st=5)}function s2(e,n){do{var i=Zj(e.alternate,e);if(i!==null){i.flags&=32767,Re=i;return}if(i=e.return,i!==null&&(i.flags|=32768,i.subtreeFlags=0,i.deletions=null),!n&&(e=e.sibling,e!==null)){Re=e;return}Re=e=i}while(e!==null);st=6,Re=null}function r2(e,n,i,l,u,f,g,x,T,M){var F=Y.T,Q=ie.p;try{ie.p=2,Y.T=null,n9(e,n,i,l,Q,u,f,g,x,T,M)}finally{Y.T=F,ie.p=Q}}function n9(e,n,i,l,u,f,g,x){do gr();while(os!==null);if((tt&6)!==0)throw Error(r(327));var T=e.finishedWork;if(l=e.finishedLanes,T===null)return null;if(e.finishedWork=null,e.finishedLanes=0,T===e.current)throw Error(r(177));e.callbackNode=null,e.callbackPriority=0,e.cancelPendingCommit=null;var M=T.lanes|T.childLanes;if(M|=Gd,V8(e,l,M,f,g,x),e===Ze&&(Re=Ze=null,Le=0),(T.subtreeFlags&10256)===0&&(T.flags&10256)===0||Fu||(Fu=!0,Zh=M,Wh=i,r9(It,function(){return gr(),null})),i=(T.flags&15990)!==0,(T.subtreeFlags&15990)!==0||i?(i=Y.T,Y.T=null,f=ie.p,ie.p=2,g=tt,tt|=4,Yj(e,T),Hx(T,e),jj(hm,e.containerInfo),ac=!!dm,hm=dm=null,e.current=T,kx(e,T.alternate,T),I(),tt=g,ie.p=f,Y.T=i):e.current=T,Fu?(Fu=!1,os=e,Fl=l):l2(e,M),M=e.pendingLanes,M===0&&(ci=null),Io(T.stateNode),Wn(e),n!==null)for(u=e.onRecoverableError,T=0;T<n.length;T++)M=n[T],u(M.value,{componentStack:M.stack});return(Fl&3)!==0&&gr(),M=e.pendingLanes,(l&4194218)!==0&&(M&42)!==0?e===$h?Gl++:(Gl=0,$h=e):Gl=0,Yl(0),null}function l2(e,n){(e.pooledCacheLanes&=n)===0&&(n=e.pooledCache,n!=null&&(e.pooledCache=null,Al(n)))}function gr(){if(os!==null){var e=os,n=Zh;Zh=0;var i=y1(Fl),l=Y.T,u=ie.p;try{if(ie.p=32>i?32:i,Y.T=null,os===null)var f=!1;else{i=Wh,Wh=null;var g=os,x=Fl;if(os=null,Fl=0,(tt&6)!==0)throw Error(r(331));var T=tt;if(tt|=4,Yx(g.current),qx(g,g.current,x,i),tt=T,Yl(0,!1),qt&&typeof qt.onPostCommitFiberRoot=="function")try{qt.onPostCommitFiberRoot(Qa,g)}catch{}f=!0}return f}finally{ie.p=u,Y.T=l,l2(e,n)}}return!1}function o2(e,n,i){n=yn(i,n),n=bh(e.stateNode,n,2),e=ii(e,n,2),e!==null&&(ol(e,2),Wn(e))}function Xe(e,n,i){if(e.tag===3)o2(e,e,i);else for(;n!==null;){if(n.tag===3){o2(n,e,i);break}else if(n.tag===1){var l=n.stateNode;if(typeof n.type.getDerivedStateFromError=="function"||typeof l.componentDidCatch=="function"&&(ci===null||!ci.has(l))){e=yn(i,e),i=fx(2),l=ii(n,i,2),l!==null&&(dx(i,l,n,e),ol(l,2),Wn(l));break}}n=n.return}}function nm(e,n,i){var l=e.pingCache;if(l===null){l=e.pingCache=new $j;var u=new Set;l.set(n,u)}else u=l.get(n),u===void 0&&(u=new Set,l.set(n,u));u.has(i)||(Yh=!0,u.add(i),e=a9.bind(null,e,n,i),n.then(e,e))}function a9(e,n,i){var l=e.pingCache;l!==null&&l.delete(n),e.pingedLanes|=e.suspendedLanes&i,e.warmLanes&=~i,Ze===e&&(Le&i)===i&&(st===4||st===3&&(Le&62914560)===Le&&300>ce()-Kh?(tt&2)===0&&pr(e,0):Qh|=i,mr===Le&&(mr=0)),Wn(e)}function u2(e,n){n===0&&(n=h1()),e=Wa(e,n),e!==null&&(ol(e,n),Wn(e))}function i9(e){var n=e.memoizedState,i=0;n!==null&&(i=n.retryLane),u2(e,i)}function s9(e,n){var i=0;switch(e.tag){case 13:var l=e.stateNode,u=e.memoizedState;u!==null&&(i=u.retryLane);break;case 19:l=e.stateNode;break;case 22:l=e.stateNode._retryCache;break;default:throw Error(r(314))}l!==null&&l.delete(n),u2(e,i)}function r9(e,n){return P(e,n)}var Qu=null,vr=null,am=!1,Xu=!1,im=!1,us=0;function Wn(e){e!==vr&&e.next===null&&(vr===null?Qu=vr=e:vr=vr.next=e),Xu=!0,am||(am=!0,o9(l9))}function Yl(e,n){if(!im&&Xu){im=!0;do for(var i=!1,l=Qu;l!==null;){if(e!==0){var u=l.pendingLanes;if(u===0)var f=0;else{var g=l.suspendedLanes,x=l.pingedLanes;f=(1<<31-en(42|e)+1)-1,f&=u&~(g&~x),f=f&201326677?f&201326677|1:f?f|2:0}f!==0&&(i=!0,d2(l,f))}else f=Le,f=nu(l,l===Ze?f:0),(f&3)===0||ll(l,f)||(i=!0,d2(l,f));l=l.next}while(i);im=!1}}function l9(){Xu=am=!1;var e=0;us!==0&&(y9()&&(e=us),us=0);for(var n=ce(),i=null,l=Qu;l!==null;){var u=l.next,f=c2(l,n);f===0?(l.next=null,i===null?Qu=u:i.next=u,u===null&&(vr=i)):(i=l,(e!==0||(f&3)!==0)&&(Xu=!0)),l=u}Yl(e)}function c2(e,n){for(var i=e.suspendedLanes,l=e.pingedLanes,u=e.expirationTimes,f=e.pendingLanes&-62914561;0<f;){var g=31-en(f),x=1<<g,T=u[g];T===-1?((x&i)===0||(x&l)!==0)&&(u[g]=O8(x,n)):T<=n&&(e.expiredLanes|=x),f&=~x}if(n=Ze,i=Le,i=nu(e,e===n?i:0),l=e.callbackNode,i===0||e===n&&We===2||e.cancelPendingCommit!==null)return l!==null&&l!==null&&ae(l),e.callbackNode=null,e.callbackPriority=0;if((i&3)===0||ll(e,i)){if(n=i&-i,n===e.callbackPriority)return n;switch(l!==null&&ae(l),y1(i)){case 2:case 8:i=ft;break;case 32:i=It;break;case 268435456:i=Ps;break;default:i=It}return l=f2.bind(null,e),i=P(i,l),e.callbackPriority=n,e.callbackNode=i,n}return l!==null&&l!==null&&ae(l),e.callbackPriority=2,e.callbackNode=null,2}function f2(e,n){var i=e.callbackNode;if(gr()&&e.callbackNode!==i)return null;var l=Le;return l=nu(e,e===Ze?l:0),l===0?null:(Jx(e,l,n),c2(e,ce()),e.callbackNode!=null&&e.callbackNode===i?f2.bind(null,e):null)}function d2(e,n){if(gr())return null;Jx(e,n,!0)}function o9(e){v9(function(){(tt&6)!==0?P(ct,e):e()})}function sm(){return us===0&&(us=d1()),us}function h2(e){return e==null||typeof e=="symbol"||typeof e=="boolean"?null:typeof e=="function"?e:lu(""+e)}function m2(e,n){var i=n.ownerDocument.createElement("input");return i.name=n.name,i.value=n.value,e.id&&i.setAttribute("form",e.id),n.parentNode.insertBefore(i,n),e=new FormData(e),i.parentNode.removeChild(i),e}function u9(e,n,i,l,u){if(n==="submit"&&i&&i.stateNode===u){var f=h2((u[Kt]||null).action),g=l.submitter;g&&(n=(n=g[Kt]||null)?h2(n.formAction):g.getAttribute("formAction"),n!==null&&(f=n,g=null));var x=new fu("action","action",null,l,u);e.push({event:x,listeners:[{instance:null,listener:function(){if(l.defaultPrevented){if(us!==0){var T=g?m2(u,g):new FormData(u);mh(i,{pending:!0,data:T,method:u.method,action:f},null,T)}}else typeof f=="function"&&(x.preventDefault(),T=g?m2(u,g):new FormData(u),mh(i,{pending:!0,data:T,method:u.method,action:f},f,T))},currentTarget:u}]})}}for(var rm=0;rm<rb.length;rm++){var lm=rb[rm],c9=lm.toLowerCase(),f9=lm[0].toUpperCase()+lm.slice(1);Rn(c9,"on"+f9)}Rn(tb,"onAnimationEnd"),Rn(nb,"onAnimationIteration"),Rn(ab,"onAnimationStart"),Rn("dblclick","onDoubleClick"),Rn("focusin","onFocus"),Rn("focusout","onBlur"),Rn(Dj,"onTransitionRun"),Rn(_j,"onTransitionStart"),Rn(Mj,"onTransitionCancel"),Rn(ib,"onTransitionEnd"),Ys("onMouseEnter",["mouseout","mouseover"]),Ys("onMouseLeave",["mouseout","mouseover"]),Ys("onPointerEnter",["pointerout","pointerover"]),Ys("onPointerLeave",["pointerout","pointerover"]),Gi("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),Gi("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),Gi("onBeforeInput",["compositionend","keypress","textInput","paste"]),Gi("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),Gi("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),Gi("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Ql="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),d9=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(Ql));function p2(e,n){n=(n&4)!==0;for(var i=0;i<e.length;i++){var l=e[i],u=l.event;l=l.listeners;e:{var f=void 0;if(n)for(var g=l.length-1;0<=g;g--){var x=l[g],T=x.instance,M=x.currentTarget;if(x=x.listener,T!==f&&u.isPropagationStopped())break e;f=x,u.currentTarget=M;try{f(u)}catch(F){Ou(F)}u.currentTarget=null,f=T}else for(g=0;g<l.length;g++){if(x=l[g],T=x.instance,M=x.currentTarget,x=x.listener,T!==f&&u.isPropagationStopped())break e;f=x,u.currentTarget=M;try{f(u)}catch(F){Ou(F)}u.currentTarget=null,f=T}}}}function ke(e,n){var i=n[Ed];i===void 0&&(i=n[Ed]=new Set);var l=e+"__bubble";i.has(l)||(y2(n,e,2,!1),i.add(l))}function om(e,n,i){var l=0;n&&(l|=4),y2(i,e,l,n)}var Ku="_reactListening"+Math.random().toString(36).slice(2);function um(e){if(!e[Ku]){e[Ku]=!0,b1.forEach(function(i){i!=="selectionchange"&&(d9.has(i)||om(i,!1,e),om(i,!0,e))});var n=e.nodeType===9?e:e.ownerDocument;n===null||n[Ku]||(n[Ku]=!0,om("selectionchange",!1,n))}}function y2(e,n,i,l){switch(B2(n)){case 2:var u=U9;break;case 8:u=B9;break;default:u=wm}i=u.bind(null,n,i,e),u=void 0,!Nd||n!=="touchstart"&&n!=="touchmove"&&n!=="wheel"||(u=!0),l?u!==void 0?e.addEventListener(n,i,{capture:!0,passive:u}):e.addEventListener(n,i,!0):u!==void 0?e.addEventListener(n,i,{passive:u}):e.addEventListener(n,i,!1)}function cm(e,n,i,l,u){var f=l;if((n&1)===0&&(n&2)===0&&l!==null)e:for(;;){if(l===null)return;var g=l.tag;if(g===3||g===4){var x=l.stateNode.containerInfo;if(x===u||x.nodeType===8&&x.parentNode===u)break;if(g===4)for(g=l.return;g!==null;){var T=g.tag;if((T===3||T===4)&&(T=g.stateNode.containerInfo,T===u||T.nodeType===8&&T.parentNode===u))return;g=g.return}for(;x!==null;){if(g=Fi(x),g===null)return;if(T=g.tag,T===5||T===6||T===26||T===27){l=f=g;continue e}x=x.parentNode}}l=l.return}N1(function(){var M=f,F=_d(i),Q=[];e:{var U=sb.get(e);if(U!==void 0){var q=fu,fe=e;switch(e){case"keypress":if(uu(i)===0)break e;case"keydown":case"keyup":q=sj;break;case"focusin":fe="focus",q=zd;break;case"focusout":fe="blur",q=zd;break;case"beforeblur":case"afterblur":q=zd;break;case"click":if(i.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":q=V1;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":q=X8;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":q=oj;break;case tb:case nb:case ab:q=W8;break;case ib:q=cj;break;case"scroll":case"scrollend":q=Y8;break;case"wheel":q=dj;break;case"copy":case"cut":case"paste":q=J8;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":q=k1;break;case"toggle":case"beforetoggle":q=mj}var xe=(n&4)!==0,rt=!xe&&(e==="scroll"||e==="scrollend"),V=xe?U!==null?U+"Capture":null:U;xe=[];for(var _=M,k;_!==null;){var G=_;if(k=G.stateNode,G=G.tag,G!==5&&G!==26&&G!==27||k===null||V===null||(G=fl(_,V),G!=null&&xe.push(Xl(_,G,k))),rt)break;_=_.return}0<xe.length&&(U=new q(U,fe,null,i,F),Q.push({event:U,listeners:xe}))}}if((n&7)===0){e:{if(U=e==="mouseover"||e==="pointerover",q=e==="mouseout"||e==="pointerout",U&&i!==Dd&&(fe=i.relatedTarget||i.fromElement)&&(Fi(fe)||fe[qs]))break e;if((q||U)&&(U=F.window===F?F:(U=F.ownerDocument)?U.defaultView||U.parentWindow:window,q?(fe=i.relatedTarget||i.toElement,q=M,fe=fe?Fi(fe):null,fe!==null&&(rt=re(fe),xe=fe.tag,fe!==rt||xe!==5&&xe!==27&&xe!==6)&&(fe=null)):(q=null,fe=M),q!==fe)){if(xe=V1,G="onMouseLeave",V="onMouseEnter",_="mouse",(e==="pointerout"||e==="pointerover")&&(xe=k1,G="onPointerLeave",V="onPointerEnter",_="pointer"),rt=q==null?U:cl(q),k=fe==null?U:cl(fe),U=new xe(G,_+"leave",q,i,F),U.target=rt,U.relatedTarget=k,G=null,Fi(F)===M&&(xe=new xe(V,_+"enter",fe,i,F),xe.target=k,xe.relatedTarget=rt,G=xe),rt=G,q&&fe)t:{for(xe=q,V=fe,_=0,k=xe;k;k=br(k))_++;for(k=0,G=V;G;G=br(G))k++;for(;0<_-k;)xe=br(xe),_--;for(;0<k-_;)V=br(V),k--;for(;_--;){if(xe===V||V!==null&&xe===V.alternate)break t;xe=br(xe),V=br(V)}xe=null}else xe=null;q!==null&&g2(Q,U,q,xe,!1),fe!==null&&rt!==null&&g2(Q,rt,fe,xe,!0)}}e:{if(U=M?cl(M):window,q=U.nodeName&&U.nodeName.toLowerCase(),q==="select"||q==="input"&&U.type==="file")var ue=G1;else if(q1(U))if(Y1)ue=Tj;else{ue=wj;var Ne=Sj}else q=U.nodeName,!q||q.toLowerCase()!=="input"||U.type!=="checkbox"&&U.type!=="radio"?M&&Ad(M.elementType)&&(ue=G1):ue=Ej;if(ue&&(ue=ue(e,M))){F1(Q,ue,i,F);break e}Ne&&Ne(e,U,M),e==="focusout"&&M&&U.type==="number"&&M.memoizedProps.value!=null&&jd(U,"number",U.value)}switch(Ne=M?cl(M):window,e){case"focusin":(q1(Ne)||Ne.contentEditable==="true")&&($s=Ne,Pd=M,bl=null);break;case"focusout":bl=Pd=$s=null;break;case"mousedown":qd=!0;break;case"contextmenu":case"mouseup":case"dragend":qd=!1,I1(Q,i,F);break;case"selectionchange":if(Aj)break;case"keydown":case"keyup":I1(Q,i,F)}var me;if(Ld)e:{switch(e){case"compositionstart":var ve="onCompositionStart";break e;case"compositionend":ve="onCompositionEnd";break e;case"compositionupdate":ve="onCompositionUpdate";break e}ve=void 0}else Ws?H1(e,i)&&(ve="onCompositionEnd"):e==="keydown"&&i.keyCode===229&&(ve="onCompositionStart");ve&&(L1&&i.locale!=="ko"&&(Ws||ve!=="onCompositionStart"?ve==="onCompositionEnd"&&Ws&&(me=R1()):(Za=F,Rd="value"in Za?Za.value:Za.textContent,Ws=!0)),Ne=Zu(M,ve),0<Ne.length&&(ve=new z1(ve,e,null,i,F),Q.push({event:ve,listeners:Ne}),me?ve.data=me:(me=P1(i),me!==null&&(ve.data=me)))),(me=yj?gj(e,i):vj(e,i))&&(ve=Zu(M,"onBeforeInput"),0<ve.length&&(Ne=new z1("onBeforeInput","beforeinput",null,i,F),Q.push({event:Ne,listeners:ve}),Ne.data=me)),u9(Q,e,M,i,F)}p2(Q,n)})}function Xl(e,n,i){return{instance:e,listener:n,currentTarget:i}}function Zu(e,n){for(var i=n+"Capture",l=[];e!==null;){var u=e,f=u.stateNode;u=u.tag,u!==5&&u!==26&&u!==27||f===null||(u=fl(e,i),u!=null&&l.unshift(Xl(e,u,f)),u=fl(e,n),u!=null&&l.push(Xl(e,u,f))),e=e.return}return l}function br(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5&&e.tag!==27);return e||null}function g2(e,n,i,l,u){for(var f=n._reactName,g=[];i!==null&&i!==l;){var x=i,T=x.alternate,M=x.stateNode;if(x=x.tag,T!==null&&T===l)break;x!==5&&x!==26&&x!==27||M===null||(T=M,u?(M=fl(i,f),M!=null&&g.unshift(Xl(i,M,T))):u||(M=fl(i,f),M!=null&&g.push(Xl(i,M,T)))),i=i.return}g.length!==0&&e.push({event:n,listeners:g})}var h9=/\r\n?/g,m9=/\u0000|\uFFFD/g;function v2(e){return(typeof e=="string"?e:""+e).replace(h9,`
`).replace(m9,"")}function b2(e,n){return n=v2(n),v2(e)===n}function Wu(){}function Qe(e,n,i,l,u,f){switch(i){case"children":typeof l=="string"?n==="body"||n==="textarea"&&l===""||Xs(e,l):(typeof l=="number"||typeof l=="bigint")&&n!=="body"&&Xs(e,""+l);break;case"className":iu(e,"class",l);break;case"tabIndex":iu(e,"tabindex",l);break;case"dir":case"role":case"viewBox":case"width":case"height":iu(e,i,l);break;case"style":_1(e,l,f);break;case"data":if(n!=="object"){iu(e,"data",l);break}case"src":case"href":if(l===""&&(n!=="a"||i!=="href")){e.removeAttribute(i);break}if(l==null||typeof l=="function"||typeof l=="symbol"||typeof l=="boolean"){e.removeAttribute(i);break}l=lu(""+l),e.setAttribute(i,l);break;case"action":case"formAction":if(typeof l=="function"){e.setAttribute(i,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof f=="function"&&(i==="formAction"?(n!=="input"&&Qe(e,n,"name",u.name,u,null),Qe(e,n,"formEncType",u.formEncType,u,null),Qe(e,n,"formMethod",u.formMethod,u,null),Qe(e,n,"formTarget",u.formTarget,u,null)):(Qe(e,n,"encType",u.encType,u,null),Qe(e,n,"method",u.method,u,null),Qe(e,n,"target",u.target,u,null)));if(l==null||typeof l=="symbol"||typeof l=="boolean"){e.removeAttribute(i);break}l=lu(""+l),e.setAttribute(i,l);break;case"onClick":l!=null&&(e.onclick=Wu);break;case"onScroll":l!=null&&ke("scroll",e);break;case"onScrollEnd":l!=null&&ke("scrollend",e);break;case"dangerouslySetInnerHTML":if(l!=null){if(typeof l!="object"||!("__html"in l))throw Error(r(61));if(i=l.__html,i!=null){if(u.children!=null)throw Error(r(60));e.innerHTML=i}}break;case"multiple":e.multiple=l&&typeof l!="function"&&typeof l!="symbol";break;case"muted":e.muted=l&&typeof l!="function"&&typeof l!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(l==null||typeof l=="function"||typeof l=="boolean"||typeof l=="symbol"){e.removeAttribute("xlink:href");break}i=lu(""+l),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",i);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":l!=null&&typeof l!="function"&&typeof l!="symbol"?e.setAttribute(i,""+l):e.removeAttribute(i);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":l&&typeof l!="function"&&typeof l!="symbol"?e.setAttribute(i,""):e.removeAttribute(i);break;case"capture":case"download":l===!0?e.setAttribute(i,""):l!==!1&&l!=null&&typeof l!="function"&&typeof l!="symbol"?e.setAttribute(i,l):e.removeAttribute(i);break;case"cols":case"rows":case"size":case"span":l!=null&&typeof l!="function"&&typeof l!="symbol"&&!isNaN(l)&&1<=l?e.setAttribute(i,l):e.removeAttribute(i);break;case"rowSpan":case"start":l==null||typeof l=="function"||typeof l=="symbol"||isNaN(l)?e.removeAttribute(i):e.setAttribute(i,l);break;case"popover":ke("beforetoggle",e),ke("toggle",e),au(e,"popover",l);break;case"xlinkActuate":oa(e,"http://www.w3.org/1999/xlink","xlink:actuate",l);break;case"xlinkArcrole":oa(e,"http://www.w3.org/1999/xlink","xlink:arcrole",l);break;case"xlinkRole":oa(e,"http://www.w3.org/1999/xlink","xlink:role",l);break;case"xlinkShow":oa(e,"http://www.w3.org/1999/xlink","xlink:show",l);break;case"xlinkTitle":oa(e,"http://www.w3.org/1999/xlink","xlink:title",l);break;case"xlinkType":oa(e,"http://www.w3.org/1999/xlink","xlink:type",l);break;case"xmlBase":oa(e,"http://www.w3.org/XML/1998/namespace","xml:base",l);break;case"xmlLang":oa(e,"http://www.w3.org/XML/1998/namespace","xml:lang",l);break;case"xmlSpace":oa(e,"http://www.w3.org/XML/1998/namespace","xml:space",l);break;case"is":au(e,"is",l);break;case"innerText":case"textContent":break;default:(!(2<i.length)||i[0]!=="o"&&i[0]!=="O"||i[1]!=="n"&&i[1]!=="N")&&(i=F8.get(i)||i,au(e,i,l))}}function fm(e,n,i,l,u,f){switch(i){case"style":_1(e,l,f);break;case"dangerouslySetInnerHTML":if(l!=null){if(typeof l!="object"||!("__html"in l))throw Error(r(61));if(i=l.__html,i!=null){if(u.children!=null)throw Error(r(60));e.innerHTML=i}}break;case"children":typeof l=="string"?Xs(e,l):(typeof l=="number"||typeof l=="bigint")&&Xs(e,""+l);break;case"onScroll":l!=null&&ke("scroll",e);break;case"onScrollEnd":l!=null&&ke("scrollend",e);break;case"onClick":l!=null&&(e.onclick=Wu);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!x1.hasOwnProperty(i))e:{if(i[0]==="o"&&i[1]==="n"&&(u=i.endsWith("Capture"),n=i.slice(2,u?i.length-7:void 0),f=e[Kt]||null,f=f!=null?f[i]:null,typeof f=="function"&&e.removeEventListener(n,f,u),typeof l=="function")){typeof f!="function"&&f!==null&&(i in e?e[i]=null:e.hasAttribute(i)&&e.removeAttribute(i)),e.addEventListener(n,l,u);break e}i in e?e[i]=l:l===!0?e.setAttribute(i,""):au(e,i,l)}}}function Mt(e,n,i){switch(n){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":ke("error",e),ke("load",e);var l=!1,u=!1,f;for(f in i)if(i.hasOwnProperty(f)){var g=i[f];if(g!=null)switch(f){case"src":l=!0;break;case"srcSet":u=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(r(137,n));default:Qe(e,n,f,g,i,null)}}u&&Qe(e,n,"srcSet",i.srcSet,i,null),l&&Qe(e,n,"src",i.src,i,null);return;case"input":ke("invalid",e);var x=f=g=u=null,T=null,M=null;for(l in i)if(i.hasOwnProperty(l)){var F=i[l];if(F!=null)switch(l){case"name":u=F;break;case"type":g=F;break;case"checked":T=F;break;case"defaultChecked":M=F;break;case"value":f=F;break;case"defaultValue":x=F;break;case"children":case"dangerouslySetInnerHTML":if(F!=null)throw Error(r(137,n));break;default:Qe(e,n,l,F,i,null)}}C1(e,f,x,T,M,g,u,!1),su(e);return;case"select":ke("invalid",e),l=g=f=null;for(u in i)if(i.hasOwnProperty(u)&&(x=i[u],x!=null))switch(u){case"value":f=x;break;case"defaultValue":g=x;break;case"multiple":l=x;default:Qe(e,n,u,x,i,null)}n=f,i=g,e.multiple=!!l,n!=null?Qs(e,!!l,n,!1):i!=null&&Qs(e,!!l,i,!0);return;case"textarea":ke("invalid",e),f=u=l=null;for(g in i)if(i.hasOwnProperty(g)&&(x=i[g],x!=null))switch(g){case"value":l=x;break;case"defaultValue":u=x;break;case"children":f=x;break;case"dangerouslySetInnerHTML":if(x!=null)throw Error(r(91));break;default:Qe(e,n,g,x,i,null)}A1(e,l,u,f),su(e);return;case"option":for(T in i)if(i.hasOwnProperty(T)&&(l=i[T],l!=null))switch(T){case"selected":e.selected=l&&typeof l!="function"&&typeof l!="symbol";break;default:Qe(e,n,T,l,i,null)}return;case"dialog":ke("cancel",e),ke("close",e);break;case"iframe":case"object":ke("load",e);break;case"video":case"audio":for(l=0;l<Ql.length;l++)ke(Ql[l],e);break;case"image":ke("error",e),ke("load",e);break;case"details":ke("toggle",e);break;case"embed":case"source":case"link":ke("error",e),ke("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(M in i)if(i.hasOwnProperty(M)&&(l=i[M],l!=null))switch(M){case"children":case"dangerouslySetInnerHTML":throw Error(r(137,n));default:Qe(e,n,M,l,i,null)}return;default:if(Ad(n)){for(F in i)i.hasOwnProperty(F)&&(l=i[F],l!==void 0&&fm(e,n,F,l,i,void 0));return}}for(x in i)i.hasOwnProperty(x)&&(l=i[x],l!=null&&Qe(e,n,x,l,i,null))}function p9(e,n,i,l){switch(n){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var u=null,f=null,g=null,x=null,T=null,M=null,F=null;for(q in i){var Q=i[q];if(i.hasOwnProperty(q)&&Q!=null)switch(q){case"checked":break;case"value":break;case"defaultValue":T=Q;default:l.hasOwnProperty(q)||Qe(e,n,q,null,l,Q)}}for(var U in l){var q=l[U];if(Q=i[U],l.hasOwnProperty(U)&&(q!=null||Q!=null))switch(U){case"type":f=q;break;case"name":u=q;break;case"checked":M=q;break;case"defaultChecked":F=q;break;case"value":g=q;break;case"defaultValue":x=q;break;case"children":case"dangerouslySetInnerHTML":if(q!=null)throw Error(r(137,n));break;default:q!==Q&&Qe(e,n,U,q,l,Q)}}Cd(e,g,x,T,M,F,f,u);return;case"select":q=g=x=U=null;for(f in i)if(T=i[f],i.hasOwnProperty(f)&&T!=null)switch(f){case"value":break;case"multiple":q=T;default:l.hasOwnProperty(f)||Qe(e,n,f,null,l,T)}for(u in l)if(f=l[u],T=i[u],l.hasOwnProperty(u)&&(f!=null||T!=null))switch(u){case"value":U=f;break;case"defaultValue":x=f;break;case"multiple":g=f;default:f!==T&&Qe(e,n,u,f,l,T)}n=x,i=g,l=q,U!=null?Qs(e,!!i,U,!1):!!l!=!!i&&(n!=null?Qs(e,!!i,n,!0):Qs(e,!!i,i?[]:"",!1));return;case"textarea":q=U=null;for(x in i)if(u=i[x],i.hasOwnProperty(x)&&u!=null&&!l.hasOwnProperty(x))switch(x){case"value":break;case"children":break;default:Qe(e,n,x,null,l,u)}for(g in l)if(u=l[g],f=i[g],l.hasOwnProperty(g)&&(u!=null||f!=null))switch(g){case"value":U=u;break;case"defaultValue":q=u;break;case"children":break;case"dangerouslySetInnerHTML":if(u!=null)throw Error(r(91));break;default:u!==f&&Qe(e,n,g,u,l,f)}j1(e,U,q);return;case"option":for(var fe in i)if(U=i[fe],i.hasOwnProperty(fe)&&U!=null&&!l.hasOwnProperty(fe))switch(fe){case"selected":e.selected=!1;break;default:Qe(e,n,fe,null,l,U)}for(T in l)if(U=l[T],q=i[T],l.hasOwnProperty(T)&&U!==q&&(U!=null||q!=null))switch(T){case"selected":e.selected=U&&typeof U!="function"&&typeof U!="symbol";break;default:Qe(e,n,T,U,l,q)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var xe in i)U=i[xe],i.hasOwnProperty(xe)&&U!=null&&!l.hasOwnProperty(xe)&&Qe(e,n,xe,null,l,U);for(M in l)if(U=l[M],q=i[M],l.hasOwnProperty(M)&&U!==q&&(U!=null||q!=null))switch(M){case"children":case"dangerouslySetInnerHTML":if(U!=null)throw Error(r(137,n));break;default:Qe(e,n,M,U,l,q)}return;default:if(Ad(n)){for(var rt in i)U=i[rt],i.hasOwnProperty(rt)&&U!==void 0&&!l.hasOwnProperty(rt)&&fm(e,n,rt,void 0,l,U);for(F in l)U=l[F],q=i[F],!l.hasOwnProperty(F)||U===q||U===void 0&&q===void 0||fm(e,n,F,U,l,q);return}}for(var V in i)U=i[V],i.hasOwnProperty(V)&&U!=null&&!l.hasOwnProperty(V)&&Qe(e,n,V,null,l,U);for(Q in l)U=l[Q],q=i[Q],!l.hasOwnProperty(Q)||U===q||U==null&&q==null||Qe(e,n,Q,U,l,q)}var dm=null,hm=null;function $u(e){return e.nodeType===9?e:e.ownerDocument}function x2(e){switch(e){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function S2(e,n){if(e===0)switch(n){case"svg":return 1;case"math":return 2;default:return 0}return e===1&&n==="foreignObject"?0:e}function mm(e,n){return e==="textarea"||e==="noscript"||typeof n.children=="string"||typeof n.children=="number"||typeof n.children=="bigint"||typeof n.dangerouslySetInnerHTML=="object"&&n.dangerouslySetInnerHTML!==null&&n.dangerouslySetInnerHTML.__html!=null}var pm=null;function y9(){var e=window.event;return e&&e.type==="popstate"?e===pm?!1:(pm=e,!0):(pm=null,!1)}var w2=typeof setTimeout=="function"?setTimeout:void 0,g9=typeof clearTimeout=="function"?clearTimeout:void 0,E2=typeof Promise=="function"?Promise:void 0,v9=typeof queueMicrotask=="function"?queueMicrotask:typeof E2<"u"?function(e){return E2.resolve(null).then(e).catch(b9)}:w2;function b9(e){setTimeout(function(){throw e})}function ym(e,n){var i=n,l=0;do{var u=i.nextSibling;if(e.removeChild(i),u&&u.nodeType===8)if(i=u.data,i==="/$"){if(l===0){e.removeChild(u),to(n);return}l--}else i!=="$"&&i!=="$?"&&i!=="$!"||l++;i=u}while(i);to(n)}function gm(e){var n=e.firstChild;for(n&&n.nodeType===10&&(n=n.nextSibling);n;){var i=n;switch(n=n.nextSibling,i.nodeName){case"HTML":case"HEAD":case"BODY":gm(i),Td(i);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(i.rel.toLowerCase()==="stylesheet")continue}e.removeChild(i)}}function x9(e,n,i,l){for(;e.nodeType===1;){var u=i;if(e.nodeName.toLowerCase()!==n.toLowerCase()){if(!l&&(e.nodeName!=="INPUT"||e.type!=="hidden"))break}else if(l){if(!e[ul])switch(n){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if(f=e.getAttribute("rel"),f==="stylesheet"&&e.hasAttribute("data-precedence"))break;if(f!==u.rel||e.getAttribute("href")!==(u.href==null?null:u.href)||e.getAttribute("crossorigin")!==(u.crossOrigin==null?null:u.crossOrigin)||e.getAttribute("title")!==(u.title==null?null:u.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(f=e.getAttribute("src"),(f!==(u.src==null?null:u.src)||e.getAttribute("type")!==(u.type==null?null:u.type)||e.getAttribute("crossorigin")!==(u.crossOrigin==null?null:u.crossOrigin))&&f&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else if(n==="input"&&e.type==="hidden"){var f=u.name==null?null:""+u.name;if(u.type==="hidden"&&e.getAttribute("name")===f)return e}else return e;if(e=zn(e.nextSibling),e===null)break}return null}function S9(e,n,i){if(n==="")return null;for(;e.nodeType!==3;)if((e.nodeType!==1||e.nodeName!=="INPUT"||e.type!=="hidden")&&!i||(e=zn(e.nextSibling),e===null))return null;return e}function zn(e){for(;e!=null;e=e.nextSibling){var n=e.nodeType;if(n===1||n===3)break;if(n===8){if(n=e.data,n==="$"||n==="$!"||n==="$?"||n==="F!"||n==="F")break;if(n==="/$")return null}}return e}function T2(e){e=e.previousSibling;for(var n=0;e;){if(e.nodeType===8){var i=e.data;if(i==="$"||i==="$!"||i==="$?"){if(n===0)return e;n--}else i==="/$"&&n++}e=e.previousSibling}return null}function C2(e,n,i){switch(n=$u(i),e){case"html":if(e=n.documentElement,!e)throw Error(r(452));return e;case"head":if(e=n.head,!e)throw Error(r(453));return e;case"body":if(e=n.body,!e)throw Error(r(454));return e;default:throw Error(r(451))}}var Tn=new Map,j2=new Set;function Ju(e){return typeof e.getRootNode=="function"?e.getRootNode():e.ownerDocument}var Ea=ie.d;ie.d={f:w9,r:E9,D:T9,C:C9,L:j9,m:A9,X:_9,S:D9,M:M9};function w9(){var e=Ea.f(),n=Gu();return e||n}function E9(e){var n=Fs(e);n!==null&&n.tag===5&&n.type==="form"?Ib(n):Ea.r(e)}var xr=typeof document>"u"?null:document;function A2(e,n,i){var l=xr;if(l&&typeof n=="string"&&n){var u=mn(n);u='link[rel="'+e+'"][href="'+u+'"]',typeof i=="string"&&(u+='[crossorigin="'+i+'"]'),j2.has(u)||(j2.add(u),e={rel:e,crossOrigin:i,href:n},l.querySelector(u)===null&&(n=l.createElement("link"),Mt(n,"link",e),Et(n),l.head.appendChild(n)))}}function T9(e){Ea.D(e),A2("dns-prefetch",e,null)}function C9(e,n){Ea.C(e,n),A2("preconnect",e,n)}function j9(e,n,i){Ea.L(e,n,i);var l=xr;if(l&&e&&n){var u='link[rel="preload"][as="'+mn(n)+'"]';n==="image"&&i&&i.imageSrcSet?(u+='[imagesrcset="'+mn(i.imageSrcSet)+'"]',typeof i.imageSizes=="string"&&(u+='[imagesizes="'+mn(i.imageSizes)+'"]')):u+='[href="'+mn(e)+'"]';var f=u;switch(n){case"style":f=Sr(e);break;case"script":f=wr(e)}Tn.has(f)||(e=te({rel:"preload",href:n==="image"&&i&&i.imageSrcSet?void 0:e,as:n},i),Tn.set(f,e),l.querySelector(u)!==null||n==="style"&&l.querySelector(Kl(f))||n==="script"&&l.querySelector(Zl(f))||(n=l.createElement("link"),Mt(n,"link",e),Et(n),l.head.appendChild(n)))}}function A9(e,n){Ea.m(e,n);var i=xr;if(i&&e){var l=n&&typeof n.as=="string"?n.as:"script",u='link[rel="modulepreload"][as="'+mn(l)+'"][href="'+mn(e)+'"]',f=u;switch(l){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":f=wr(e)}if(!Tn.has(f)&&(e=te({rel:"modulepreload",href:e},n),Tn.set(f,e),i.querySelector(u)===null)){switch(l){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(i.querySelector(Zl(f)))return}l=i.createElement("link"),Mt(l,"link",e),Et(l),i.head.appendChild(l)}}}function D9(e,n,i){Ea.S(e,n,i);var l=xr;if(l&&e){var u=Gs(l).hoistableStyles,f=Sr(e);n=n||"default";var g=u.get(f);if(!g){var x={loading:0,preload:null};if(g=l.querySelector(Kl(f)))x.loading=5;else{e=te({rel:"stylesheet",href:e,"data-precedence":n},i),(i=Tn.get(f))&&vm(e,i);var T=g=l.createElement("link");Et(T),Mt(T,"link",e),T._p=new Promise(function(M,F){T.onload=M,T.onerror=F}),T.addEventListener("load",function(){x.loading|=1}),T.addEventListener("error",function(){x.loading|=2}),x.loading|=4,Iu(g,n,l)}g={type:"stylesheet",instance:g,count:1,state:x},u.set(f,g)}}}function _9(e,n){Ea.X(e,n);var i=xr;if(i&&e){var l=Gs(i).hoistableScripts,u=wr(e),f=l.get(u);f||(f=i.querySelector(Zl(u)),f||(e=te({src:e,async:!0},n),(n=Tn.get(u))&&bm(e,n),f=i.createElement("script"),Et(f),Mt(f,"link",e),i.head.appendChild(f)),f={type:"script",instance:f,count:1,state:null},l.set(u,f))}}function M9(e,n){Ea.M(e,n);var i=xr;if(i&&e){var l=Gs(i).hoistableScripts,u=wr(e),f=l.get(u);f||(f=i.querySelector(Zl(u)),f||(e=te({src:e,async:!0,type:"module"},n),(n=Tn.get(u))&&bm(e,n),f=i.createElement("script"),Et(f),Mt(f,"link",e),i.head.appendChild(f)),f={type:"script",instance:f,count:1,state:null},l.set(u,f))}}function D2(e,n,i,l){var u=(u=Nn.current)?Ju(u):null;if(!u)throw Error(r(446));switch(e){case"meta":case"title":return null;case"style":return typeof i.precedence=="string"&&typeof i.href=="string"?(n=Sr(i.href),i=Gs(u).hoistableStyles,l=i.get(n),l||(l={type:"style",instance:null,count:0,state:null},i.set(n,l)),l):{type:"void",instance:null,count:0,state:null};case"link":if(i.rel==="stylesheet"&&typeof i.href=="string"&&typeof i.precedence=="string"){e=Sr(i.href);var f=Gs(u).hoistableStyles,g=f.get(e);if(g||(u=u.ownerDocument||u,g={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},f.set(e,g),(f=u.querySelector(Kl(e)))&&!f._p&&(g.instance=f,g.state.loading=5),Tn.has(e)||(i={rel:"preload",as:"style",href:i.href,crossOrigin:i.crossOrigin,integrity:i.integrity,media:i.media,hrefLang:i.hrefLang,referrerPolicy:i.referrerPolicy},Tn.set(e,i),f||N9(u,e,i,g.state))),n&&l===null)throw Error(r(528,""));return g}if(n&&l!==null)throw Error(r(529,""));return null;case"script":return n=i.async,i=i.src,typeof i=="string"&&n&&typeof n!="function"&&typeof n!="symbol"?(n=wr(i),i=Gs(u).hoistableScripts,l=i.get(n),l||(l={type:"script",instance:null,count:0,state:null},i.set(n,l)),l):{type:"void",instance:null,count:0,state:null};default:throw Error(r(444,e))}}function Sr(e){return'href="'+mn(e)+'"'}function Kl(e){return'link[rel="stylesheet"]['+e+"]"}function _2(e){return te({},e,{"data-precedence":e.precedence,precedence:null})}function N9(e,n,i,l){e.querySelector('link[rel="preload"][as="style"]['+n+"]")?l.loading=1:(n=e.createElement("link"),l.preload=n,n.addEventListener("load",function(){return l.loading|=1}),n.addEventListener("error",function(){return l.loading|=2}),Mt(n,"link",i),Et(n),e.head.appendChild(n))}function wr(e){return'[src="'+mn(e)+'"]'}function Zl(e){return"script[async]"+e}function M2(e,n,i){if(n.count++,n.instance===null)switch(n.type){case"style":var l=e.querySelector('style[data-href~="'+mn(i.href)+'"]');if(l)return n.instance=l,Et(l),l;var u=te({},i,{"data-href":i.href,"data-precedence":i.precedence,href:null,precedence:null});return l=(e.ownerDocument||e).createElement("style"),Et(l),Mt(l,"style",u),Iu(l,i.precedence,e),n.instance=l;case"stylesheet":u=Sr(i.href);var f=e.querySelector(Kl(u));if(f)return n.state.loading|=4,n.instance=f,Et(f),f;l=_2(i),(u=Tn.get(u))&&vm(l,u),f=(e.ownerDocument||e).createElement("link"),Et(f);var g=f;return g._p=new Promise(function(x,T){g.onload=x,g.onerror=T}),Mt(f,"link",l),n.state.loading|=4,Iu(f,i.precedence,e),n.instance=f;case"script":return f=wr(i.src),(u=e.querySelector(Zl(f)))?(n.instance=u,Et(u),u):(l=i,(u=Tn.get(f))&&(l=te({},i),bm(l,u)),e=e.ownerDocument||e,u=e.createElement("script"),Et(u),Mt(u,"link",l),e.head.appendChild(u),n.instance=u);case"void":return null;default:throw Error(r(443,n.type))}else n.type==="stylesheet"&&(n.state.loading&4)===0&&(l=n.instance,n.state.loading|=4,Iu(l,i.precedence,e));return n.instance}function Iu(e,n,i){for(var l=i.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),u=l.length?l[l.length-1]:null,f=u,g=0;g<l.length;g++){var x=l[g];if(x.dataset.precedence===n)f=x;else if(f!==u)break}f?f.parentNode.insertBefore(e,f.nextSibling):(n=i.nodeType===9?i.head:i,n.insertBefore(e,n.firstChild))}function vm(e,n){e.crossOrigin==null&&(e.crossOrigin=n.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=n.referrerPolicy),e.title==null&&(e.title=n.title)}function bm(e,n){e.crossOrigin==null&&(e.crossOrigin=n.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=n.referrerPolicy),e.integrity==null&&(e.integrity=n.integrity)}var ec=null;function N2(e,n,i){if(ec===null){var l=new Map,u=ec=new Map;u.set(i,l)}else u=ec,l=u.get(i),l||(l=new Map,u.set(i,l));if(l.has(e))return l;for(l.set(e,null),i=i.getElementsByTagName(e),u=0;u<i.length;u++){var f=i[u];if(!(f[ul]||f[zt]||e==="link"&&f.getAttribute("rel")==="stylesheet")&&f.namespaceURI!=="http://www.w3.org/2000/svg"){var g=f.getAttribute(n)||"";g=e+g;var x=l.get(g);x?x.push(f):l.set(g,[f])}}return l}function R2(e,n,i){e=e.ownerDocument||e,e.head.insertBefore(i,n==="title"?e.querySelector("head > title"):null)}function R9(e,n,i){if(i===1||n.itemProp!=null)return!1;switch(e){case"meta":case"title":return!0;case"style":if(typeof n.precedence!="string"||typeof n.href!="string"||n.href==="")break;return!0;case"link":if(typeof n.rel!="string"||typeof n.href!="string"||n.href===""||n.onLoad||n.onError)break;switch(n.rel){case"stylesheet":return e=n.disabled,typeof n.precedence=="string"&&e==null;default:return!0}case"script":if(n.async&&typeof n.async!="function"&&typeof n.async!="symbol"&&!n.onLoad&&!n.onError&&n.src&&typeof n.src=="string")return!0}return!1}function O2(e){return!(e.type==="stylesheet"&&(e.state.loading&3)===0)}var Wl=null;function O9(){}function V9(e,n,i){if(Wl===null)throw Error(r(475));var l=Wl;if(n.type==="stylesheet"&&(typeof i.media!="string"||matchMedia(i.media).matches!==!1)&&(n.state.loading&4)===0){if(n.instance===null){var u=Sr(i.href),f=e.querySelector(Kl(u));if(f){e=f._p,e!==null&&typeof e=="object"&&typeof e.then=="function"&&(l.count++,l=tc.bind(l),e.then(l,l)),n.state.loading|=4,n.instance=f,Et(f);return}f=e.ownerDocument||e,i=_2(i),(u=Tn.get(u))&&vm(i,u),f=f.createElement("link"),Et(f);var g=f;g._p=new Promise(function(x,T){g.onload=x,g.onerror=T}),Mt(f,"link",i),n.instance=f}l.stylesheets===null&&(l.stylesheets=new Map),l.stylesheets.set(n,e),(e=n.state.preload)&&(n.state.loading&3)===0&&(l.count++,n=tc.bind(l),e.addEventListener("load",n),e.addEventListener("error",n))}}function z9(){if(Wl===null)throw Error(r(475));var e=Wl;return e.stylesheets&&e.count===0&&xm(e,e.stylesheets),0<e.count?function(n){var i=setTimeout(function(){if(e.stylesheets&&xm(e,e.stylesheets),e.unsuspend){var l=e.unsuspend;e.unsuspend=null,l()}},6e4);return e.unsuspend=n,function(){e.unsuspend=null,clearTimeout(i)}}:null}function tc(){if(this.count--,this.count===0){if(this.stylesheets)xm(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}}var nc=null;function xm(e,n){e.stylesheets=null,e.unsuspend!==null&&(e.count++,nc=new Map,n.forEach(k9,e),nc=null,tc.call(e))}function k9(e,n){if(!(n.state.loading&4)){var i=nc.get(e);if(i)var l=i.get(null);else{i=new Map,nc.set(e,i);for(var u=e.querySelectorAll("link[data-precedence],style[data-precedence]"),f=0;f<u.length;f++){var g=u[f];(g.nodeName==="LINK"||g.getAttribute("media")!=="not all")&&(i.set(g.dataset.precedence,g),l=g)}l&&i.set(null,l)}u=n.instance,g=u.getAttribute("data-precedence"),f=i.get(g)||l,f===l&&i.set(null,u),i.set(g,u),this.count++,l=tc.bind(this),u.addEventListener("load",l),u.addEventListener("error",l),f?f.parentNode.insertBefore(u,f.nextSibling):(e=e.nodeType===9?e.head:e,e.insertBefore(u,e.firstChild)),n.state.loading|=4}}var $l={$$typeof:E,Provider:null,Consumer:null,_currentValue:_e,_currentValue2:_e,_threadCount:0};function L9(e,n,i,l,u,f,g,x){this.tag=1,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=wd(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.finishedLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=wd(0),this.hiddenUpdates=wd(null),this.identifierPrefix=l,this.onUncaughtError=u,this.onCaughtError=f,this.onRecoverableError=g,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=x,this.incompleteTransitions=new Map}function V2(e,n,i,l,u,f,g,x,T,M,F,Q){return e=new L9(e,n,i,g,x,T,M,Q),n=1,f===!0&&(n|=24),f=wn(3,null,null,n),e.current=f,f.stateNode=e,n=Jd(),n.refCount++,e.pooledCache=n,n.refCount++,f.memoizedState={element:l,isDehydrated:i,cache:n},Rh(f),e}function z2(e){return e?(e=er,e):er}function k2(e,n,i,l,u,f){u=z2(u),l.context===null?l.context=u:l.pendingContext=u,l=ai(n),l.payload={element:i},f=f===void 0?null:f,f!==null&&(l.callback=f),i=ii(e,l,n),i!==null&&(Gt(i,e,n),Vl(i,e,n))}function L2(e,n){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var i=e.retryLane;e.retryLane=i!==0&&i<n?i:n}}function Sm(e,n){L2(e,n),(e=e.alternate)&&L2(e,n)}function U2(e){if(e.tag===13){var n=Wa(e,67108864);n!==null&&Gt(n,e,67108864),Sm(e,67108864)}}var ac=!0;function U9(e,n,i,l){var u=Y.T;Y.T=null;var f=ie.p;try{ie.p=2,wm(e,n,i,l)}finally{ie.p=f,Y.T=u}}function B9(e,n,i,l){var u=Y.T;Y.T=null;var f=ie.p;try{ie.p=8,wm(e,n,i,l)}finally{ie.p=f,Y.T=u}}function wm(e,n,i,l){if(ac){var u=Em(l);if(u===null)cm(e,n,l,ic,i),H2(e,l);else if(P9(u,e,n,i,l))l.stopPropagation();else if(H2(e,l),n&4&&-1<H9.indexOf(e)){for(;u!==null;){var f=Fs(u);if(f!==null)switch(f.tag){case 3:if(f=f.stateNode,f.current.memoizedState.isDehydrated){var g=qi(f.pendingLanes);if(g!==0){var x=f;for(x.pendingLanes|=2,x.entangledLanes|=2;g;){var T=1<<31-en(g);x.entanglements[1]|=T,g&=~T}Wn(f),(tt&6)===0&&(Pu=ce()+500,Yl(0))}}break;case 13:x=Wa(f,2),x!==null&&Gt(x,f,2),Gu(),Sm(f,2)}if(f=Em(l),f===null&&cm(e,n,l,ic,i),f===u)break;u=f}u!==null&&l.stopPropagation()}else cm(e,n,l,null,i)}}function Em(e){return e=_d(e),Tm(e)}var ic=null;function Tm(e){if(ic=null,e=Fi(e),e!==null){var n=re(e);if(n===null)e=null;else{var i=n.tag;if(i===13){if(e=De(n),e!==null)return e;e=null}else if(i===3){if(n.stateNode.current.memoizedState.isDehydrated)return n.tag===3?n.stateNode.containerInfo:null;e=null}else n!==e&&(e=null)}}return ic=e,null}function B2(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(Ee()){case ct:return 2;case ft:return 8;case It:case xd:return 32;case Ps:return 268435456;default:return 32}default:return 32}}var Cm=!1,fi=null,di=null,hi=null,Jl=new Map,Il=new Map,mi=[],H9="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function H2(e,n){switch(e){case"focusin":case"focusout":fi=null;break;case"dragenter":case"dragleave":di=null;break;case"mouseover":case"mouseout":hi=null;break;case"pointerover":case"pointerout":Jl.delete(n.pointerId);break;case"gotpointercapture":case"lostpointercapture":Il.delete(n.pointerId)}}function eo(e,n,i,l,u,f){return e===null||e.nativeEvent!==f?(e={blockedOn:n,domEventName:i,eventSystemFlags:l,nativeEvent:f,targetContainers:[u]},n!==null&&(n=Fs(n),n!==null&&U2(n)),e):(e.eventSystemFlags|=l,n=e.targetContainers,u!==null&&n.indexOf(u)===-1&&n.push(u),e)}function P9(e,n,i,l,u){switch(n){case"focusin":return fi=eo(fi,e,n,i,l,u),!0;case"dragenter":return di=eo(di,e,n,i,l,u),!0;case"mouseover":return hi=eo(hi,e,n,i,l,u),!0;case"pointerover":var f=u.pointerId;return Jl.set(f,eo(Jl.get(f)||null,e,n,i,l,u)),!0;case"gotpointercapture":return f=u.pointerId,Il.set(f,eo(Il.get(f)||null,e,n,i,l,u)),!0}return!1}function P2(e){var n=Fi(e.target);if(n!==null){var i=re(n);if(i!==null){if(n=i.tag,n===13){if(n=De(i),n!==null){e.blockedOn=n,z8(e.priority,function(){if(i.tag===13){var l=rn(),u=Wa(i,l);u!==null&&Gt(u,i,l),Sm(i,l)}});return}}else if(n===3&&i.stateNode.current.memoizedState.isDehydrated){e.blockedOn=i.tag===3?i.stateNode.containerInfo:null;return}}}e.blockedOn=null}function sc(e){if(e.blockedOn!==null)return!1;for(var n=e.targetContainers;0<n.length;){var i=Em(e.nativeEvent);if(i===null){i=e.nativeEvent;var l=new i.constructor(i.type,i);Dd=l,i.target.dispatchEvent(l),Dd=null}else return n=Fs(i),n!==null&&U2(n),e.blockedOn=i,!1;n.shift()}return!0}function q2(e,n,i){sc(e)&&i.delete(n)}function q9(){Cm=!1,fi!==null&&sc(fi)&&(fi=null),di!==null&&sc(di)&&(di=null),hi!==null&&sc(hi)&&(hi=null),Jl.forEach(q2),Il.forEach(q2)}function rc(e,n){e.blockedOn===n&&(e.blockedOn=null,Cm||(Cm=!0,t.unstable_scheduleCallback(t.unstable_NormalPriority,q9)))}var lc=null;function F2(e){lc!==e&&(lc=e,t.unstable_scheduleCallback(t.unstable_NormalPriority,function(){lc===e&&(lc=null);for(var n=0;n<e.length;n+=3){var i=e[n],l=e[n+1],u=e[n+2];if(typeof l!="function"){if(Tm(l||i)===null)continue;break}var f=Fs(i);f!==null&&(e.splice(n,3),n-=3,mh(f,{pending:!0,data:u,method:i.method,action:l},l,u))}}))}function to(e){function n(T){return rc(T,e)}fi!==null&&rc(fi,e),di!==null&&rc(di,e),hi!==null&&rc(hi,e),Jl.forEach(n),Il.forEach(n);for(var i=0;i<mi.length;i++){var l=mi[i];l.blockedOn===e&&(l.blockedOn=null)}for(;0<mi.length&&(i=mi[0],i.blockedOn===null);)P2(i),i.blockedOn===null&&mi.shift();if(i=(e.ownerDocument||e).$$reactFormReplay,i!=null)for(l=0;l<i.length;l+=3){var u=i[l],f=i[l+1],g=u[Kt]||null;if(typeof f=="function")g||F2(i);else if(g){var x=null;if(f&&f.hasAttribute("formAction")){if(u=f,g=f[Kt]||null)x=g.formAction;else if(Tm(u)!==null)continue}else x=g.action;typeof x=="function"?i[l+1]=x:(i.splice(l,3),l-=3),F2(i)}}}function jm(e){this._internalRoot=e}oc.prototype.render=jm.prototype.render=function(e){var n=this._internalRoot;if(n===null)throw Error(r(409));var i=n.current,l=rn();k2(i,l,e,n,null,null)},oc.prototype.unmount=jm.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var n=e.containerInfo;e.tag===0&&gr(),k2(e.current,2,null,e,null,null),Gu(),n[qs]=null}};function oc(e){this._internalRoot=e}oc.prototype.unstable_scheduleHydration=function(e){if(e){var n=g1();e={blockedOn:null,target:e,priority:n};for(var i=0;i<mi.length&&n!==0&&n<mi[i].priority;i++);mi.splice(i,0,e),i===0&&P2(e)}};var G2=a.version;if(G2!=="19.0.0")throw Error(r(527,G2,"19.0.0"));ie.findDOMNode=function(e){var n=e._reactInternals;if(n===void 0)throw typeof e.render=="function"?Error(r(188)):(e=Object.keys(e).join(","),Error(r(268,e)));return e=K(n),e=e!==null?de(e):null,e=e===null?null:e.stateNode,e};var F9={bundleType:0,version:"19.0.0",rendererPackageName:"react-dom",currentDispatcherRef:Y,findFiberByHostInstance:Fi,reconcilerVersion:"19.0.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var uc=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!uc.isDisabled&&uc.supportsFiber)try{Qa=uc.inject(F9),qt=uc}catch{}}return Dr.createRoot=function(e,n){if(!o(e))throw Error(r(299));var i=!1,l="",u=lx,f=ox,g=ux,x=null;return n!=null&&(n.unstable_strictMode===!0&&(i=!0),n.identifierPrefix!==void 0&&(l=n.identifierPrefix),n.onUncaughtError!==void 0&&(u=n.onUncaughtError),n.onCaughtError!==void 0&&(f=n.onCaughtError),n.onRecoverableError!==void 0&&(g=n.onRecoverableError),n.unstable_transitionCallbacks!==void 0&&(x=n.unstable_transitionCallbacks)),n=V2(e,1,!1,null,null,i,l,u,f,g,x,null),e[qs]=n.current,um(e.nodeType===8?e.parentNode:e),new jm(n)},Dr.hydrateRoot=function(e,n,i){if(!o(e))throw Error(r(299));var l=!1,u="",f=lx,g=ox,x=ux,T=null,M=null;return i!=null&&(i.unstable_strictMode===!0&&(l=!0),i.identifierPrefix!==void 0&&(u=i.identifierPrefix),i.onUncaughtError!==void 0&&(f=i.onUncaughtError),i.onCaughtError!==void 0&&(g=i.onCaughtError),i.onRecoverableError!==void 0&&(x=i.onRecoverableError),i.unstable_transitionCallbacks!==void 0&&(T=i.unstable_transitionCallbacks),i.formState!==void 0&&(M=i.formState)),n=V2(e,1,!0,n,i??null,l,u,f,g,x,T,M),n.context=z2(null),i=n.current,l=rn(),u=ai(l),u.callback=null,ii(i,u,l),n.current.lanes=l,ol(n,l),Wn(n),e[qs]=n.current,um(e),new oc(n)},Dr.version="19.0.0",Dr}var p0;function NS(){if(p0)return Tc.exports;p0=1;function t(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(t)}catch(a){console.error(a)}}return t(),Tc.exports=MS(),Tc.exports}var RS=NS();function OS(t,a){if(t instanceof RegExp)return{keys:!1,pattern:t};var s,r,o,c,d=[],h="",p=t.split("/");for(p[0]||p.shift();o=p.shift();)s=o[0],s==="*"?(d.push(s),h+=o[1]==="?"?"(?:/(.*))?":"/(.*)"):s===":"?(r=o.indexOf("?",1),c=o.indexOf(".",1),d.push(o.substring(1,~r?r:~c?c:o.length)),h+=~r&&!~c?"(?:/([^/]+?))?":"/([^/]+?)",~c&&(h+=(~r?"?":"")+"\\"+o.substring(c))):h+="/"+o;return{keys:d,pattern:new RegExp("^"+h+(a?"(?=$|/)":"/?$"),"i")}}var Dc={exports:{}},_c={};/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var y0;function VS(){if(y0)return _c;y0=1;var t=ro();function a(b,S){return b===S&&(b!==0||1/b===1/S)||b!==b&&S!==S}var s=typeof Object.is=="function"?Object.is:a,r=t.useState,o=t.useEffect,c=t.useLayoutEffect,d=t.useDebugValue;function h(b,S){var E=S(),N=r({inst:{value:E,getSnapshot:S}}),C=N[0].inst,R=N[1];return c(function(){C.value=E,C.getSnapshot=S,p(C)&&R({inst:C})},[b,E,S]),o(function(){return p(C)&&R({inst:C}),b(function(){p(C)&&R({inst:C})})},[b]),d(E),E}function p(b){var S=b.getSnapshot;b=b.value;try{var E=S();return!s(b,E)}catch{return!0}}function y(b,S){return S()}var v=typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"?y:h;return _c.useSyncExternalStore=t.useSyncExternalStore!==void 0?t.useSyncExternalStore:v,_c}var g0;function zS(){return g0||(g0=1,Dc.exports=VS()),Dc.exports}var v0=zS();const kS=a0.useInsertionEffect,LS=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u"?w.useLayoutEffect:w.useEffect,US=kS||LS,b0=t=>{const a=w.useRef([t,(...s)=>a[0](...s)]).current;return US(()=>{a[0]=t}),a[1]},BS="popstate",Mc="pushState",Nc="replaceState",x0=[BS,Mc,Nc,"hashchange"],HS=t=>{for(const a of x0)addEventListener(a,t);return()=>{for(const a of x0)removeEventListener(a,t)}},S0=(t,a)=>v0.useSyncExternalStore(HS,t,a),PS=()=>location.search,qS=({ssrSearch:t=""}={})=>S0(PS,()=>t),w0=()=>location.pathname,FS=({ssrPath:t}={})=>S0(w0,t?()=>t:w0),GS=(t,{replace:a=!1,state:s=null}={})=>history[a?Nc:Mc](s,"",t),YS=(t={})=>[FS(t),GS],E0=Symbol.for("wouter_v3");if(typeof history<"u"&&typeof window[E0]>"u"){for(const t of[Mc,Nc]){const a=history[t];history[t]=function(){const s=a.apply(this,arguments),r=new Event(t);return r.arguments=arguments,dispatchEvent(r),s}}Object.defineProperty(window,E0,{value:!0})}const QS=(t,a)=>a.toLowerCase().indexOf(t.toLowerCase())?"~"+a:a.slice(t.length)||"/",T0=(t="")=>t==="/"?"":t,XS=(t,a)=>t[0]==="~"?t.slice(1):T0(a)+t,KS=(t="",a)=>QS(C0(T0(t)),C0(a)),C0=t=>{try{return decodeURI(t)}catch{return t}},j0={hook:YS,searchHook:qS,parser:OS,base:"",ssrPath:void 0,ssrSearch:void 0,ssrContext:void 0,hrefs:t=>t},A0=w.createContext(j0),lo=()=>w.useContext(A0),D0={},_0=w.createContext(D0),ZS=()=>w.useContext(_0),Rc=t=>{const[a,s]=t.hook(t);return[KS(t.base,a),b0((r,o)=>s(XS(r,t.base),o))]},M0=(t,a,s,r)=>{const{pattern:o,keys:c}=a instanceof RegExp?{keys:!1,pattern:a}:t(a||"*",r),d=o.exec(s)||[],[h,...p]=d;return h!==void 0?[!0,(()=>{const y=c!==!1?Object.fromEntries(c.map((b,S)=>[b,p[S]])):d.groups;let v={...p};return y&&Object.assign(v,y),v})(),...r?[h]:[]]:[!1,null]},N0=({children:t,...a})=>{var v,b;const s=lo(),r=a.hook?j0:s;let o=r;const[c,d]=((v=a.ssrPath)==null?void 0:v.split("?"))??[];d&&(a.ssrSearch=d,a.ssrPath=c),a.hrefs=a.hrefs??((b=a.hook)==null?void 0:b.hrefs);let h=w.useRef({}),p=h.current,y=p;for(let S in r){const E=S==="base"?r[S]+(a[S]||""):a[S]||r[S];p===y&&E!==y[S]&&(h.current=y={...y}),y[S]=E,E!==r[S]&&(o=y)}return w.createElement(A0.Provider,{value:o,children:t})},R0=({children:t,component:a},s)=>a?w.createElement(a,{params:s}):typeof t=="function"?t(s):t,WS=t=>{let a=w.useRef(D0);const s=a.current;return a.current=Object.keys(t).length!==Object.keys(s).length||Object.entries(t).some(([r,o])=>o!==s[r])?t:s},ja=({path:t,nest:a,match:s,...r})=>{const o=lo(),[c]=Rc(o),[d,h,p]=s??M0(o.parser,t,c,a),y=WS({...ZS(),...h});if(!d)return null;const v=p?w.createElement(N0,{base:p},R0(r,y)):R0(r,y);return w.createElement(_0.Provider,{value:y,children:v})},$S=w.forwardRef((t,a)=>{const s=lo(),[r,o]=Rc(s),{to:c="",href:d=c,onClick:h,asChild:p,children:y,className:v,replace:b,state:S,...E}=t,N=b0(R=>{R.ctrlKey||R.metaKey||R.altKey||R.shiftKey||R.button!==0||(h==null||h(R),R.defaultPrevented||(R.preventDefault(),o(d,t)))}),C=s.hrefs(d[0]==="~"?d.slice(1):s.base+d,s);return p&&w.isValidElement(y)?w.cloneElement(y,{onClick:N,href:C}):w.createElement("a",{...E,onClick:N,href:C,className:v!=null&&v.call?v(r===d):v,children:y,ref:a})}),O0=t=>Array.isArray(t)?t.flatMap(a=>O0(a&&a.type===w.Fragment?a.props.children:a)):[t],JS=({children:t,location:a})=>{const s=lo(),[r]=Rc(s);for(const o of O0(t)){let c=0;if(w.isValidElement(o)&&(c=M0(s.parser,o.props.path,a||r,o.props.nest))[0])return w.cloneElement(o,{match:c})}return null},_r={v:[]},V0=()=>_r.v.forEach(t=>t()),IS=t=>(_r.v.push(t)===1&&addEventListener("hashchange",V0),()=>{_r.v=_r.v.filter(a=>a!==t),_r.v.length||removeEventListener("hashchange",V0)}),e6=()=>"/"+location.hash.replace(/^#?\/?/,""),t6=(t,{state:a=null}={})=>{const[s,r]=t.replace(/^#?\/?/,"").split("?");history.replaceState(a,"",location.pathname+(r?`?${r}`:location.search)+(location.hash=`#/${s}`))},oo=({ssrPath:t="/"}={})=>[v0.useSyncExternalStore(IS,e6,()=>t),t6];oo.hrefs=t=>"#"+t;const Oc=w.createContext({});function Vc(t){const a=w.useRef(null);return a.current===null&&(a.current=t()),a.current}const zc=typeof window<"u",z0=zc?w.useLayoutEffect:w.useEffect,uo=w.createContext(null),kc=w.createContext({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"});class n6 extends w.Component{getSnapshotBeforeUpdate(a){const s=this.props.childRef.current;if(s&&a.isPresent&&!this.props.isPresent){const r=s.offsetParent,o=r instanceof HTMLElement&&r.offsetWidth||0,c=this.props.sizeRef.current;c.height=s.offsetHeight||0,c.width=s.offsetWidth||0,c.top=s.offsetTop,c.left=s.offsetLeft,c.right=o-c.width-c.left}return null}componentDidUpdate(){}render(){return this.props.children}}function a6({children:t,isPresent:a,anchorX:s}){const r=w.useId(),o=w.useRef(null),c=w.useRef({width:0,height:0,top:0,left:0,right:0}),{nonce:d}=w.useContext(kc);return w.useInsertionEffect(()=>{const{width:h,height:p,top:y,left:v,right:b}=c.current;if(a||!o.current||!h||!p)return;const S=s==="left"?`left: ${v}`:`right: ${b}`;o.current.dataset.motionPopId=r;const E=document.createElement("style");return d&&(E.nonce=d),document.head.appendChild(E),E.sheet&&E.sheet.insertRule(`
          [data-motion-pop-id="${r}"] {
            position: absolute !important;
            width: ${h}px !important;
            height: ${p}px !important;
            ${S}px !important;
            top: ${y}px !important;
          }
        `),()=>{document.head.removeChild(E)}},[a]),m.jsx(n6,{isPresent:a,childRef:o,sizeRef:c,children:w.cloneElement(t,{ref:o})})}const i6=({children:t,initial:a,isPresent:s,onExitComplete:r,custom:o,presenceAffectsLayout:c,mode:d,anchorX:h})=>{const p=Vc(s6),y=w.useId(),v=w.useCallback(S=>{p.set(S,!0);for(const E of p.values())if(!E)return;r&&r()},[p,r]),b=w.useMemo(()=>({id:y,initial:a,isPresent:s,custom:o,onExitComplete:v,register:S=>(p.set(S,!1),()=>p.delete(S))}),c?[Math.random(),v]:[s,v]);return w.useMemo(()=>{p.forEach((S,E)=>p.set(E,!1))},[s]),w.useEffect(()=>{!s&&!p.size&&r&&r()},[s]),d==="popLayout"&&(t=m.jsx(a6,{isPresent:s,anchorX:h,children:t})),m.jsx(uo.Provider,{value:b,children:t})};function s6(){return new Map}function k0(t=!0){const a=w.useContext(uo);if(a===null)return[!0,null];const{isPresent:s,onExitComplete:r,register:o}=a,c=w.useId();w.useEffect(()=>{if(t)return o(c)},[t]);const d=w.useCallback(()=>t&&r&&r(c),[c,r,t]);return!s&&r?[!1,d]:[!0]}const co=t=>t.key||"";function L0(t){const a=[];return w.Children.forEach(t,s=>{w.isValidElement(s)&&a.push(s)}),a}const U0=({children:t,custom:a,initial:s=!0,onExitComplete:r,presenceAffectsLayout:o=!0,mode:c="sync",propagate:d=!1,anchorX:h="left"})=>{const[p,y]=k0(d),v=w.useMemo(()=>L0(t),[t]),b=d&&!p?[]:v.map(co),S=w.useRef(!0),E=w.useRef(v),N=Vc(()=>new Map),[C,R]=w.useState(v),[z,B]=w.useState(v);z0(()=>{S.current=!1,E.current=v;for(let O=0;O<z.length;O++){const X=co(z[O]);b.includes(X)?N.delete(X):N.get(X)!==!0&&N.set(X,!1)}},[z,b.length,b.join("-")]);const H=[];if(v!==C){let O=[...v];for(let X=0;X<z.length;X++){const J=z[X],Z=co(J);b.includes(Z)||(O.splice(X,0,J),H.push(J))}return c==="wait"&&H.length&&(O=H),B(L0(O)),R(v),null}const{forceRender:W}=w.useContext(Oc);return m.jsx(m.Fragment,{children:z.map(O=>{const X=co(O),J=d&&!p?!1:v===z||b.includes(X),Z=()=>{if(N.has(X))N.set(X,!0);else return;let Y=!0;N.forEach(te=>{te||(Y=!1)}),Y&&(W==null||W(),B(E.current),d&&(y==null||y()),r&&r())};return m.jsx(i6,{isPresent:J,initial:!S.current||s?void 0:!1,custom:a,presenceAffectsLayout:o,mode:c,onExitComplete:J?void 0:Z,anchorX:h,children:O},X)})})};function Lc(t,a){t.indexOf(a)===-1&&t.push(a)}function Uc(t,a){const s=t.indexOf(a);s>-1&&t.splice(s,1)}const $t=t=>t;let B0=$t;const r6={useManualTiming:!1};function Bc(t){let a;return()=>(a===void 0&&(a=t()),a)}const ds=(t,a,s)=>{const r=a-t;return r===0?1:(s-t)/r};class Hc{constructor(){this.subscriptions=[]}add(a){return Lc(this.subscriptions,a),()=>Uc(this.subscriptions,a)}notify(a,s,r){const o=this.subscriptions.length;if(o)if(o===1)this.subscriptions[0](a,s,r);else for(let c=0;c<o;c++){const d=this.subscriptions[c];d&&d(a,s,r)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const $n=t=>t*1e3,Jn=t=>t/1e3;function H0(t,a){return a?t*(1e3/a):0}const l6=Bc(()=>window.ScrollTimeline!==void 0);class o6{constructor(a){this.stop=()=>this.runAll("stop"),this.animations=a.filter(Boolean)}get finished(){return Promise.all(this.animations.map(a=>"finished"in a?a.finished:a))}getAll(a){return this.animations[0][a]}setAll(a,s){for(let r=0;r<this.animations.length;r++)this.animations[r][a]=s}attachTimeline(a,s){const r=this.animations.map(o=>{if(l6()&&o.attachTimeline)return o.attachTimeline(a);if(typeof s=="function")return s(o)});return()=>{r.forEach((o,c)=>{o&&o(),this.animations[c].stop()})}}get time(){return this.getAll("time")}set time(a){this.setAll("time",a)}get speed(){return this.getAll("speed")}set speed(a){this.setAll("speed",a)}get startTime(){return this.getAll("startTime")}get duration(){let a=0;for(let s=0;s<this.animations.length;s++)a=Math.max(a,this.animations[s].duration);return a}runAll(a){this.animations.forEach(s=>s[a]())}flatten(){this.runAll("flatten")}play(){this.runAll("play")}pause(){this.runAll("pause")}cancel(){this.runAll("cancel")}complete(){this.runAll("complete")}}class u6 extends o6{then(a,s){return Promise.all(this.animations).then(a).catch(s)}}function Pc(t,a){return t?t[a]||t.default||t:void 0}const qc=2e4;function P0(t){let a=0;const s=50;let r=t.next(a);for(;!r.done&&a<qc;)a+=s,r=t.next(a);return a>=qc?1/0:a}function Fc(t){return typeof t=="function"}function q0(t,a){t.timeline=a,t.onfinish=null}const Gc=t=>Array.isArray(t)&&typeof t[0]=="number",c6={linearEasing:void 0};function f6(t,a){const s=Bc(t);return()=>{var r;return(r=c6[a])!==null&&r!==void 0?r:s()}}const fo=f6(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch{return!1}return!0},"linearEasing"),F0=(t,a,s=10)=>{let r="";const o=Math.max(Math.round(a/s),2);for(let c=0;c<o;c++)r+=t(ds(0,o-1,c))+", ";return`linear(${r.substring(0,r.length-2)})`};function G0(t){return!!(typeof t=="function"&&fo()||!t||typeof t=="string"&&(t in Yc||fo())||Gc(t)||Array.isArray(t)&&t.every(G0))}const Mr=([t,a,s,r])=>`cubic-bezier(${t}, ${a}, ${s}, ${r})`,Yc={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:Mr([0,.65,.55,1]),circOut:Mr([.55,0,1,.45]),backIn:Mr([.31,.01,.66,-.59]),backOut:Mr([.33,1.53,.69,.99])};function Y0(t,a){if(t)return typeof t=="function"&&fo()?F0(t,a):Gc(t)?Mr(t):Array.isArray(t)?t.map(s=>Y0(s,a)||Yc.easeOut):Yc[t]}const ho=["read","resolveKeyframes","update","preRender","render","postRender"],Q0={value:null};function d6(t,a){let s=new Set,r=new Set,o=!1,c=!1;const d=new WeakSet;let h={delta:0,timestamp:0,isProcessing:!1},p=0;function y(b){d.has(b)&&(v.schedule(b),t()),p++,b(h)}const v={schedule:(b,S=!1,E=!1)=>{const C=E&&o?s:r;return S&&d.add(b),C.has(b)||C.add(b),b},cancel:b=>{r.delete(b),d.delete(b)},process:b=>{if(h=b,o){c=!0;return}o=!0,[s,r]=[r,s],s.forEach(y),a&&Q0.value&&Q0.value.frameloop[a].push(p),p=0,s.clear(),o=!1,c&&(c=!1,v.process(b))}};return v}const h6=40;function X0(t,a){let s=!1,r=!0;const o={delta:0,timestamp:0,isProcessing:!1},c=()=>s=!0,d=ho.reduce((z,B)=>(z[B]=d6(c,a?B:void 0),z),{}),{read:h,resolveKeyframes:p,update:y,preRender:v,render:b,postRender:S}=d,E=()=>{const z=performance.now();s=!1,o.delta=r?1e3/60:Math.max(Math.min(z-o.timestamp,h6),1),o.timestamp=z,o.isProcessing=!0,h.process(o),p.process(o),y.process(o),v.process(o),b.process(o),S.process(o),o.isProcessing=!1,s&&a&&(r=!1,t(E))},N=()=>{s=!0,r=!0,o.isProcessing||t(E)};return{schedule:ho.reduce((z,B)=>{const H=d[B];return z[B]=(W,O=!1,X=!1)=>(s||N(),H.schedule(W,O,X)),z},{}),cancel:z=>{for(let B=0;B<ho.length;B++)d[ho[B]].cancel(z)},state:o,steps:d}}const{schedule:Ke,cancel:Aa,state:At,steps:Qc}=X0(typeof requestAnimationFrame<"u"?requestAnimationFrame:$t,!0),{schedule:Xc}=X0(queueMicrotask,!1);let mo;function m6(){mo=void 0}const kn={now:()=>(mo===void 0&&kn.set(At.isProcessing||r6.useManualTiming?At.timestamp:performance.now()),mo),set:t=>{mo=t,queueMicrotask(m6)}},jn={x:!1,y:!1};function K0(){return jn.x||jn.y}function p6(t){return t==="x"||t==="y"?jn[t]?null:(jn[t]=!0,()=>{jn[t]=!1}):jn.x||jn.y?null:(jn.x=jn.y=!0,()=>{jn.x=jn.y=!1})}function y6(t,a,s){var r;if(t instanceof EventTarget)return[t];if(typeof t=="string"){let o=document;const c=(r=void 0)!==null&&r!==void 0?r:o.querySelectorAll(t);return c?Array.from(c):[]}return Array.from(t)}function Z0(t,a){const s=y6(t),r=new AbortController,o={passive:!0,...a,signal:r.signal};return[s,o,()=>r.abort()]}function W0(t){return!(t.pointerType==="touch"||K0())}function g6(t,a,s={}){const[r,o,c]=Z0(t,s),d=h=>{if(!W0(h))return;const{target:p}=h,y=a(p,h);if(typeof y!="function"||!p)return;const v=b=>{W0(b)&&(y(b),p.removeEventListener("pointerleave",v))};p.addEventListener("pointerleave",v,o)};return r.forEach(h=>{h.addEventListener("pointerenter",d,o)}),c}const $0=(t,a)=>a?t===a?!0:$0(t,a.parentElement):!1,Kc=t=>t.pointerType==="mouse"?typeof t.button!="number"||t.button<=0:t.isPrimary!==!1,v6=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]);function b6(t){return v6.has(t.tagName)||t.tabIndex!==-1}const Nr=new WeakSet;function J0(t){return a=>{a.key==="Enter"&&t(a)}}function Zc(t,a){t.dispatchEvent(new PointerEvent("pointer"+a,{isPrimary:!0,bubbles:!0}))}const x6=(t,a)=>{const s=t.currentTarget;if(!s)return;const r=J0(()=>{if(Nr.has(s))return;Zc(s,"down");const o=J0(()=>{Zc(s,"up")}),c=()=>Zc(s,"cancel");s.addEventListener("keyup",o,a),s.addEventListener("blur",c,a)});s.addEventListener("keydown",r,a),s.addEventListener("blur",()=>s.removeEventListener("keydown",r),a)};function I0(t){return Kc(t)&&!K0()}function S6(t,a,s={}){const[r,o,c]=Z0(t,s),d=h=>{const p=h.currentTarget;if(!I0(h)||Nr.has(p))return;Nr.add(p);const y=a(p,h),v=(E,N)=>{window.removeEventListener("pointerup",b),window.removeEventListener("pointercancel",S),!(!I0(E)||!Nr.has(p))&&(Nr.delete(p),typeof y=="function"&&y(E,{success:N}))},b=E=>{v(E,p===window||p===document||s.useGlobalTarget||$0(p,E.target))},S=E=>{v(E,!1)};window.addEventListener("pointerup",b,o),window.addEventListener("pointercancel",S,o)};return r.forEach(h=>{(s.useGlobalTarget?window:h).addEventListener("pointerdown",d,o),h instanceof HTMLElement&&(h.addEventListener("focus",y=>x6(y,o)),!b6(h)&&h.tabIndex===null&&(h.tabIndex=0))}),c}const ep=30,w6=t=>!isNaN(parseFloat(t));class E6{constructor(a,s={}){this.version="12.6.1",this.canTrackVelocity=null,this.events={},this.updateAndNotify=(r,o=!0)=>{const c=kn.now();this.updatedAt!==c&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(r),this.current!==this.prev&&this.events.change&&this.events.change.notify(this.current),o&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.hasAnimated=!1,this.setCurrent(a),this.owner=s.owner}setCurrent(a){this.current=a,this.updatedAt=kn.now(),this.canTrackVelocity===null&&a!==void 0&&(this.canTrackVelocity=w6(this.current))}setPrevFrameValue(a=this.current){this.prevFrameValue=a,this.prevUpdatedAt=this.updatedAt}onChange(a){return this.on("change",a)}on(a,s){this.events[a]||(this.events[a]=new Hc);const r=this.events[a].add(s);return a==="change"?()=>{r(),Ke.read(()=>{this.events.change.getSize()||this.stop()})}:r}clearListeners(){for(const a in this.events)this.events[a].clear()}attach(a,s){this.passiveEffect=a,this.stopPassiveEffect=s}set(a,s=!0){!s||!this.passiveEffect?this.updateAndNotify(a,s):this.passiveEffect(a,this.updateAndNotify)}setWithVelocity(a,s,r){this.set(s),this.prev=void 0,this.prevFrameValue=a,this.prevUpdatedAt=this.updatedAt-r}jump(a,s=!0){this.updateAndNotify(a),this.prev=a,this.prevUpdatedAt=this.prevFrameValue=void 0,s&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return this.current}getPrevious(){return this.prev}getVelocity(){const a=kn.now();if(!this.canTrackVelocity||this.prevFrameValue===void 0||a-this.updatedAt>ep)return 0;const s=Math.min(this.updatedAt-this.prevUpdatedAt,ep);return H0(parseFloat(this.current)-parseFloat(this.prevFrameValue),s)}start(a){return this.stop(),new Promise(s=>{this.hasAnimated=!0,this.animation=a(s),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function Rr(t,a){return new E6(t,a)}const tp=w.createContext({strict:!1}),np={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},hs={};for(const t in np)hs[t]={isEnabled:a=>np[t].some(s=>!!a[s])};function T6(t){for(const a in t)hs[a]={...hs[a],...t[a]}}const C6=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function po(t){return t.startsWith("while")||t.startsWith("drag")&&t!=="draggable"||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||C6.has(t)}let ap=t=>!po(t);function j6(t){t&&(ap=a=>a.startsWith("on")?!po(a):t(a))}try{j6(require("@emotion/is-prop-valid").default)}catch{}function A6(t,a,s){const r={};for(const o in t)o==="values"&&typeof t.values=="object"||(ap(o)||s===!0&&po(o)||!a&&!po(o)||t.draggable&&o.startsWith("onDrag"))&&(r[o]=t[o]);return r}function D6(t){if(typeof Proxy>"u")return t;const a=new Map,s=(...r)=>t(...r);return new Proxy(s,{get:(r,o)=>o==="create"?t:(a.has(o)||a.set(o,t(o)),a.get(o))})}const yo=w.createContext({});function go(t){return t!==null&&typeof t=="object"&&typeof t.start=="function"}function Or(t){return typeof t=="string"||Array.isArray(t)}const Wc=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],$c=["initial",...Wc];function vo(t){return go(t.animate)||$c.some(a=>Or(t[a]))}function ip(t){return!!(vo(t)||t.variants)}function _6(t,a){if(vo(t)){const{initial:s,animate:r}=t;return{initial:s===!1||Or(s)?s:void 0,animate:Or(r)?r:void 0}}return t.inherit!==!1?a:{}}function M6(t){const{initial:a,animate:s}=_6(t,w.useContext(yo));return w.useMemo(()=>({initial:a,animate:s}),[sp(a),sp(s)])}function sp(t){return Array.isArray(t)?t.join(" "):t}const N6=Symbol.for("motionComponentSymbol");function ms(t){return t&&typeof t=="object"&&Object.prototype.hasOwnProperty.call(t,"current")}function R6(t,a,s){return w.useCallback(r=>{r&&t.onMount&&t.onMount(r),a&&(r?a.mount(r):a.unmount()),s&&(typeof s=="function"?s(r):ms(s)&&(s.current=r))},[a])}const Jc=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),rp="data-"+Jc("framerAppearId"),lp=w.createContext({});function O6(t,a,s,r,o){var c,d;const{visualElement:h}=w.useContext(yo),p=w.useContext(tp),y=w.useContext(uo),v=w.useContext(kc).reducedMotion,b=w.useRef(null);r=r||p.renderer,!b.current&&r&&(b.current=r(t,{visualState:a,parent:h,props:s,presenceContext:y,blockInitialAnimation:y?y.initial===!1:!1,reducedMotionConfig:v}));const S=b.current,E=w.useContext(lp);S&&!S.projection&&o&&(S.type==="html"||S.type==="svg")&&V6(b.current,s,o,E);const N=w.useRef(!1);w.useInsertionEffect(()=>{S&&N.current&&S.update(s,y)});const C=s[rp],R=w.useRef(!!C&&!(!((c=window.MotionHandoffIsComplete)===null||c===void 0)&&c.call(window,C))&&((d=window.MotionHasOptimisedAnimation)===null||d===void 0?void 0:d.call(window,C)));return z0(()=>{S&&(N.current=!0,window.MotionIsMounted=!0,S.updateFeatures(),Xc.render(S.render),R.current&&S.animationState&&S.animationState.animateChanges())}),w.useEffect(()=>{S&&(!R.current&&S.animationState&&S.animationState.animateChanges(),R.current&&(queueMicrotask(()=>{var z;(z=window.MotionHandoffMarkAsComplete)===null||z===void 0||z.call(window,C)}),R.current=!1))}),S}function V6(t,a,s,r){const{layoutId:o,layout:c,drag:d,dragConstraints:h,layoutScroll:p,layoutRoot:y}=a;t.projection=new s(t.latestValues,a["data-framer-portal-id"]?void 0:op(t.parent)),t.projection.setOptions({layoutId:o,layout:c,alwaysMeasureLayout:!!d||h&&ms(h),visualElement:t,animationType:typeof c=="string"?c:"both",initialPromotionConfig:r,layoutScroll:p,layoutRoot:y})}function op(t){if(t)return t.options.allowProjection!==!1?t.projection:op(t.parent)}function z6({preloadedFeatures:t,createVisualElement:a,useRender:s,useVisualState:r,Component:o}){var c,d;t&&T6(t);function h(y,v){let b;const S={...w.useContext(kc),...y,layoutId:k6(y)},{isStatic:E}=S,N=M6(y),C=r(y,E);if(!E&&zc){L6();const R=U6(S);b=R.MeasureLayout,N.visualElement=O6(o,C,S,a,R.ProjectionNode)}return m.jsxs(yo.Provider,{value:N,children:[b&&N.visualElement?m.jsx(b,{visualElement:N.visualElement,...S}):null,s(o,y,R6(C,N.visualElement,v),C,E,N.visualElement)]})}h.displayName=`motion.${typeof o=="string"?o:`create(${(d=(c=o.displayName)!==null&&c!==void 0?c:o.name)!==null&&d!==void 0?d:""})`}`;const p=w.forwardRef(h);return p[N6]=o,p}function k6({layoutId:t}){const a=w.useContext(Oc).id;return a&&t!==void 0?a+"-"+t:t}function L6(t,a){w.useContext(tp).strict}function U6(t){const{drag:a,layout:s}=hs;if(!a&&!s)return{};const r={...a,...s};return{MeasureLayout:a!=null&&a.isEnabled(t)||s!=null&&s.isEnabled(t)?r.MeasureLayout:void 0,ProjectionNode:r.ProjectionNode}}const up=t=>a=>typeof a=="string"&&a.startsWith(t),Ic=up("--"),B6=up("var(--"),ef=t=>B6(t)?H6.test(t.split("/*")[0].trim()):!1,H6=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,Vr={};function P6(t){for(const a in t)Vr[a]=t[a],Ic(a)&&(Vr[a].isCSSVariable=!0)}const ps=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],bi=new Set(ps);function cp(t,{layout:a,layoutId:s}){return bi.has(t)||t.startsWith("origin")||(a||s!==void 0)&&(!!Vr[t]||t==="opacity")}const Rt=t=>!!(t&&t.getVelocity),fp=(t,a)=>a&&typeof t=="number"?a.transform(t):t,In=(t,a,s)=>s>a?a:s<t?t:s,ys={test:t=>typeof t=="number",parse:parseFloat,transform:t=>t},zr={...ys,transform:t=>In(0,1,t)},bo={...ys,default:1},kr=t=>({test:a=>typeof a=="string"&&a.endsWith(t)&&a.split(" ").length===1,parse:parseFloat,transform:a=>`${a}${t}`}),Da=kr("deg"),Ln=kr("%"),ge=kr("px"),q6=kr("vh"),F6=kr("vw"),dp={...Ln,parse:t=>Ln.parse(t)/100,transform:t=>Ln.transform(t*100)},G6={borderWidth:ge,borderTopWidth:ge,borderRightWidth:ge,borderBottomWidth:ge,borderLeftWidth:ge,borderRadius:ge,radius:ge,borderTopLeftRadius:ge,borderTopRightRadius:ge,borderBottomRightRadius:ge,borderBottomLeftRadius:ge,width:ge,maxWidth:ge,height:ge,maxHeight:ge,top:ge,right:ge,bottom:ge,left:ge,padding:ge,paddingTop:ge,paddingRight:ge,paddingBottom:ge,paddingLeft:ge,margin:ge,marginTop:ge,marginRight:ge,marginBottom:ge,marginLeft:ge,backgroundPositionX:ge,backgroundPositionY:ge},Y6={rotate:Da,rotateX:Da,rotateY:Da,rotateZ:Da,scale:bo,scaleX:bo,scaleY:bo,scaleZ:bo,skew:Da,skewX:Da,skewY:Da,distance:ge,translateX:ge,translateY:ge,translateZ:ge,x:ge,y:ge,z:ge,perspective:ge,transformPerspective:ge,opacity:zr,originX:dp,originY:dp,originZ:ge},hp={...ys,transform:Math.round},tf={...G6,...Y6,zIndex:hp,size:ge,fillOpacity:zr,strokeOpacity:zr,numOctaves:hp},Q6={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},X6=ps.length;function K6(t,a,s){let r="",o=!0;for(let c=0;c<X6;c++){const d=ps[c],h=t[d];if(h===void 0)continue;let p=!0;if(typeof h=="number"?p=h===(d.startsWith("scale")?1:0):p=parseFloat(h)===0,!p||s){const y=fp(h,tf[d]);if(!p){o=!1;const v=Q6[d]||d;r+=`${v}(${y}) `}s&&(a[d]=y)}}return r=r.trim(),s?r=s(a,o?"":r):o&&(r="none"),r}function nf(t,a,s){const{style:r,vars:o,transformOrigin:c}=t;let d=!1,h=!1;for(const p in a){const y=a[p];if(bi.has(p)){d=!0;continue}else if(Ic(p)){o[p]=y;continue}else{const v=fp(y,tf[p]);p.startsWith("origin")?(h=!0,c[p]=v):r[p]=v}}if(a.transform||(d||s?r.transform=K6(a,t.transform,s):r.transform&&(r.transform="none")),h){const{originX:p="50%",originY:y="50%",originZ:v=0}=c;r.transformOrigin=`${p} ${y} ${v}`}}const af=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function mp(t,a,s){for(const r in a)!Rt(a[r])&&!cp(r,s)&&(t[r]=a[r])}function Z6({transformTemplate:t},a){return w.useMemo(()=>{const s=af();return nf(s,a,t),Object.assign({},s.vars,s.style)},[a])}function W6(t,a){const s=t.style||{},r={};return mp(r,s,t),Object.assign(r,Z6(t,a)),r}function $6(t,a){const s={},r=W6(t,a);return t.drag&&t.dragListener!==!1&&(s.draggable=!1,r.userSelect=r.WebkitUserSelect=r.WebkitTouchCallout="none",r.touchAction=t.drag===!0?"none":`pan-${t.drag==="x"?"y":"x"}`),t.tabIndex===void 0&&(t.onTap||t.onTapStart||t.whileTap)&&(s.tabIndex=0),s.style=r,s}const J6=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function sf(t){return typeof t!="string"||t.includes("-")?!1:!!(J6.indexOf(t)>-1||/[A-Z]/u.test(t))}const I6={offset:"stroke-dashoffset",array:"stroke-dasharray"},ew={offset:"strokeDashoffset",array:"strokeDasharray"};function tw(t,a,s=1,r=0,o=!0){t.pathLength=1;const c=o?I6:ew;t[c.offset]=ge.transform(-r);const d=ge.transform(a),h=ge.transform(s);t[c.array]=`${d} ${h}`}function pp(t,a,s){return typeof t=="string"?t:ge.transform(a+s*t)}function nw(t,a,s){const r=pp(a,t.x,t.width),o=pp(s,t.y,t.height);return`${r} ${o}`}function rf(t,{attrX:a,attrY:s,attrScale:r,originX:o,originY:c,pathLength:d,pathSpacing:h=1,pathOffset:p=0,...y},v,b){if(nf(t,y,b),v){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};const{attrs:S,style:E,dimensions:N}=t;S.transform&&(N&&(E.transform=S.transform),delete S.transform),N&&(o!==void 0||c!==void 0||E.transform)&&(E.transformOrigin=nw(N,o!==void 0?o:.5,c!==void 0?c:.5)),a!==void 0&&(S.x=a),s!==void 0&&(S.y=s),r!==void 0&&(S.scale=r),d!==void 0&&tw(S,d,h,p,!1)}const yp=()=>({...af(),attrs:{}}),lf=t=>typeof t=="string"&&t.toLowerCase()==="svg";function aw(t,a,s,r){const o=w.useMemo(()=>{const c=yp();return rf(c,a,lf(r),t.transformTemplate),{...c.attrs,style:{...c.style}}},[a]);if(t.style){const c={};mp(c,t.style,t),o.style={...c,...o.style}}return o}function iw(t=!1){return(s,r,o,{latestValues:c},d)=>{const p=(sf(s)?aw:$6)(r,c,d,s),y=A6(r,typeof s=="string",t),v=s!==w.Fragment?{...y,...p,ref:o}:{},{children:b}=r,S=w.useMemo(()=>Rt(b)?b.get():b,[b]);return w.createElement(s,{...v,children:S})}}function gp(t){const a=[{},{}];return t==null||t.values.forEach((s,r)=>{a[0][r]=s.get(),a[1][r]=s.getVelocity()}),a}function of(t,a,s,r){if(typeof a=="function"){const[o,c]=gp(r);a=a(s!==void 0?s:t.custom,o,c)}if(typeof a=="string"&&(a=t.variants&&t.variants[a]),typeof a=="function"){const[o,c]=gp(r);a=a(s!==void 0?s:t.custom,o,c)}return a}const uf=t=>Array.isArray(t),sw=t=>!!(t&&typeof t=="object"&&t.mix&&t.toValue),rw=t=>uf(t)?t[t.length-1]||0:t;function xo(t){const a=Rt(t)?t.get():t;return sw(a)?a.toValue():a}function lw({scrapeMotionValuesFromProps:t,createRenderState:a,onUpdate:s},r,o,c){const d={latestValues:ow(r,o,c,t),renderState:a()};return s&&(d.onMount=h=>s({props:r,current:h,...d}),d.onUpdate=h=>s(h)),d}const vp=t=>(a,s)=>{const r=w.useContext(yo),o=w.useContext(uo),c=()=>lw(t,a,r,o);return s?c():Vc(c)};function ow(t,a,s,r){const o={},c=r(t,{});for(const S in c)o[S]=xo(c[S]);let{initial:d,animate:h}=t;const p=vo(t),y=ip(t);a&&y&&!p&&t.inherit!==!1&&(d===void 0&&(d=a.initial),h===void 0&&(h=a.animate));let v=s?s.initial===!1:!1;v=v||d===!1;const b=v?h:d;if(b&&typeof b!="boolean"&&!go(b)){const S=Array.isArray(b)?b:[b];for(let E=0;E<S.length;E++){const N=of(t,S[E]);if(N){const{transitionEnd:C,transition:R,...z}=N;for(const B in z){let H=z[B];if(Array.isArray(H)){const W=v?H.length-1:0;H=H[W]}H!==null&&(o[B]=H)}for(const B in C)o[B]=C[B]}}}return o}function cf(t,a,s){var r;const{style:o}=t,c={};for(const d in o)(Rt(o[d])||a.style&&Rt(a.style[d])||cp(d,t)||((r=s==null?void 0:s.getValue(d))===null||r===void 0?void 0:r.liveStyle)!==void 0)&&(c[d]=o[d]);return c}const uw={useVisualState:vp({scrapeMotionValuesFromProps:cf,createRenderState:af})};function bp(t,a){try{a.dimensions=typeof t.getBBox=="function"?t.getBBox():t.getBoundingClientRect()}catch{a.dimensions={x:0,y:0,width:0,height:0}}}function xp(t,{style:a,vars:s},r,o){Object.assign(t.style,a,o&&o.getProjectionStyles(r));for(const c in s)t.style.setProperty(c,s[c])}const Sp=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function wp(t,a,s,r){xp(t,a,void 0,r);for(const o in a.attrs)t.setAttribute(Sp.has(o)?o:Jc(o),a.attrs[o])}function Ep(t,a,s){const r=cf(t,a,s);for(const o in t)if(Rt(t[o])||Rt(a[o])){const c=ps.indexOf(o)!==-1?"attr"+o.charAt(0).toUpperCase()+o.substring(1):o;r[c]=t[o]}return r}const Tp=["x","y","width","height","cx","cy","r"],cw={useVisualState:vp({scrapeMotionValuesFromProps:Ep,createRenderState:yp,onUpdate:({props:t,prevProps:a,current:s,renderState:r,latestValues:o})=>{if(!s)return;let c=!!t.drag;if(!c){for(const h in o)if(bi.has(h)){c=!0;break}}if(!c)return;let d=!a;if(a)for(let h=0;h<Tp.length;h++){const p=Tp[h];t[p]!==a[p]&&(d=!0)}d&&Ke.read(()=>{bp(s,r),Ke.render(()=>{rf(r,o,lf(s.tagName),t.transformTemplate),wp(s,r)})})}})};function fw(t,a){return function(r,{forwardMotionProps:o}={forwardMotionProps:!1}){const d={...sf(r)?cw:uw,preloadedFeatures:t,useRender:iw(o),createVisualElement:a,Component:r};return z6(d)}}function Lr(t,a,s){const r=t.getProps();return of(r,a,s!==void 0?s:r.custom,t)}const Cp=new Set(["width","height","top","left","right","bottom",...ps]);function dw(t,a,s){t.hasValue(a)?t.getValue(a).set(s):t.addValue(a,Rr(s))}function hw(t,a){const s=Lr(t,a);let{transitionEnd:r={},transition:o={},...c}=s||{};c={...c,...r};for(const d in c){const h=rw(c[d]);dw(t,d,h)}}function mw(t){return!!(Rt(t)&&t.add)}function ff(t,a){const s=t.getValue("willChange");if(mw(s))return s.add(a)}function jp(t){return t.props[rp]}const Ap=(t,a,s)=>(((1-3*s+3*a)*t+(3*s-6*a))*t+3*a)*t,pw=1e-7,yw=12;function gw(t,a,s,r,o){let c,d,h=0;do d=a+(s-a)/2,c=Ap(d,r,o)-t,c>0?s=d:a=d;while(Math.abs(c)>pw&&++h<yw);return d}function Ur(t,a,s,r){if(t===a&&s===r)return $t;const o=c=>gw(c,0,1,t,s);return c=>c===0||c===1?c:Ap(o(c),a,r)}const Dp=t=>a=>a<=.5?t(2*a)/2:(2-t(2*(1-a)))/2,_p=t=>a=>1-t(1-a),Mp=Ur(.33,1.53,.69,.99),df=_p(Mp),Np=Dp(df),Rp=t=>(t*=2)<1?.5*df(t):.5*(2-Math.pow(2,-10*(t-1))),hf=t=>1-Math.sin(Math.acos(t)),Op=_p(hf),Vp=Dp(hf),zp=t=>/^0[^.\s]+$/u.test(t);function vw(t){return typeof t=="number"?t===0:t!==null?t==="none"||t==="0"||zp(t):!0}const Br=t=>Math.round(t*1e5)/1e5,mf=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;function bw(t){return t==null}const xw=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,pf=(t,a)=>s=>!!(typeof s=="string"&&xw.test(s)&&s.startsWith(t)||a&&!bw(s)&&Object.prototype.hasOwnProperty.call(s,a)),kp=(t,a,s)=>r=>{if(typeof r!="string")return r;const[o,c,d,h]=r.match(mf);return{[t]:parseFloat(o),[a]:parseFloat(c),[s]:parseFloat(d),alpha:h!==void 0?parseFloat(h):1}},Sw=t=>In(0,255,t),yf={...ys,transform:t=>Math.round(Sw(t))},xi={test:pf("rgb","red"),parse:kp("red","green","blue"),transform:({red:t,green:a,blue:s,alpha:r=1})=>"rgba("+yf.transform(t)+", "+yf.transform(a)+", "+yf.transform(s)+", "+Br(zr.transform(r))+")"};function ww(t){let a="",s="",r="",o="";return t.length>5?(a=t.substring(1,3),s=t.substring(3,5),r=t.substring(5,7),o=t.substring(7,9)):(a=t.substring(1,2),s=t.substring(2,3),r=t.substring(3,4),o=t.substring(4,5),a+=a,s+=s,r+=r,o+=o),{red:parseInt(a,16),green:parseInt(s,16),blue:parseInt(r,16),alpha:o?parseInt(o,16)/255:1}}const gf={test:pf("#"),parse:ww,transform:xi.transform},gs={test:pf("hsl","hue"),parse:kp("hue","saturation","lightness"),transform:({hue:t,saturation:a,lightness:s,alpha:r=1})=>"hsla("+Math.round(t)+", "+Ln.transform(Br(a))+", "+Ln.transform(Br(s))+", "+Br(zr.transform(r))+")"},Ot={test:t=>xi.test(t)||gf.test(t)||gs.test(t),parse:t=>xi.test(t)?xi.parse(t):gs.test(t)?gs.parse(t):gf.parse(t),transform:t=>typeof t=="string"?t:t.hasOwnProperty("red")?xi.transform(t):gs.transform(t)},Ew=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;function Tw(t){var a,s;return isNaN(t)&&typeof t=="string"&&(((a=t.match(mf))===null||a===void 0?void 0:a.length)||0)+(((s=t.match(Ew))===null||s===void 0?void 0:s.length)||0)>0}const Lp="number",Up="color",Cw="var",jw="var(",Bp="${}",Aw=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function Hr(t){const a=t.toString(),s=[],r={color:[],number:[],var:[]},o=[];let c=0;const h=a.replace(Aw,p=>(Ot.test(p)?(r.color.push(c),o.push(Up),s.push(Ot.parse(p))):p.startsWith(jw)?(r.var.push(c),o.push(Cw),s.push(p)):(r.number.push(c),o.push(Lp),s.push(parseFloat(p))),++c,Bp)).split(Bp);return{values:s,split:h,indexes:r,types:o}}function Hp(t){return Hr(t).values}function Pp(t){const{split:a,types:s}=Hr(t),r=a.length;return o=>{let c="";for(let d=0;d<r;d++)if(c+=a[d],o[d]!==void 0){const h=s[d];h===Lp?c+=Br(o[d]):h===Up?c+=Ot.transform(o[d]):c+=o[d]}return c}}const Dw=t=>typeof t=="number"?0:t;function _w(t){const a=Hp(t);return Pp(t)(a.map(Dw))}const _a={test:Tw,parse:Hp,createTransformer:Pp,getAnimatableNone:_w},Mw=new Set(["brightness","contrast","saturate","opacity"]);function Nw(t){const[a,s]=t.slice(0,-1).split("(");if(a==="drop-shadow")return t;const[r]=s.match(mf)||[];if(!r)return t;const o=s.replace(r,"");let c=Mw.has(a)?1:0;return r!==s&&(c*=100),a+"("+c+o+")"}const Rw=/\b([a-z-]*)\(.*?\)/gu,vf={..._a,getAnimatableNone:t=>{const a=t.match(Rw);return a?a.map(Nw).join(" "):t}},Ow={...tf,color:Ot,backgroundColor:Ot,outlineColor:Ot,fill:Ot,stroke:Ot,borderColor:Ot,borderTopColor:Ot,borderRightColor:Ot,borderBottomColor:Ot,borderLeftColor:Ot,filter:vf,WebkitFilter:vf},qp=t=>Ow[t];function Fp(t,a){let s=qp(t);return s!==vf&&(s=_a),s.getAnimatableNone?s.getAnimatableNone(a):void 0}const Vw=new Set(["auto","none","0"]);function zw(t,a,s){let r=0,o;for(;r<t.length&&!o;){const c=t[r];typeof c=="string"&&!Vw.has(c)&&Hr(c).values.length&&(o=t[r]),r++}if(o&&s)for(const c of a)t[c]=Fp(s,o)}const Si=t=>t*180/Math.PI,bf=t=>{const a=Si(Math.atan2(t[1],t[0]));return xf(a)},kw={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:t=>(Math.abs(t[0])+Math.abs(t[3]))/2,rotate:bf,rotateZ:bf,skewX:t=>Si(Math.atan(t[1])),skewY:t=>Si(Math.atan(t[2])),skew:t=>(Math.abs(t[1])+Math.abs(t[2]))/2},xf=t=>(t=t%360,t<0&&(t+=360),t),Gp=bf,Yp=t=>Math.sqrt(t[0]*t[0]+t[1]*t[1]),Qp=t=>Math.sqrt(t[4]*t[4]+t[5]*t[5]),Lw={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:Yp,scaleY:Qp,scale:t=>(Yp(t)+Qp(t))/2,rotateX:t=>xf(Si(Math.atan2(t[6],t[5]))),rotateY:t=>xf(Si(Math.atan2(-t[2],t[0]))),rotateZ:Gp,rotate:Gp,skewX:t=>Si(Math.atan(t[4])),skewY:t=>Si(Math.atan(t[1])),skew:t=>(Math.abs(t[1])+Math.abs(t[4]))/2};function Xp(t){return t.includes("scale")?1:0}function Sf(t,a){if(!t||t==="none")return Xp(a);const s=t.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);let r,o;if(s)r=Lw,o=s;else{const h=t.match(/^matrix\(([-\d.e\s,]+)\)$/u);r=kw,o=h}if(!o)return Xp(a);const c=r[a],d=o[1].split(",").map(Bw);return typeof c=="function"?c(d):d[c]}const Uw=(t,a)=>{const{transform:s="none"}=getComputedStyle(t);return Sf(s,a)};function Bw(t){return parseFloat(t.trim())}const Kp=t=>t===ys||t===ge,Hw=new Set(["x","y","z"]),Pw=ps.filter(t=>!Hw.has(t));function qw(t){const a=[];return Pw.forEach(s=>{const r=t.getValue(s);r!==void 0&&(a.push([s,r.get()]),r.set(s.startsWith("scale")?1:0))}),a}const vs={width:({x:t},{paddingLeft:a="0",paddingRight:s="0"})=>t.max-t.min-parseFloat(a)-parseFloat(s),height:({y:t},{paddingTop:a="0",paddingBottom:s="0"})=>t.max-t.min-parseFloat(a)-parseFloat(s),top:(t,{top:a})=>parseFloat(a),left:(t,{left:a})=>parseFloat(a),bottom:({y:t},{top:a})=>parseFloat(a)+(t.max-t.min),right:({x:t},{left:a})=>parseFloat(a)+(t.max-t.min),x:(t,{transform:a})=>Sf(a,"x"),y:(t,{transform:a})=>Sf(a,"y")};vs.translateX=vs.x,vs.translateY=vs.y;const wi=new Set;let wf=!1,Ef=!1;function Zp(){if(Ef){const t=Array.from(wi).filter(r=>r.needsMeasurement),a=new Set(t.map(r=>r.element)),s=new Map;a.forEach(r=>{const o=qw(r);o.length&&(s.set(r,o),r.render())}),t.forEach(r=>r.measureInitialState()),a.forEach(r=>{r.render();const o=s.get(r);o&&o.forEach(([c,d])=>{var h;(h=r.getValue(c))===null||h===void 0||h.set(d)})}),t.forEach(r=>r.measureEndState()),t.forEach(r=>{r.suspendedScrollY!==void 0&&window.scrollTo(0,r.suspendedScrollY)})}Ef=!1,wf=!1,wi.forEach(t=>t.complete()),wi.clear()}function Wp(){wi.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(Ef=!0)})}function Fw(){Wp(),Zp()}class Tf{constructor(a,s,r,o,c,d=!1){this.isComplete=!1,this.isAsync=!1,this.needsMeasurement=!1,this.isScheduled=!1,this.unresolvedKeyframes=[...a],this.onComplete=s,this.name=r,this.motionValue=o,this.element=c,this.isAsync=d}scheduleResolve(){this.isScheduled=!0,this.isAsync?(wi.add(this),wf||(wf=!0,Ke.read(Wp),Ke.resolveKeyframes(Zp))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:a,name:s,element:r,motionValue:o}=this;for(let c=0;c<a.length;c++)if(a[c]===null)if(c===0){const d=o==null?void 0:o.get(),h=a[a.length-1];if(d!==void 0)a[0]=d;else if(r&&s){const p=r.readValue(s,h);p!=null&&(a[0]=p)}a[0]===void 0&&(a[0]=h),o&&d===void 0&&o.set(a[0])}else a[c]=a[c-1]}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(){this.isComplete=!0,this.onComplete(this.unresolvedKeyframes,this.finalKeyframe),wi.delete(this)}cancel(){this.isComplete||(this.isScheduled=!1,wi.delete(this))}resume(){this.isComplete||this.scheduleResolve()}}const $p=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t),Gw=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function Yw(t){const a=Gw.exec(t);if(!a)return[,];const[,s,r,o]=a;return[`--${s??r}`,o]}function Jp(t,a,s=1){const[r,o]=Yw(t);if(!r)return;const c=window.getComputedStyle(a).getPropertyValue(r);if(c){const d=c.trim();return $p(d)?parseFloat(d):d}return ef(o)?Jp(o,a,s+1):o}const Ip=t=>a=>a.test(t),ey=[ys,ge,Ln,Da,F6,q6,{test:t=>t==="auto",parse:t=>t}],ty=t=>ey.find(Ip(t));class ny extends Tf{constructor(a,s,r,o,c){super(a,s,r,o,c,!0)}readKeyframes(){const{unresolvedKeyframes:a,element:s,name:r}=this;if(!s||!s.current)return;super.readKeyframes();for(let p=0;p<a.length;p++){let y=a[p];if(typeof y=="string"&&(y=y.trim(),ef(y))){const v=Jp(y,s.current);v!==void 0&&(a[p]=v),p===a.length-1&&(this.finalKeyframe=y)}}if(this.resolveNoneKeyframes(),!Cp.has(r)||a.length!==2)return;const[o,c]=a,d=ty(o),h=ty(c);if(d!==h)if(Kp(d)&&Kp(h))for(let p=0;p<a.length;p++){const y=a[p];typeof y=="string"&&(a[p]=parseFloat(y))}else this.needsMeasurement=!0}resolveNoneKeyframes(){const{unresolvedKeyframes:a,name:s}=this,r=[];for(let o=0;o<a.length;o++)vw(a[o])&&r.push(o);r.length&&zw(a,r,s)}measureInitialState(){const{element:a,unresolvedKeyframes:s,name:r}=this;if(!a||!a.current)return;r==="height"&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=vs[r](a.measureViewportBox(),window.getComputedStyle(a.current)),s[0]=this.measuredOrigin;const o=s[s.length-1];o!==void 0&&a.getValue(r,o).jump(o,!1)}measureEndState(){var a;const{element:s,name:r,unresolvedKeyframes:o}=this;if(!s||!s.current)return;const c=s.getValue(r);c&&c.jump(this.measuredOrigin,!1);const d=o.length-1,h=o[d];o[d]=vs[r](s.measureViewportBox(),window.getComputedStyle(s.current)),h!==null&&this.finalKeyframe===void 0&&(this.finalKeyframe=h),!((a=this.removedTransforms)===null||a===void 0)&&a.length&&this.removedTransforms.forEach(([p,y])=>{s.getValue(p).set(y)}),this.resolveNoneKeyframes()}}const ay=(t,a)=>a==="zIndex"?!1:!!(typeof t=="number"||Array.isArray(t)||typeof t=="string"&&(_a.test(t)||t==="0")&&!t.startsWith("url("));function Qw(t){const a=t[0];if(t.length===1)return!0;for(let s=0;s<t.length;s++)if(t[s]!==a)return!0}function Xw(t,a,s,r){const o=t[0];if(o===null)return!1;if(a==="display"||a==="visibility")return!0;const c=t[t.length-1],d=ay(o,a),h=ay(c,a);return!d||!h?!1:Qw(t)||(s==="spring"||Fc(s))&&r}const Kw=t=>t!==null;function So(t,{repeat:a,repeatType:s="loop"},r){const o=t.filter(Kw),c=a&&s!=="loop"&&a%2===1?0:o.length-1;return!c||r===void 0?o[c]:r}const Zw=40;class iy{constructor({autoplay:a=!0,delay:s=0,type:r="keyframes",repeat:o=0,repeatDelay:c=0,repeatType:d="loop",...h}){this.isStopped=!1,this.hasAttemptedResolve=!1,this.createdAt=kn.now(),this.options={autoplay:a,delay:s,type:r,repeat:o,repeatDelay:c,repeatType:d,...h},this.updateFinishedPromise()}calcStartTime(){return this.resolvedAt?this.resolvedAt-this.createdAt>Zw?this.resolvedAt:this.createdAt:this.createdAt}get resolved(){return!this._resolved&&!this.hasAttemptedResolve&&Fw(),this._resolved}onKeyframesResolved(a,s){this.resolvedAt=kn.now(),this.hasAttemptedResolve=!0;const{name:r,type:o,velocity:c,delay:d,onComplete:h,onUpdate:p,isGenerator:y}=this.options;if(!y&&!Xw(a,r,o,c))if(d)this.options.duration=0;else{p&&p(So(a,this.options,s)),h&&h(),this.resolveFinishedPromise();return}const v=this.initPlayback(a,s);v!==!1&&(this._resolved={keyframes:a,finalKeyframe:s,...v},this.onPostResolved())}onPostResolved(){}then(a,s){return this.currentFinishedPromise.then(a,s)}flatten(){this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear")}updateFinishedPromise(){this.currentFinishedPromise=new Promise(a=>{this.resolveFinishedPromise=a})}}const Ie=(t,a,s)=>t+(a-t)*s;function Cf(t,a,s){return s<0&&(s+=1),s>1&&(s-=1),s<1/6?t+(a-t)*6*s:s<1/2?a:s<2/3?t+(a-t)*(2/3-s)*6:t}function Ww({hue:t,saturation:a,lightness:s,alpha:r}){t/=360,a/=100,s/=100;let o=0,c=0,d=0;if(!a)o=c=d=s;else{const h=s<.5?s*(1+a):s+a-s*a,p=2*s-h;o=Cf(p,h,t+1/3),c=Cf(p,h,t),d=Cf(p,h,t-1/3)}return{red:Math.round(o*255),green:Math.round(c*255),blue:Math.round(d*255),alpha:r}}function wo(t,a){return s=>s>0?a:t}const jf=(t,a,s)=>{const r=t*t,o=s*(a*a-r)+r;return o<0?0:Math.sqrt(o)},$w=[gf,xi,gs],Jw=t=>$w.find(a=>a.test(t));function sy(t){const a=Jw(t);if(!a)return!1;let s=a.parse(t);return a===gs&&(s=Ww(s)),s}const ry=(t,a)=>{const s=sy(t),r=sy(a);if(!s||!r)return wo(t,a);const o={...s};return c=>(o.red=jf(s.red,r.red,c),o.green=jf(s.green,r.green,c),o.blue=jf(s.blue,r.blue,c),o.alpha=Ie(s.alpha,r.alpha,c),xi.transform(o))},Iw=(t,a)=>s=>a(t(s)),Pr=(...t)=>t.reduce(Iw),Af=new Set(["none","hidden"]);function e5(t,a){return Af.has(t)?s=>s<=0?t:a:s=>s>=1?a:t}function t5(t,a){return s=>Ie(t,a,s)}function Df(t){return typeof t=="number"?t5:typeof t=="string"?ef(t)?wo:Ot.test(t)?ry:i5:Array.isArray(t)?ly:typeof t=="object"?Ot.test(t)?ry:n5:wo}function ly(t,a){const s=[...t],r=s.length,o=t.map((c,d)=>Df(c)(c,a[d]));return c=>{for(let d=0;d<r;d++)s[d]=o[d](c);return s}}function n5(t,a){const s={...t,...a},r={};for(const o in s)t[o]!==void 0&&a[o]!==void 0&&(r[o]=Df(t[o])(t[o],a[o]));return o=>{for(const c in r)s[c]=r[c](o);return s}}function a5(t,a){var s;const r=[],o={color:0,var:0,number:0};for(let c=0;c<a.values.length;c++){const d=a.types[c],h=t.indexes[d][o[d]],p=(s=t.values[h])!==null&&s!==void 0?s:0;r[c]=p,o[d]++}return r}const i5=(t,a)=>{const s=_a.createTransformer(a),r=Hr(t),o=Hr(a);return r.indexes.var.length===o.indexes.var.length&&r.indexes.color.length===o.indexes.color.length&&r.indexes.number.length>=o.indexes.number.length?Af.has(t)&&!o.values.length||Af.has(a)&&!r.values.length?e5(t,a):Pr(ly(a5(r,o),o.values),s):wo(t,a)};function oy(t,a,s){return typeof t=="number"&&typeof a=="number"&&typeof s=="number"?Ie(t,a,s):Df(t)(t,a)}const s5=5;function uy(t,a,s){const r=Math.max(a-s5,0);return H0(s-t(r),a-r)}const nt={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1},cy=.001;function r5({duration:t=nt.duration,bounce:a=nt.bounce,velocity:s=nt.velocity,mass:r=nt.mass}){let o,c,d=1-a;d=In(nt.minDamping,nt.maxDamping,d),t=In(nt.minDuration,nt.maxDuration,Jn(t)),d<1?(o=y=>{const v=y*d,b=v*t,S=v-s,E=_f(y,d),N=Math.exp(-b);return cy-S/E*N},c=y=>{const b=y*d*t,S=b*s+s,E=Math.pow(d,2)*Math.pow(y,2)*t,N=Math.exp(-b),C=_f(Math.pow(y,2),d);return(-o(y)+cy>0?-1:1)*((S-E)*N)/C}):(o=y=>{const v=Math.exp(-y*t),b=(y-s)*t+1;return-.001+v*b},c=y=>{const v=Math.exp(-y*t),b=(s-y)*(t*t);return v*b});const h=5/t,p=o5(o,c,h);if(t=$n(t),isNaN(p))return{stiffness:nt.stiffness,damping:nt.damping,duration:t};{const y=Math.pow(p,2)*r;return{stiffness:y,damping:d*2*Math.sqrt(r*y),duration:t}}}const l5=12;function o5(t,a,s){let r=s;for(let o=1;o<l5;o++)r=r-t(r)/a(r);return r}function _f(t,a){return t*Math.sqrt(1-a*a)}const u5=["duration","bounce"],c5=["stiffness","damping","mass"];function fy(t,a){return a.some(s=>t[s]!==void 0)}function f5(t){let a={velocity:nt.velocity,stiffness:nt.stiffness,damping:nt.damping,mass:nt.mass,isResolvedFromDuration:!1,...t};if(!fy(t,c5)&&fy(t,u5))if(t.visualDuration){const s=t.visualDuration,r=2*Math.PI/(s*1.2),o=r*r,c=2*In(.05,1,1-(t.bounce||0))*Math.sqrt(o);a={...a,mass:nt.mass,stiffness:o,damping:c}}else{const s=r5(t);a={...a,...s,mass:nt.mass},a.isResolvedFromDuration=!0}return a}function dy(t=nt.visualDuration,a=nt.bounce){const s=typeof t!="object"?{visualDuration:t,keyframes:[0,1],bounce:a}:t;let{restSpeed:r,restDelta:o}=s;const c=s.keyframes[0],d=s.keyframes[s.keyframes.length-1],h={done:!1,value:c},{stiffness:p,damping:y,mass:v,duration:b,velocity:S,isResolvedFromDuration:E}=f5({...s,velocity:-Jn(s.velocity||0)}),N=S||0,C=y/(2*Math.sqrt(p*v)),R=d-c,z=Jn(Math.sqrt(p/v)),B=Math.abs(R)<5;r||(r=B?nt.restSpeed.granular:nt.restSpeed.default),o||(o=B?nt.restDelta.granular:nt.restDelta.default);let H;if(C<1){const O=_f(z,C);H=X=>{const J=Math.exp(-C*z*X);return d-J*((N+C*z*R)/O*Math.sin(O*X)+R*Math.cos(O*X))}}else if(C===1)H=O=>d-Math.exp(-z*O)*(R+(N+z*R)*O);else{const O=z*Math.sqrt(C*C-1);H=X=>{const J=Math.exp(-C*z*X),Z=Math.min(O*X,300);return d-J*((N+C*z*R)*Math.sinh(Z)+O*R*Math.cosh(Z))/O}}const W={calculatedDuration:E&&b||null,next:O=>{const X=H(O);if(E)h.done=O>=b;else{let J=0;C<1&&(J=O===0?$n(N):uy(H,O,X));const Z=Math.abs(J)<=r,Y=Math.abs(d-X)<=o;h.done=Z&&Y}return h.value=h.done?d:X,h},toString:()=>{const O=Math.min(P0(W),qc),X=F0(J=>W.next(O*J).value,O,30);return O+"ms "+X}};return W}function hy({keyframes:t,velocity:a=0,power:s=.8,timeConstant:r=325,bounceDamping:o=10,bounceStiffness:c=500,modifyTarget:d,min:h,max:p,restDelta:y=.5,restSpeed:v}){const b=t[0],S={done:!1,value:b},E=Z=>h!==void 0&&Z<h||p!==void 0&&Z>p,N=Z=>h===void 0?p:p===void 0||Math.abs(h-Z)<Math.abs(p-Z)?h:p;let C=s*a;const R=b+C,z=d===void 0?R:d(R);z!==R&&(C=z-b);const B=Z=>-C*Math.exp(-Z/r),H=Z=>z+B(Z),W=Z=>{const Y=B(Z),te=H(Z);S.done=Math.abs(Y)<=y,S.value=S.done?z:te};let O,X;const J=Z=>{E(S.value)&&(O=Z,X=dy({keyframes:[S.value,N(S.value)],velocity:uy(H,Z,S.value),damping:o,stiffness:c,restDelta:y,restSpeed:v}))};return J(0),{calculatedDuration:null,next:Z=>{let Y=!1;return!X&&O===void 0&&(Y=!0,W(Z),J(Z)),O!==void 0&&Z>=O?X.next(Z-O):(!Y&&W(Z),S)}}}const d5=Ur(.42,0,1,1),h5=Ur(0,0,.58,1),my=Ur(.42,0,.58,1),m5=t=>Array.isArray(t)&&typeof t[0]!="number",p5={linear:$t,easeIn:d5,easeInOut:my,easeOut:h5,circIn:hf,circInOut:Vp,circOut:Op,backIn:df,backInOut:Np,backOut:Mp,anticipate:Rp},py=t=>{if(Gc(t)){B0(t.length===4);const[a,s,r,o]=t;return Ur(a,s,r,o)}else if(typeof t=="string")return p5[t];return t};function y5(t,a,s){const r=[],o=s||oy,c=t.length-1;for(let d=0;d<c;d++){let h=o(t[d],t[d+1]);if(a){const p=Array.isArray(a)?a[d]||$t:a;h=Pr(p,h)}r.push(h)}return r}function g5(t,a,{clamp:s=!0,ease:r,mixer:o}={}){const c=t.length;if(B0(c===a.length),c===1)return()=>a[0];if(c===2&&a[0]===a[1])return()=>a[1];const d=t[0]===t[1];t[0]>t[c-1]&&(t=[...t].reverse(),a=[...a].reverse());const h=y5(a,r,o),p=h.length,y=v=>{if(d&&v<t[0])return a[0];let b=0;if(p>1)for(;b<t.length-2&&!(v<t[b+1]);b++);const S=ds(t[b],t[b+1],v);return h[b](S)};return s?v=>y(In(t[0],t[c-1],v)):y}function v5(t,a){const s=t[t.length-1];for(let r=1;r<=a;r++){const o=ds(0,a,r);t.push(Ie(s,1,o))}}function b5(t){const a=[0];return v5(a,t.length-1),a}function x5(t,a){return t.map(s=>s*a)}function S5(t,a){return t.map(()=>a||my).splice(0,t.length-1)}function Eo({duration:t=300,keyframes:a,times:s,ease:r="easeInOut"}){const o=m5(r)?r.map(py):py(r),c={done:!1,value:a[0]},d=x5(s&&s.length===a.length?s:b5(a),t),h=g5(d,a,{ease:Array.isArray(o)?o:S5(a,o)});return{calculatedDuration:t,next:p=>(c.value=h(p),c.done=p>=t,c)}}const w5=t=>{const a=({timestamp:s})=>t(s);return{start:()=>Ke.update(a,!0),stop:()=>Aa(a),now:()=>At.isProcessing?At.timestamp:kn.now()}},E5={decay:hy,inertia:hy,tween:Eo,keyframes:Eo,spring:dy},T5=t=>t/100;class Mf extends iy{constructor(a){super(a),this.holdTime=null,this.cancelTime=null,this.currentTime=0,this.playbackSpeed=1,this.pendingPlayState="running",this.startTime=null,this.state="idle",this.stop=()=>{if(this.resolver.cancel(),this.isStopped=!0,this.state==="idle")return;this.teardown();const{onStop:p}=this.options;p&&p()};const{name:s,motionValue:r,element:o,keyframes:c}=this.options,d=(o==null?void 0:o.KeyframeResolver)||Tf,h=(p,y)=>this.onKeyframesResolved(p,y);this.resolver=new d(c,h,s,r,o),this.resolver.scheduleResolve()}flatten(){super.flatten(),this._resolved&&Object.assign(this._resolved,this.initPlayback(this._resolved.keyframes))}initPlayback(a){const{type:s="keyframes",repeat:r=0,repeatDelay:o=0,repeatType:c,velocity:d=0}=this.options,h=Fc(s)?s:E5[s]||Eo;let p,y;h!==Eo&&typeof a[0]!="number"&&(p=Pr(T5,oy(a[0],a[1])),a=[0,100]);const v=h({...this.options,keyframes:a});c==="mirror"&&(y=h({...this.options,keyframes:[...a].reverse(),velocity:-d})),v.calculatedDuration===null&&(v.calculatedDuration=P0(v));const{calculatedDuration:b}=v,S=b+o,E=S*(r+1)-o;return{generator:v,mirroredGenerator:y,mapPercentToKeyframes:p,calculatedDuration:b,resolvedDuration:S,totalDuration:E}}onPostResolved(){const{autoplay:a=!0}=this.options;this.play(),this.pendingPlayState==="paused"||!a?this.pause():this.state=this.pendingPlayState}tick(a,s=!1){const{resolved:r}=this;if(!r){const{keyframes:Z}=this.options;return{done:!0,value:Z[Z.length-1]}}const{finalKeyframe:o,generator:c,mirroredGenerator:d,mapPercentToKeyframes:h,keyframes:p,calculatedDuration:y,totalDuration:v,resolvedDuration:b}=r;if(this.startTime===null)return c.next(0);const{delay:S,repeat:E,repeatType:N,repeatDelay:C,onUpdate:R}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,a):this.speed<0&&(this.startTime=Math.min(a-v/this.speed,this.startTime)),s?this.currentTime=a:this.holdTime!==null?this.currentTime=this.holdTime:this.currentTime=Math.round(a-this.startTime)*this.speed;const z=this.currentTime-S*(this.speed>=0?1:-1),B=this.speed>=0?z<0:z>v;this.currentTime=Math.max(z,0),this.state==="finished"&&this.holdTime===null&&(this.currentTime=v);let H=this.currentTime,W=c;if(E){const Z=Math.min(this.currentTime,v)/b;let Y=Math.floor(Z),te=Z%1;!te&&Z>=1&&(te=1),te===1&&Y--,Y=Math.min(Y,E+1),!!(Y%2)&&(N==="reverse"?(te=1-te,C&&(te-=C/b)):N==="mirror"&&(W=d)),H=In(0,1,te)*b}const O=B?{done:!1,value:p[0]}:W.next(H);h&&(O.value=h(O.value));let{done:X}=O;!B&&y!==null&&(X=this.speed>=0?this.currentTime>=v:this.currentTime<=0);const J=this.holdTime===null&&(this.state==="finished"||this.state==="running"&&X);return J&&o!==void 0&&(O.value=So(p,this.options,o)),R&&R(O.value),J&&this.finish(),O}get duration(){const{resolved:a}=this;return a?Jn(a.calculatedDuration):0}get time(){return Jn(this.currentTime)}set time(a){a=$n(a),this.currentTime=a,this.holdTime!==null||this.speed===0?this.holdTime=a:this.driver&&(this.startTime=this.driver.now()-a/this.speed)}get speed(){return this.playbackSpeed}set speed(a){const s=this.playbackSpeed!==a;this.playbackSpeed=a,s&&(this.time=Jn(this.currentTime))}play(){if(this.resolver.isScheduled||this.resolver.resume(),!this._resolved){this.pendingPlayState="running";return}if(this.isStopped)return;const{driver:a=w5,onPlay:s,startTime:r}=this.options;this.driver||(this.driver=a(c=>this.tick(c))),s&&s();const o=this.driver.now();this.holdTime!==null?this.startTime=o-this.holdTime:this.startTime?this.state==="finished"&&(this.startTime=o):this.startTime=r??this.calcStartTime(),this.state==="finished"&&this.updateFinishedPromise(),this.cancelTime=this.startTime,this.holdTime=null,this.state="running",this.driver.start()}pause(){var a;if(!this._resolved){this.pendingPlayState="paused";return}this.state="paused",this.holdTime=(a=this.currentTime)!==null&&a!==void 0?a:0}complete(){this.state!=="running"&&this.play(),this.pendingPlayState=this.state="finished",this.holdTime=null}finish(){this.teardown(),this.state="finished";const{onComplete:a}=this.options;a&&a()}cancel(){this.cancelTime!==null&&this.tick(this.cancelTime),this.teardown(),this.updateFinishedPromise()}teardown(){this.state="idle",this.stopDriver(),this.resolveFinishedPromise(),this.updateFinishedPromise(),this.startTime=this.cancelTime=null,this.resolver.cancel()}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(a){return this.startTime=0,this.tick(a,!0)}}const C5=new Set(["opacity","clipPath","filter","transform"]);function j5(t,a,s,{delay:r=0,duration:o=300,repeat:c=0,repeatType:d="loop",ease:h="easeInOut",times:p}={}){const y={[a]:s};p&&(y.offset=p);const v=Y0(h,o);return Array.isArray(v)&&(y.easing=v),t.animate(y,{delay:r,duration:o,easing:Array.isArray(v)?"linear":v,fill:"both",iterations:c+1,direction:d==="reverse"?"alternate":"normal"})}const A5=Bc(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),To=10,D5=2e4;function _5(t){return Fc(t.type)||t.type==="spring"||!G0(t.ease)}function M5(t,a){const s=new Mf({...a,keyframes:t,repeat:0,delay:0,isGenerator:!0});let r={done:!1,value:t[0]};const o=[];let c=0;for(;!r.done&&c<D5;)r=s.sample(c),o.push(r.value),c+=To;return{times:void 0,keyframes:o,duration:c-To,ease:"linear"}}const yy={anticipate:Rp,backInOut:Np,circInOut:Vp};function N5(t){return t in yy}class gy extends iy{constructor(a){super(a);const{name:s,motionValue:r,element:o,keyframes:c}=this.options;this.resolver=new ny(c,(d,h)=>this.onKeyframesResolved(d,h),s,r,o),this.resolver.scheduleResolve()}initPlayback(a,s){let{duration:r=300,times:o,ease:c,type:d,motionValue:h,name:p,startTime:y}=this.options;if(!h.owner||!h.owner.current)return!1;if(typeof c=="string"&&fo()&&N5(c)&&(c=yy[c]),_5(this.options)){const{onComplete:b,onUpdate:S,motionValue:E,element:N,...C}=this.options,R=M5(a,C);a=R.keyframes,a.length===1&&(a[1]=a[0]),r=R.duration,o=R.times,c=R.ease,d="keyframes"}const v=j5(h.owner.current,p,a,{...this.options,duration:r,times:o,ease:c});return v.startTime=y??this.calcStartTime(),this.pendingTimeline?(q0(v,this.pendingTimeline),this.pendingTimeline=void 0):v.onfinish=()=>{const{onComplete:b}=this.options;h.set(So(a,this.options,s)),b&&b(),this.cancel(),this.resolveFinishedPromise()},{animation:v,duration:r,times:o,type:d,ease:c,keyframes:a}}get duration(){const{resolved:a}=this;if(!a)return 0;const{duration:s}=a;return Jn(s)}get time(){const{resolved:a}=this;if(!a)return 0;const{animation:s}=a;return Jn(s.currentTime||0)}set time(a){const{resolved:s}=this;if(!s)return;const{animation:r}=s;r.currentTime=$n(a)}get speed(){const{resolved:a}=this;if(!a)return 1;const{animation:s}=a;return s.playbackRate}set speed(a){const{resolved:s}=this;if(!s)return;const{animation:r}=s;r.playbackRate=a}get state(){const{resolved:a}=this;if(!a)return"idle";const{animation:s}=a;return s.playState}get startTime(){const{resolved:a}=this;if(!a)return null;const{animation:s}=a;return s.startTime}attachTimeline(a){if(!this._resolved)this.pendingTimeline=a;else{const{resolved:s}=this;if(!s)return $t;const{animation:r}=s;q0(r,a)}return $t}play(){if(this.isStopped)return;const{resolved:a}=this;if(!a)return;const{animation:s}=a;s.playState==="finished"&&this.updateFinishedPromise(),s.play()}pause(){const{resolved:a}=this;if(!a)return;const{animation:s}=a;s.pause()}stop(){if(this.resolver.cancel(),this.isStopped=!0,this.state==="idle")return;this.resolveFinishedPromise(),this.updateFinishedPromise();const{resolved:a}=this;if(!a)return;const{animation:s,keyframes:r,duration:o,type:c,ease:d,times:h}=a;if(s.playState==="idle"||s.playState==="finished")return;if(this.time){const{motionValue:y,onUpdate:v,onComplete:b,element:S,...E}=this.options,N=new Mf({...E,keyframes:r,duration:o,type:c,ease:d,times:h,isGenerator:!0}),C=$n(this.time);y.setWithVelocity(N.sample(C-To).value,N.sample(C).value,To)}const{onStop:p}=this.options;p&&p(),this.cancel()}complete(){const{resolved:a}=this;a&&a.animation.finish()}cancel(){const{resolved:a}=this;a&&a.animation.cancel()}static supports(a){const{motionValue:s,name:r,repeatDelay:o,repeatType:c,damping:d,type:h}=a;if(!s||!s.owner||!(s.owner.current instanceof HTMLElement))return!1;const{onUpdate:p,transformTemplate:y}=s.owner.getProps();return A5()&&r&&C5.has(r)&&(r!=="transform"||!y)&&!p&&!o&&c!=="mirror"&&d!==0&&h!=="inertia"}}const R5={type:"spring",stiffness:500,damping:25,restSpeed:10},O5=t=>({type:"spring",stiffness:550,damping:t===0?2*Math.sqrt(550):30,restSpeed:10}),V5={type:"keyframes",duration:.8},z5={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},k5=(t,{keyframes:a})=>a.length>2?V5:bi.has(t)?t.startsWith("scale")?O5(a[1]):R5:z5;function L5({when:t,delay:a,delayChildren:s,staggerChildren:r,staggerDirection:o,repeat:c,repeatType:d,repeatDelay:h,from:p,elapsed:y,...v}){return!!Object.keys(v).length}const Nf=(t,a,s,r={},o,c)=>d=>{const h=Pc(r,t)||{},p=h.delay||r.delay||0;let{elapsed:y=0}=r;y=y-$n(p);let v={keyframes:Array.isArray(s)?s:[null,s],ease:"easeOut",velocity:a.getVelocity(),...h,delay:-y,onUpdate:S=>{a.set(S),h.onUpdate&&h.onUpdate(S)},onComplete:()=>{d(),h.onComplete&&h.onComplete()},name:t,motionValue:a,element:c?void 0:o};L5(h)||(v={...v,...k5(t,v)}),v.duration&&(v.duration=$n(v.duration)),v.repeatDelay&&(v.repeatDelay=$n(v.repeatDelay)),v.from!==void 0&&(v.keyframes[0]=v.from);let b=!1;if((v.type===!1||v.duration===0&&!v.repeatDelay)&&(v.duration=0,v.delay===0&&(b=!0)),v.allowFlatten=!h.type&&!h.ease,b&&!c&&a.get()!==void 0){const S=So(v.keyframes,h);if(S!==void 0)return Ke.update(()=>{v.onUpdate(S),v.onComplete()}),new u6([])}return!c&&gy.supports(v)?new gy(v):new Mf(v)};function U5({protectedKeys:t,needsAnimating:a},s){const r=t.hasOwnProperty(s)&&a[s]!==!0;return a[s]=!1,r}function vy(t,a,{delay:s=0,transitionOverride:r,type:o}={}){var c;let{transition:d=t.getDefaultTransition(),transitionEnd:h,...p}=a;r&&(d=r);const y=[],v=o&&t.animationState&&t.animationState.getState()[o];for(const b in p){const S=t.getValue(b,(c=t.latestValues[b])!==null&&c!==void 0?c:null),E=p[b];if(E===void 0||v&&U5(v,b))continue;const N={delay:s,...Pc(d||{},b)};let C=!1;if(window.MotionHandoffAnimation){const z=jp(t);if(z){const B=window.MotionHandoffAnimation(z,b,Ke);B!==null&&(N.startTime=B,C=!0)}}ff(t,b),S.start(Nf(b,S,E,t.shouldReduceMotion&&Cp.has(b)?{type:!1}:N,t,C));const R=S.animation;R&&y.push(R)}return h&&Promise.all(y).then(()=>{Ke.update(()=>{h&&hw(t,h)})}),y}function Rf(t,a,s={}){var r;const o=Lr(t,a,s.type==="exit"?(r=t.presenceContext)===null||r===void 0?void 0:r.custom:void 0);let{transition:c=t.getDefaultTransition()||{}}=o||{};s.transitionOverride&&(c=s.transitionOverride);const d=o?()=>Promise.all(vy(t,o,s)):()=>Promise.resolve(),h=t.variantChildren&&t.variantChildren.size?(y=0)=>{const{delayChildren:v=0,staggerChildren:b,staggerDirection:S}=c;return B5(t,a,v+y,b,S,s)}:()=>Promise.resolve(),{when:p}=c;if(p){const[y,v]=p==="beforeChildren"?[d,h]:[h,d];return y().then(()=>v())}else return Promise.all([d(),h(s.delay)])}function B5(t,a,s=0,r=0,o=1,c){const d=[],h=(t.variantChildren.size-1)*r,p=o===1?(y=0)=>y*r:(y=0)=>h-y*r;return Array.from(t.variantChildren).sort(H5).forEach((y,v)=>{y.notify("AnimationStart",a),d.push(Rf(y,a,{...c,delay:s+p(v)}).then(()=>y.notify("AnimationComplete",a)))}),Promise.all(d)}function H5(t,a){return t.sortNodePosition(a)}function P5(t,a,s={}){t.notify("AnimationStart",a);let r;if(Array.isArray(a)){const o=a.map(c=>Rf(t,c,s));r=Promise.all(o)}else if(typeof a=="string")r=Rf(t,a,s);else{const o=typeof a=="function"?Lr(t,a,s.custom):a;r=Promise.all(vy(t,o,s))}return r.then(()=>{t.notify("AnimationComplete",a)})}function by(t,a){if(!Array.isArray(a))return!1;const s=a.length;if(s!==t.length)return!1;for(let r=0;r<s;r++)if(a[r]!==t[r])return!1;return!0}const q5=$c.length;function xy(t){if(!t)return;if(!t.isControllingVariants){const s=t.parent?xy(t.parent)||{}:{};return t.props.initial!==void 0&&(s.initial=t.props.initial),s}const a={};for(let s=0;s<q5;s++){const r=$c[s],o=t.props[r];(Or(o)||o===!1)&&(a[r]=o)}return a}const F5=[...Wc].reverse(),G5=Wc.length;function Y5(t){return a=>Promise.all(a.map(({animation:s,options:r})=>P5(t,s,r)))}function Q5(t){let a=Y5(t),s=Sy(),r=!0;const o=p=>(y,v)=>{var b;const S=Lr(t,v,p==="exit"?(b=t.presenceContext)===null||b===void 0?void 0:b.custom:void 0);if(S){const{transition:E,transitionEnd:N,...C}=S;y={...y,...C,...N}}return y};function c(p){a=p(t)}function d(p){const{props:y}=t,v=xy(t.parent)||{},b=[],S=new Set;let E={},N=1/0;for(let R=0;R<G5;R++){const z=F5[R],B=s[z],H=y[z]!==void 0?y[z]:v[z],W=Or(H),O=z===p?B.isActive:null;O===!1&&(N=R);let X=H===v[z]&&H!==y[z]&&W;if(X&&r&&t.manuallyAnimateOnMount&&(X=!1),B.protectedKeys={...E},!B.isActive&&O===null||!H&&!B.prevProp||go(H)||typeof H=="boolean")continue;const J=X5(B.prevProp,H);let Z=J||z===p&&B.isActive&&!X&&W||R>N&&W,Y=!1;const te=Array.isArray(H)?H:[H];let je=te.reduce(o(z),{});O===!1&&(je={});const{prevResolvedValues:le={}}=B,ye={...le,...je},Ae=ne=>{Z=!0,S.has(ne)&&(Y=!0,S.delete(ne)),B.needsAnimating[ne]=!0;const re=t.getValue(ne);re&&(re.liveStyle=!1)};for(const ne in ye){const re=je[ne],De=le[ne];if(E.hasOwnProperty(ne))continue;let D=!1;uf(re)&&uf(De)?D=!by(re,De):D=re!==De,D?re!=null?Ae(ne):S.add(ne):re!==void 0&&S.has(ne)?Ae(ne):B.protectedKeys[ne]=!0}B.prevProp=H,B.prevResolvedValues=je,B.isActive&&(E={...E,...je}),r&&t.blockInitialAnimation&&(Z=!1),Z&&(!(X&&J)||Y)&&b.push(...te.map(ne=>({animation:ne,options:{type:z}})))}if(S.size){const R={};if(typeof y.initial!="boolean"){const z=Lr(t,Array.isArray(y.initial)?y.initial[0]:y.initial);z&&z.transition&&(R.transition=z.transition)}S.forEach(z=>{const B=t.getBaseTarget(z),H=t.getValue(z);H&&(H.liveStyle=!0),R[z]=B??null}),b.push({animation:R})}let C=!!b.length;return r&&(y.initial===!1||y.initial===y.animate)&&!t.manuallyAnimateOnMount&&(C=!1),r=!1,C?a(b):Promise.resolve()}function h(p,y){var v;if(s[p].isActive===y)return Promise.resolve();(v=t.variantChildren)===null||v===void 0||v.forEach(S=>{var E;return(E=S.animationState)===null||E===void 0?void 0:E.setActive(p,y)}),s[p].isActive=y;const b=d(p);for(const S in s)s[S].protectedKeys={};return b}return{animateChanges:d,setActive:h,setAnimateFunction:c,getState:()=>s,reset:()=>{s=Sy(),r=!0}}}function X5(t,a){return typeof a=="string"?a!==t:Array.isArray(a)?!by(a,t):!1}function Ei(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function Sy(){return{animate:Ei(!0),whileInView:Ei(),whileHover:Ei(),whileTap:Ei(),whileDrag:Ei(),whileFocus:Ei(),exit:Ei()}}class Ma{constructor(a){this.isMounted=!1,this.node=a}update(){}}class K5 extends Ma{constructor(a){super(a),a.animationState||(a.animationState=Q5(a))}updateAnimationControlsSubscription(){const{animate:a}=this.node.getProps();go(a)&&(this.unmountControls=a.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:a}=this.node.getProps(),{animate:s}=this.node.prevProps||{};a!==s&&this.updateAnimationControlsSubscription()}unmount(){var a;this.node.animationState.reset(),(a=this.unmountControls)===null||a===void 0||a.call(this)}}let Z5=0;class W5 extends Ma{constructor(){super(...arguments),this.id=Z5++}update(){if(!this.node.presenceContext)return;const{isPresent:a,onExitComplete:s}=this.node.presenceContext,{isPresent:r}=this.node.prevPresenceContext||{};if(!this.node.animationState||a===r)return;const o=this.node.animationState.setActive("exit",!a);s&&!a&&o.then(()=>{s(this.id)})}mount(){const{register:a,onExitComplete:s}=this.node.presenceContext||{};s&&s(this.id),a&&(this.unmount=a(this.id))}unmount(){}}const $5={animation:{Feature:K5},exit:{Feature:W5}};function qr(t,a,s,r={passive:!0}){return t.addEventListener(a,s,r),()=>t.removeEventListener(a,s)}function Fr(t){return{point:{x:t.pageX,y:t.pageY}}}const J5=t=>a=>Kc(a)&&t(a,Fr(a));function Gr(t,a,s,r){return qr(t,a,J5(s),r)}function wy({top:t,left:a,right:s,bottom:r}){return{x:{min:a,max:s},y:{min:t,max:r}}}function I5({x:t,y:a}){return{top:a.min,right:t.max,bottom:a.max,left:t.min}}function e4(t,a){if(!a)return t;const s=a({x:t.left,y:t.top}),r=a({x:t.right,y:t.bottom});return{top:s.y,left:s.x,bottom:r.y,right:r.x}}const Ey=1e-4,t4=1-Ey,n4=1+Ey,Ty=.01,a4=0-Ty,i4=0+Ty;function Ut(t){return t.max-t.min}function s4(t,a,s){return Math.abs(t-a)<=s}function Cy(t,a,s,r=.5){t.origin=r,t.originPoint=Ie(a.min,a.max,t.origin),t.scale=Ut(s)/Ut(a),t.translate=Ie(s.min,s.max,t.origin)-t.originPoint,(t.scale>=t4&&t.scale<=n4||isNaN(t.scale))&&(t.scale=1),(t.translate>=a4&&t.translate<=i4||isNaN(t.translate))&&(t.translate=0)}function Yr(t,a,s,r){Cy(t.x,a.x,s.x,r?r.originX:void 0),Cy(t.y,a.y,s.y,r?r.originY:void 0)}function jy(t,a,s){t.min=s.min+a.min,t.max=t.min+Ut(a)}function r4(t,a,s){jy(t.x,a.x,s.x),jy(t.y,a.y,s.y)}function Ay(t,a,s){t.min=a.min-s.min,t.max=t.min+Ut(a)}function Qr(t,a,s){Ay(t.x,a.x,s.x),Ay(t.y,a.y,s.y)}const Dy=()=>({translate:0,scale:1,origin:0,originPoint:0}),bs=()=>({x:Dy(),y:Dy()}),_y=()=>({min:0,max:0}),lt=()=>({x:_y(),y:_y()});function on(t){return[t("x"),t("y")]}function Of(t){return t===void 0||t===1}function Vf({scale:t,scaleX:a,scaleY:s}){return!Of(t)||!Of(a)||!Of(s)}function Ti(t){return Vf(t)||My(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function My(t){return Ny(t.x)||Ny(t.y)}function Ny(t){return t&&t!=="0%"}function Co(t,a,s){const r=t-s,o=a*r;return s+o}function Ry(t,a,s,r,o){return o!==void 0&&(t=Co(t,o,r)),Co(t,s,r)+a}function zf(t,a=0,s=1,r,o){t.min=Ry(t.min,a,s,r,o),t.max=Ry(t.max,a,s,r,o)}function Oy(t,{x:a,y:s}){zf(t.x,a.translate,a.scale,a.originPoint),zf(t.y,s.translate,s.scale,s.originPoint)}const Vy=.999999999999,zy=1.0000000000001;function l4(t,a,s,r=!1){const o=s.length;if(!o)return;a.x=a.y=1;let c,d;for(let h=0;h<o;h++){c=s[h],d=c.projectionDelta;const{visualElement:p}=c.options;p&&p.props.style&&p.props.style.display==="contents"||(r&&c.options.layoutScroll&&c.scroll&&c!==c.root&&Ss(t,{x:-c.scroll.offset.x,y:-c.scroll.offset.y}),d&&(a.x*=d.x.scale,a.y*=d.y.scale,Oy(t,d)),r&&Ti(c.latestValues)&&Ss(t,c.latestValues))}a.x<zy&&a.x>Vy&&(a.x=1),a.y<zy&&a.y>Vy&&(a.y=1)}function xs(t,a){t.min=t.min+a,t.max=t.max+a}function ky(t,a,s,r,o=.5){const c=Ie(t.min,t.max,o);zf(t,a,s,c,r)}function Ss(t,a){ky(t.x,a.x,a.scaleX,a.scale,a.originX),ky(t.y,a.y,a.scaleY,a.scale,a.originY)}function Ly(t,a){return wy(e4(t.getBoundingClientRect(),a))}function o4(t,a,s){const r=Ly(t,s),{scroll:o}=a;return o&&(xs(r.x,o.offset.x),xs(r.y,o.offset.y)),r}const Uy=({current:t})=>t?t.ownerDocument.defaultView:null,By=(t,a)=>Math.abs(t-a);function u4(t,a){const s=By(t.x,a.x),r=By(t.y,a.y);return Math.sqrt(s**2+r**2)}class Hy{constructor(a,s,{transformPagePoint:r,contextWindow:o,dragSnapToOrigin:c=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const b=Lf(this.lastMoveEventInfo,this.history),S=this.startEvent!==null,E=u4(b.offset,{x:0,y:0})>=3;if(!S&&!E)return;const{point:N}=b,{timestamp:C}=At;this.history.push({...N,timestamp:C});const{onStart:R,onMove:z}=this.handlers;S||(R&&R(this.lastMoveEvent,b),this.startEvent=this.lastMoveEvent),z&&z(this.lastMoveEvent,b)},this.handlePointerMove=(b,S)=>{this.lastMoveEvent=b,this.lastMoveEventInfo=kf(S,this.transformPagePoint),Ke.update(this.updatePoint,!0)},this.handlePointerUp=(b,S)=>{this.end();const{onEnd:E,onSessionEnd:N,resumeAnimation:C}=this.handlers;if(this.dragSnapToOrigin&&C&&C(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const R=Lf(b.type==="pointercancel"?this.lastMoveEventInfo:kf(S,this.transformPagePoint),this.history);this.startEvent&&E&&E(b,R),N&&N(b,R)},!Kc(a))return;this.dragSnapToOrigin=c,this.handlers=s,this.transformPagePoint=r,this.contextWindow=o||window;const d=Fr(a),h=kf(d,this.transformPagePoint),{point:p}=h,{timestamp:y}=At;this.history=[{...p,timestamp:y}];const{onSessionStart:v}=s;v&&v(a,Lf(h,this.history)),this.removeListeners=Pr(Gr(this.contextWindow,"pointermove",this.handlePointerMove),Gr(this.contextWindow,"pointerup",this.handlePointerUp),Gr(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(a){this.handlers=a}end(){this.removeListeners&&this.removeListeners(),Aa(this.updatePoint)}}function kf(t,a){return a?{point:a(t.point)}:t}function Py(t,a){return{x:t.x-a.x,y:t.y-a.y}}function Lf({point:t},a){return{point:t,delta:Py(t,qy(a)),offset:Py(t,c4(a)),velocity:f4(a,.1)}}function c4(t){return t[0]}function qy(t){return t[t.length-1]}function f4(t,a){if(t.length<2)return{x:0,y:0};let s=t.length-1,r=null;const o=qy(t);for(;s>=0&&(r=t[s],!(o.timestamp-r.timestamp>$n(a)));)s--;if(!r)return{x:0,y:0};const c=Jn(o.timestamp-r.timestamp);if(c===0)return{x:0,y:0};const d={x:(o.x-r.x)/c,y:(o.y-r.y)/c};return d.x===1/0&&(d.x=0),d.y===1/0&&(d.y=0),d}function d4(t,{min:a,max:s},r){return a!==void 0&&t<a?t=r?Ie(a,t,r.min):Math.max(t,a):s!==void 0&&t>s&&(t=r?Ie(s,t,r.max):Math.min(t,s)),t}function Fy(t,a,s){return{min:a!==void 0?t.min+a:void 0,max:s!==void 0?t.max+s-(t.max-t.min):void 0}}function h4(t,{top:a,left:s,bottom:r,right:o}){return{x:Fy(t.x,s,o),y:Fy(t.y,a,r)}}function Gy(t,a){let s=a.min-t.min,r=a.max-t.max;return a.max-a.min<t.max-t.min&&([s,r]=[r,s]),{min:s,max:r}}function m4(t,a){return{x:Gy(t.x,a.x),y:Gy(t.y,a.y)}}function p4(t,a){let s=.5;const r=Ut(t),o=Ut(a);return o>r?s=ds(a.min,a.max-r,t.min):r>o&&(s=ds(t.min,t.max-o,a.min)),In(0,1,s)}function y4(t,a){const s={};return a.min!==void 0&&(s.min=a.min-t.min),a.max!==void 0&&(s.max=a.max-t.min),s}const Uf=.35;function g4(t=Uf){return t===!1?t=0:t===!0&&(t=Uf),{x:Yy(t,"left","right"),y:Yy(t,"top","bottom")}}function Yy(t,a,s){return{min:Qy(t,a),max:Qy(t,s)}}function Qy(t,a){return typeof t=="number"?t:t[a]||0}const v4=new WeakMap;class b4{constructor(a){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=lt(),this.visualElement=a}start(a,{snapToCursor:s=!1}={}){const{presenceContext:r}=this.visualElement;if(r&&r.isPresent===!1)return;const o=v=>{const{dragSnapToOrigin:b}=this.getProps();b?this.pauseAnimation():this.stopAnimation(),s&&this.snapToCursor(Fr(v).point)},c=(v,b)=>{const{drag:S,dragPropagation:E,onDragStart:N}=this.getProps();if(S&&!E&&(this.openDragLock&&this.openDragLock(),this.openDragLock=p6(S),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),on(R=>{let z=this.getAxisMotionValue(R).get()||0;if(Ln.test(z)){const{projection:B}=this.visualElement;if(B&&B.layout){const H=B.layout.layoutBox[R];H&&(z=Ut(H)*(parseFloat(z)/100))}}this.originPoint[R]=z}),N&&Ke.postRender(()=>N(v,b)),ff(this.visualElement,"transform");const{animationState:C}=this.visualElement;C&&C.setActive("whileDrag",!0)},d=(v,b)=>{const{dragPropagation:S,dragDirectionLock:E,onDirectionLock:N,onDrag:C}=this.getProps();if(!S&&!this.openDragLock)return;const{offset:R}=b;if(E&&this.currentDirection===null){this.currentDirection=x4(R),this.currentDirection!==null&&N&&N(this.currentDirection);return}this.updateAxis("x",b.point,R),this.updateAxis("y",b.point,R),this.visualElement.render(),C&&C(v,b)},h=(v,b)=>this.stop(v,b),p=()=>on(v=>{var b;return this.getAnimationState(v)==="paused"&&((b=this.getAxisMotionValue(v).animation)===null||b===void 0?void 0:b.play())}),{dragSnapToOrigin:y}=this.getProps();this.panSession=new Hy(a,{onSessionStart:o,onStart:c,onMove:d,onSessionEnd:h,resumeAnimation:p},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:y,contextWindow:Uy(this.visualElement)})}stop(a,s){const r=this.isDragging;if(this.cancel(),!r)return;const{velocity:o}=s;this.startAnimation(o);const{onDragEnd:c}=this.getProps();c&&Ke.postRender(()=>c(a,s))}cancel(){this.isDragging=!1;const{projection:a,animationState:s}=this.visualElement;a&&(a.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:r}=this.getProps();!r&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),s&&s.setActive("whileDrag",!1)}updateAxis(a,s,r){const{drag:o}=this.getProps();if(!r||!jo(a,o,this.currentDirection))return;const c=this.getAxisMotionValue(a);let d=this.originPoint[a]+r[a];this.constraints&&this.constraints[a]&&(d=d4(d,this.constraints[a],this.elastic[a])),c.set(d)}resolveConstraints(){var a;const{dragConstraints:s,dragElastic:r}=this.getProps(),o=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):(a=this.visualElement.projection)===null||a===void 0?void 0:a.layout,c=this.constraints;s&&ms(s)?this.constraints||(this.constraints=this.resolveRefConstraints()):s&&o?this.constraints=h4(o.layoutBox,s):this.constraints=!1,this.elastic=g4(r),c!==this.constraints&&o&&this.constraints&&!this.hasMutatedConstraints&&on(d=>{this.constraints!==!1&&this.getAxisMotionValue(d)&&(this.constraints[d]=y4(o.layoutBox[d],this.constraints[d]))})}resolveRefConstraints(){const{dragConstraints:a,onMeasureDragConstraints:s}=this.getProps();if(!a||!ms(a))return!1;const r=a.current,{projection:o}=this.visualElement;if(!o||!o.layout)return!1;const c=o4(r,o.root,this.visualElement.getTransformPagePoint());let d=m4(o.layout.layoutBox,c);if(s){const h=s(I5(d));this.hasMutatedConstraints=!!h,h&&(d=wy(h))}return d}startAnimation(a){const{drag:s,dragMomentum:r,dragElastic:o,dragTransition:c,dragSnapToOrigin:d,onDragTransitionEnd:h}=this.getProps(),p=this.constraints||{},y=on(v=>{if(!jo(v,s,this.currentDirection))return;let b=p&&p[v]||{};d&&(b={min:0,max:0});const S=o?200:1e6,E=o?40:1e7,N={type:"inertia",velocity:r?a[v]:0,bounceStiffness:S,bounceDamping:E,timeConstant:750,restDelta:1,restSpeed:10,...c,...b};return this.startAxisValueAnimation(v,N)});return Promise.all(y).then(h)}startAxisValueAnimation(a,s){const r=this.getAxisMotionValue(a);return ff(this.visualElement,a),r.start(Nf(a,r,0,s,this.visualElement,!1))}stopAnimation(){on(a=>this.getAxisMotionValue(a).stop())}pauseAnimation(){on(a=>{var s;return(s=this.getAxisMotionValue(a).animation)===null||s===void 0?void 0:s.pause()})}getAnimationState(a){var s;return(s=this.getAxisMotionValue(a).animation)===null||s===void 0?void 0:s.state}getAxisMotionValue(a){const s=`_drag${a.toUpperCase()}`,r=this.visualElement.getProps(),o=r[s];return o||this.visualElement.getValue(a,(r.initial?r.initial[a]:void 0)||0)}snapToCursor(a){on(s=>{const{drag:r}=this.getProps();if(!jo(s,r,this.currentDirection))return;const{projection:o}=this.visualElement,c=this.getAxisMotionValue(s);if(o&&o.layout){const{min:d,max:h}=o.layout.layoutBox[s];c.set(a[s]-Ie(d,h,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:a,dragConstraints:s}=this.getProps(),{projection:r}=this.visualElement;if(!ms(s)||!r||!this.constraints)return;this.stopAnimation();const o={x:0,y:0};on(d=>{const h=this.getAxisMotionValue(d);if(h&&this.constraints!==!1){const p=h.get();o[d]=p4({min:p,max:p},this.constraints[d])}});const{transformTemplate:c}=this.visualElement.getProps();this.visualElement.current.style.transform=c?c({},""):"none",r.root&&r.root.updateScroll(),r.updateLayout(),this.resolveConstraints(),on(d=>{if(!jo(d,a,null))return;const h=this.getAxisMotionValue(d),{min:p,max:y}=this.constraints[d];h.set(Ie(p,y,o[d]))})}addListeners(){if(!this.visualElement.current)return;v4.set(this.visualElement,this);const a=this.visualElement.current,s=Gr(a,"pointerdown",p=>{const{drag:y,dragListener:v=!0}=this.getProps();y&&v&&this.start(p)}),r=()=>{const{dragConstraints:p}=this.getProps();ms(p)&&p.current&&(this.constraints=this.resolveRefConstraints())},{projection:o}=this.visualElement,c=o.addEventListener("measure",r);o&&!o.layout&&(o.root&&o.root.updateScroll(),o.updateLayout()),Ke.read(r);const d=qr(window,"resize",()=>this.scalePositionWithinConstraints()),h=o.addEventListener("didUpdate",({delta:p,hasLayoutChanged:y})=>{this.isDragging&&y&&(on(v=>{const b=this.getAxisMotionValue(v);b&&(this.originPoint[v]+=p[v].translate,b.set(b.get()+p[v].translate))}),this.visualElement.render())});return()=>{d(),s(),c(),h&&h()}}getProps(){const a=this.visualElement.getProps(),{drag:s=!1,dragDirectionLock:r=!1,dragPropagation:o=!1,dragConstraints:c=!1,dragElastic:d=Uf,dragMomentum:h=!0}=a;return{...a,drag:s,dragDirectionLock:r,dragPropagation:o,dragConstraints:c,dragElastic:d,dragMomentum:h}}}function jo(t,a,s){return(a===!0||a===t)&&(s===null||s===t)}function x4(t,a=10){let s=null;return Math.abs(t.y)>a?s="y":Math.abs(t.x)>a&&(s="x"),s}class S4 extends Ma{constructor(a){super(a),this.removeGroupControls=$t,this.removeListeners=$t,this.controls=new b4(a)}mount(){const{dragControls:a}=this.node.getProps();a&&(this.removeGroupControls=a.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||$t}unmount(){this.removeGroupControls(),this.removeListeners()}}const Xy=t=>(a,s)=>{t&&Ke.postRender(()=>t(a,s))};class w4 extends Ma{constructor(){super(...arguments),this.removePointerDownListener=$t}onPointerDown(a){this.session=new Hy(a,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:Uy(this.node)})}createPanHandlers(){const{onPanSessionStart:a,onPanStart:s,onPan:r,onPanEnd:o}=this.node.getProps();return{onSessionStart:Xy(a),onStart:Xy(s),onMove:r,onEnd:(c,d)=>{delete this.session,o&&Ke.postRender(()=>o(c,d))}}}mount(){this.removePointerDownListener=Gr(this.node.current,"pointerdown",a=>this.onPointerDown(a))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}const Ao={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function Ky(t,a){return a.max===a.min?0:t/(a.max-a.min)*100}const Xr={correct:(t,a)=>{if(!a.target)return t;if(typeof t=="string")if(ge.test(t))t=parseFloat(t);else return t;const s=Ky(t,a.target.x),r=Ky(t,a.target.y);return`${s}% ${r}%`}},E4={correct:(t,{treeScale:a,projectionDelta:s})=>{const r=t,o=_a.parse(t);if(o.length>5)return r;const c=_a.createTransformer(t),d=typeof o[0]!="number"?1:0,h=s.x.scale*a.x,p=s.y.scale*a.y;o[0+d]/=h,o[1+d]/=p;const y=Ie(h,p,.5);return typeof o[2+d]=="number"&&(o[2+d]/=y),typeof o[3+d]=="number"&&(o[3+d]/=y),c(o)}};class T4 extends w.Component{componentDidMount(){const{visualElement:a,layoutGroup:s,switchLayoutGroup:r,layoutId:o}=this.props,{projection:c}=a;P6(C4),c&&(s.group&&s.group.add(c),r&&r.register&&o&&r.register(c),c.root.didUpdate(),c.addEventListener("animationComplete",()=>{this.safeToRemove()}),c.setOptions({...c.options,onExitComplete:()=>this.safeToRemove()})),Ao.hasEverUpdated=!0}getSnapshotBeforeUpdate(a){const{layoutDependency:s,visualElement:r,drag:o,isPresent:c}=this.props,d=r.projection;return d&&(d.isPresent=c,o||a.layoutDependency!==s||s===void 0||a.isPresent!==c?d.willUpdate():this.safeToRemove(),a.isPresent!==c&&(c?d.promote():d.relegate()||Ke.postRender(()=>{const h=d.getStack();(!h||!h.members.length)&&this.safeToRemove()}))),null}componentDidUpdate(){const{projection:a}=this.props.visualElement;a&&(a.root.didUpdate(),Xc.postRender(()=>{!a.currentAnimation&&a.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:a,layoutGroup:s,switchLayoutGroup:r}=this.props,{projection:o}=a;o&&(o.scheduleCheckAfterUnmount(),s&&s.group&&s.group.remove(o),r&&r.deregister&&r.deregister(o))}safeToRemove(){const{safeToRemove:a}=this.props;a&&a()}render(){return null}}function Zy(t){const[a,s]=k0(),r=w.useContext(Oc);return m.jsx(T4,{...t,layoutGroup:r,switchLayoutGroup:w.useContext(lp),isPresent:a,safeToRemove:s})}const C4={borderRadius:{...Xr,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:Xr,borderTopRightRadius:Xr,borderBottomLeftRadius:Xr,borderBottomRightRadius:Xr,boxShadow:E4};function j4(t,a,s){const r=Rt(t)?t:Rr(t);return r.start(Nf("",r,a,s)),r.animation}function A4(t){return t instanceof SVGElement&&t.tagName!=="svg"}const D4=(t,a)=>t.depth-a.depth;class _4{constructor(){this.children=[],this.isDirty=!1}add(a){Lc(this.children,a),this.isDirty=!0}remove(a){Uc(this.children,a),this.isDirty=!0}forEach(a){this.isDirty&&this.children.sort(D4),this.isDirty=!1,this.children.forEach(a)}}function M4(t,a){const s=kn.now(),r=({timestamp:o})=>{const c=o-s;c>=a&&(Aa(r),t(c-a))};return Ke.read(r,!0),()=>Aa(r)}const Wy=["TopLeft","TopRight","BottomLeft","BottomRight"],N4=Wy.length,$y=t=>typeof t=="string"?parseFloat(t):t,Jy=t=>typeof t=="number"||ge.test(t);function R4(t,a,s,r,o,c){o?(t.opacity=Ie(0,s.opacity!==void 0?s.opacity:1,O4(r)),t.opacityExit=Ie(a.opacity!==void 0?a.opacity:1,0,V4(r))):c&&(t.opacity=Ie(a.opacity!==void 0?a.opacity:1,s.opacity!==void 0?s.opacity:1,r));for(let d=0;d<N4;d++){const h=`border${Wy[d]}Radius`;let p=Iy(a,h),y=Iy(s,h);if(p===void 0&&y===void 0)continue;p||(p=0),y||(y=0),p===0||y===0||Jy(p)===Jy(y)?(t[h]=Math.max(Ie($y(p),$y(y),r),0),(Ln.test(y)||Ln.test(p))&&(t[h]+="%")):t[h]=y}(a.rotate||s.rotate)&&(t.rotate=Ie(a.rotate||0,s.rotate||0,r))}function Iy(t,a){return t[a]!==void 0?t[a]:t.borderRadius}const O4=eg(0,.5,Op),V4=eg(.5,.95,$t);function eg(t,a,s){return r=>r<t?0:r>a?1:s(ds(t,a,r))}function tg(t,a){t.min=a.min,t.max=a.max}function un(t,a){tg(t.x,a.x),tg(t.y,a.y)}function ng(t,a){t.translate=a.translate,t.scale=a.scale,t.originPoint=a.originPoint,t.origin=a.origin}function ag(t,a,s,r,o){return t-=a,t=Co(t,1/s,r),o!==void 0&&(t=Co(t,1/o,r)),t}function z4(t,a=0,s=1,r=.5,o,c=t,d=t){if(Ln.test(a)&&(a=parseFloat(a),a=Ie(d.min,d.max,a/100)-d.min),typeof a!="number")return;let h=Ie(c.min,c.max,r);t===c&&(h-=a),t.min=ag(t.min,a,s,h,o),t.max=ag(t.max,a,s,h,o)}function ig(t,a,[s,r,o],c,d){z4(t,a[s],a[r],a[o],a.scale,c,d)}const k4=["x","scaleX","originX"],L4=["y","scaleY","originY"];function sg(t,a,s,r){ig(t.x,a,k4,s?s.x:void 0,r?r.x:void 0),ig(t.y,a,L4,s?s.y:void 0,r?r.y:void 0)}function rg(t){return t.translate===0&&t.scale===1}function lg(t){return rg(t.x)&&rg(t.y)}function og(t,a){return t.min===a.min&&t.max===a.max}function U4(t,a){return og(t.x,a.x)&&og(t.y,a.y)}function ug(t,a){return Math.round(t.min)===Math.round(a.min)&&Math.round(t.max)===Math.round(a.max)}function cg(t,a){return ug(t.x,a.x)&&ug(t.y,a.y)}function fg(t){return Ut(t.x)/Ut(t.y)}function dg(t,a){return t.translate===a.translate&&t.scale===a.scale&&t.originPoint===a.originPoint}class B4{constructor(){this.members=[]}add(a){Lc(this.members,a),a.scheduleRender()}remove(a){if(Uc(this.members,a),a===this.prevLead&&(this.prevLead=void 0),a===this.lead){const s=this.members[this.members.length-1];s&&this.promote(s)}}relegate(a){const s=this.members.findIndex(o=>a===o);if(s===0)return!1;let r;for(let o=s;o>=0;o--){const c=this.members[o];if(c.isPresent!==!1){r=c;break}}return r?(this.promote(r),!0):!1}promote(a,s){const r=this.lead;if(a!==r&&(this.prevLead=r,this.lead=a,a.show(),r)){r.instance&&r.scheduleRender(),a.scheduleRender(),a.resumeFrom=r,s&&(a.resumeFrom.preserveOpacity=!0),r.snapshot&&(a.snapshot=r.snapshot,a.snapshot.latestValues=r.animationValues||r.latestValues),a.root&&a.root.isUpdating&&(a.isLayoutDirty=!0);const{crossfade:o}=a.options;o===!1&&r.hide()}}exitAnimationComplete(){this.members.forEach(a=>{const{options:s,resumingFrom:r}=a;s.onExitComplete&&s.onExitComplete(),r&&r.options.onExitComplete&&r.options.onExitComplete()})}scheduleRender(){this.members.forEach(a=>{a.instance&&a.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function H4(t,a,s){let r="";const o=t.x.translate/a.x,c=t.y.translate/a.y,d=(s==null?void 0:s.z)||0;if((o||c||d)&&(r=`translate3d(${o}px, ${c}px, ${d}px) `),(a.x!==1||a.y!==1)&&(r+=`scale(${1/a.x}, ${1/a.y}) `),s){const{transformPerspective:y,rotate:v,rotateX:b,rotateY:S,skewX:E,skewY:N}=s;y&&(r=`perspective(${y}px) ${r}`),v&&(r+=`rotate(${v}deg) `),b&&(r+=`rotateX(${b}deg) `),S&&(r+=`rotateY(${S}deg) `),E&&(r+=`skewX(${E}deg) `),N&&(r+=`skewY(${N}deg) `)}const h=t.x.scale*a.x,p=t.y.scale*a.y;return(h!==1||p!==1)&&(r+=`scale(${h}, ${p})`),r||"none"}const Bf=["","X","Y","Z"],P4={visibility:"hidden"},hg=1e3;let q4=0;function Hf(t,a,s,r){const{latestValues:o}=a;o[t]&&(s[t]=o[t],a.setStaticValue(t,0),r&&(r[t]=0))}function mg(t){if(t.hasCheckedOptimisedAppear=!0,t.root===t)return;const{visualElement:a}=t.options;if(!a)return;const s=jp(a);if(window.MotionHasOptimisedAnimation(s,"transform")){const{layout:o,layoutId:c}=t.options;window.MotionCancelOptimisedAnimation(s,"transform",Ke,!(o||c))}const{parent:r}=t;r&&!r.hasCheckedOptimisedAppear&&mg(r)}function pg({attachResizeListener:t,defaultParent:a,measureScroll:s,checkIsScrollRoot:r,resetTransform:o}){return class{constructor(d={},h=a==null?void 0:a()){this.id=q4++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,this.nodes.forEach(Y4),this.nodes.forEach(W4),this.nodes.forEach($4),this.nodes.forEach(Q4)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=d,this.root=h?h.root||h:this,this.path=h?[...h.path,h]:[],this.parent=h,this.depth=h?h.depth+1:0;for(let p=0;p<this.path.length;p++)this.path[p].shouldResetTransform=!0;this.root===this&&(this.nodes=new _4)}addEventListener(d,h){return this.eventHandlers.has(d)||this.eventHandlers.set(d,new Hc),this.eventHandlers.get(d).add(h)}notifyListeners(d,...h){const p=this.eventHandlers.get(d);p&&p.notify(...h)}hasListeners(d){return this.eventHandlers.has(d)}mount(d,h=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=A4(d),this.instance=d;const{layoutId:p,layout:y,visualElement:v}=this.options;if(v&&!v.current&&v.mount(d),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),h&&(y||p)&&(this.isLayoutDirty=!0),t){let b;const S=()=>this.root.updateBlockedByResize=!1;t(d,()=>{this.root.updateBlockedByResize=!0,b&&b(),b=M4(S,250),Ao.hasAnimatedSinceResize&&(Ao.hasAnimatedSinceResize=!1,this.nodes.forEach(gg))})}p&&this.root.registerSharedNode(p,this),this.options.animate!==!1&&v&&(p||y)&&this.addEventListener("didUpdate",({delta:b,hasLayoutChanged:S,hasRelativeLayoutChanged:E,layout:N})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}const C=this.options.transition||v.getDefaultTransition()||n3,{onLayoutAnimationStart:R,onLayoutAnimationComplete:z}=v.getProps(),B=!this.targetLayout||!cg(this.targetLayout,N),H=!S&&E;if(this.options.layoutRoot||this.resumeFrom||H||S&&(B||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(b,H);const W={...Pc(C,"layout"),onPlay:R,onComplete:z};(v.shouldReduceMotion||this.options.layoutRoot)&&(W.delay=0,W.type=!1),this.startAnimation(W)}else S||gg(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=N})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const d=this.getStack();d&&d.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,Aa(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(J4),this.animationId++)}getTransformTemplate(){const{visualElement:d}=this.options;return d&&d.getProps().transformTemplate}willUpdate(d=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&mg(this),!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let v=0;v<this.path.length;v++){const b=this.path[v];b.shouldResetTransform=!0,b.updateScroll("snapshot"),b.options.layoutRoot&&b.willUpdate(!1)}const{layoutId:h,layout:p}=this.options;if(h===void 0&&!p)return;const y=this.getTransformTemplate();this.prevTransformTemplateValue=y?y(this.latestValues,""):void 0,this.updateSnapshot(),d&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(yg);return}this.isUpdating||this.nodes.forEach(K4),this.isUpdating=!1,this.nodes.forEach(Z4),this.nodes.forEach(F4),this.nodes.forEach(G4),this.clearAllSnapshots();const h=kn.now();At.delta=In(0,1e3/60,h-At.timestamp),At.timestamp=h,At.isProcessing=!0,Qc.update.process(At),Qc.preRender.process(At),Qc.render.process(At),At.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,Xc.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(X4),this.sharedNodes.forEach(I4)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,Ke.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){Ke.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure(),this.snapshot&&!Ut(this.snapshot.measuredBox.x)&&!Ut(this.snapshot.measuredBox.y)&&(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let p=0;p<this.path.length;p++)this.path[p].updateScroll();const d=this.layout;this.layout=this.measure(!1),this.layoutCorrected=lt(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:h}=this.options;h&&h.notify("LayoutMeasure",this.layout.layoutBox,d?d.layoutBox:void 0)}updateScroll(d="measure"){let h=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===d&&(h=!1),h){const p=r(this.instance);this.scroll={animationId:this.root.animationId,phase:d,isRoot:p,offset:s(this.instance),wasRoot:this.scroll?this.scroll.isRoot:p}}}resetTransform(){if(!o)return;const d=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,h=this.projectionDelta&&!lg(this.projectionDelta),p=this.getTransformTemplate(),y=p?p(this.latestValues,""):void 0,v=y!==this.prevTransformTemplateValue;d&&(h||Ti(this.latestValues)||v)&&(o(this.instance,y),this.shouldResetTransform=!1,this.scheduleRender())}measure(d=!0){const h=this.measurePageBox();let p=this.removeElementScroll(h);return d&&(p=this.removeTransform(p)),a3(p),{animationId:this.root.animationId,measuredBox:h,layoutBox:p,latestValues:{},source:this.id}}measurePageBox(){var d;const{visualElement:h}=this.options;if(!h)return lt();const p=h.measureViewportBox();if(!(((d=this.scroll)===null||d===void 0?void 0:d.wasRoot)||this.path.some(i3))){const{scroll:v}=this.root;v&&(xs(p.x,v.offset.x),xs(p.y,v.offset.y))}return p}removeElementScroll(d){var h;const p=lt();if(un(p,d),!((h=this.scroll)===null||h===void 0)&&h.wasRoot)return p;for(let y=0;y<this.path.length;y++){const v=this.path[y],{scroll:b,options:S}=v;v!==this.root&&b&&S.layoutScroll&&(b.wasRoot&&un(p,d),xs(p.x,b.offset.x),xs(p.y,b.offset.y))}return p}applyTransform(d,h=!1){const p=lt();un(p,d);for(let y=0;y<this.path.length;y++){const v=this.path[y];!h&&v.options.layoutScroll&&v.scroll&&v!==v.root&&Ss(p,{x:-v.scroll.offset.x,y:-v.scroll.offset.y}),Ti(v.latestValues)&&Ss(p,v.latestValues)}return Ti(this.latestValues)&&Ss(p,this.latestValues),p}removeTransform(d){const h=lt();un(h,d);for(let p=0;p<this.path.length;p++){const y=this.path[p];if(!y.instance||!Ti(y.latestValues))continue;Vf(y.latestValues)&&y.updateSnapshot();const v=lt(),b=y.measurePageBox();un(v,b),sg(h,y.latestValues,y.snapshot?y.snapshot.layoutBox:void 0,v)}return Ti(this.latestValues)&&sg(h,this.latestValues),h}setTargetDelta(d){this.targetDelta=d,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(d){this.options={...this.options,...d,crossfade:d.crossfade!==void 0?d.crossfade:!0}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==At.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(d=!1){var h;const p=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=p.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=p.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=p.isSharedProjectionDirty);const y=!!this.resumingFrom||this!==p;if(!(d||y&&this.isSharedProjectionDirty||this.isProjectionDirty||!((h=this.parent)===null||h===void 0)&&h.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;const{layout:b,layoutId:S}=this.options;if(!(!this.layout||!(b||S))){if(this.resolvedRelativeTargetAt=At.timestamp,!this.targetDelta&&!this.relativeTarget){const E=this.getClosestProjectingParent();E&&E.layout&&this.animationProgress!==1?(this.relativeParent=E,this.forceRelativeParentToResolveTarget(),this.relativeTarget=lt(),this.relativeTargetOrigin=lt(),Qr(this.relativeTargetOrigin,this.layout.layoutBox,E.layout.layoutBox),un(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(!(!this.relativeTarget&&!this.targetDelta)&&(this.target||(this.target=lt(),this.targetWithTransforms=lt()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),r4(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):un(this.target,this.layout.layoutBox),Oy(this.target,this.targetDelta)):un(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget)){this.attemptToResolveRelativeTarget=!1;const E=this.getClosestProjectingParent();E&&!!E.resumingFrom==!!this.resumingFrom&&!E.options.layoutScroll&&E.target&&this.animationProgress!==1?(this.relativeParent=E,this.forceRelativeParentToResolveTarget(),this.relativeTarget=lt(),this.relativeTargetOrigin=lt(),Qr(this.relativeTargetOrigin,this.target,E.target),un(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}}}getClosestProjectingParent(){if(!(!this.parent||Vf(this.parent.latestValues)||My(this.parent.latestValues)))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var d;const h=this.getLead(),p=!!this.resumingFrom||this!==h;let y=!0;if((this.isProjectionDirty||!((d=this.parent)===null||d===void 0)&&d.isProjectionDirty)&&(y=!1),p&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(y=!1),this.resolvedRelativeTargetAt===At.timestamp&&(y=!1),y)return;const{layout:v,layoutId:b}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(v||b))return;un(this.layoutCorrected,this.layout.layoutBox);const S=this.treeScale.x,E=this.treeScale.y;l4(this.layoutCorrected,this.treeScale,this.path,p),h.layout&&!h.target&&(this.treeScale.x!==1||this.treeScale.y!==1)&&(h.target=h.layout.layoutBox,h.targetWithTransforms=lt());const{target:N}=h;if(!N){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}!this.projectionDelta||!this.prevProjectionDelta?this.createProjectionDeltas():(ng(this.prevProjectionDelta.x,this.projectionDelta.x),ng(this.prevProjectionDelta.y,this.projectionDelta.y)),Yr(this.projectionDelta,this.layoutCorrected,N,this.latestValues),(this.treeScale.x!==S||this.treeScale.y!==E||!dg(this.projectionDelta.x,this.prevProjectionDelta.x)||!dg(this.projectionDelta.y,this.prevProjectionDelta.y))&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",N))}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(d=!0){var h;if((h=this.options.visualElement)===null||h===void 0||h.scheduleRender(),d){const p=this.getStack();p&&p.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=bs(),this.projectionDelta=bs(),this.projectionDeltaWithTransform=bs()}setAnimationOrigin(d,h=!1){const p=this.snapshot,y=p?p.latestValues:{},v={...this.latestValues},b=bs();(!this.relativeParent||!this.relativeParent.options.layoutRoot)&&(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!h;const S=lt(),E=p?p.source:void 0,N=this.layout?this.layout.source:void 0,C=E!==N,R=this.getStack(),z=!R||R.members.length<=1,B=!!(C&&!z&&this.options.crossfade===!0&&!this.path.some(t3));this.animationProgress=0;let H;this.mixTargetDelta=W=>{const O=W/1e3;vg(b.x,d.x,O),vg(b.y,d.y,O),this.setTargetDelta(b),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(Qr(S,this.layout.layoutBox,this.relativeParent.layout.layoutBox),e3(this.relativeTarget,this.relativeTargetOrigin,S,O),H&&U4(this.relativeTarget,H)&&(this.isProjectionDirty=!1),H||(H=lt()),un(H,this.relativeTarget)),C&&(this.animationValues=v,R4(v,y,this.latestValues,O,B,z)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=O},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(d){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&(Aa(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=Ke.update(()=>{Ao.hasAnimatedSinceResize=!0,this.currentAnimation=j4(0,hg,{...d,onUpdate:h=>{this.mixTargetDelta(h),d.onUpdate&&d.onUpdate(h)},onStop:()=>{},onComplete:()=>{d.onComplete&&d.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const d=this.getStack();d&&d.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(hg),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const d=this.getLead();let{targetWithTransforms:h,target:p,layout:y,latestValues:v}=d;if(!(!h||!p||!y)){if(this!==d&&this.layout&&y&&Eg(this.options.animationType,this.layout.layoutBox,y.layoutBox)){p=this.target||lt();const b=Ut(this.layout.layoutBox.x);p.x.min=d.target.x.min,p.x.max=p.x.min+b;const S=Ut(this.layout.layoutBox.y);p.y.min=d.target.y.min,p.y.max=p.y.min+S}un(h,p),Ss(h,v),Yr(this.projectionDeltaWithTransform,this.layoutCorrected,h,v)}}registerSharedNode(d,h){this.sharedNodes.has(d)||this.sharedNodes.set(d,new B4),this.sharedNodes.get(d).add(h);const y=h.options.initialPromotionConfig;h.promote({transition:y?y.transition:void 0,preserveFollowOpacity:y&&y.shouldPreserveFollowOpacity?y.shouldPreserveFollowOpacity(h):void 0})}isLead(){const d=this.getStack();return d?d.lead===this:!0}getLead(){var d;const{layoutId:h}=this.options;return h?((d=this.getStack())===null||d===void 0?void 0:d.lead)||this:this}getPrevLead(){var d;const{layoutId:h}=this.options;return h?(d=this.getStack())===null||d===void 0?void 0:d.prevLead:void 0}getStack(){const{layoutId:d}=this.options;if(d)return this.root.sharedNodes.get(d)}promote({needsReset:d,transition:h,preserveFollowOpacity:p}={}){const y=this.getStack();y&&y.promote(this,p),d&&(this.projectionDelta=void 0,this.needsReset=!0),h&&this.setOptions({transition:h})}relegate(){const d=this.getStack();return d?d.relegate(this):!1}resetSkewAndRotation(){const{visualElement:d}=this.options;if(!d)return;let h=!1;const{latestValues:p}=d;if((p.z||p.rotate||p.rotateX||p.rotateY||p.rotateZ||p.skewX||p.skewY)&&(h=!0),!h)return;const y={};p.z&&Hf("z",d,y,this.animationValues);for(let v=0;v<Bf.length;v++)Hf(`rotate${Bf[v]}`,d,y,this.animationValues),Hf(`skew${Bf[v]}`,d,y,this.animationValues);d.render();for(const v in y)d.setStaticValue(v,y[v]),this.animationValues&&(this.animationValues[v]=y[v]);d.scheduleRender()}getProjectionStyles(d){var h,p;if(!this.instance||this.isSVG)return;if(!this.isVisible)return P4;const y={visibility:""},v=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,y.opacity="",y.pointerEvents=xo(d==null?void 0:d.pointerEvents)||"",y.transform=v?v(this.latestValues,""):"none",y;const b=this.getLead();if(!this.projectionDelta||!this.layout||!b.target){const C={};return this.options.layoutId&&(C.opacity=this.latestValues.opacity!==void 0?this.latestValues.opacity:1,C.pointerEvents=xo(d==null?void 0:d.pointerEvents)||""),this.hasProjected&&!Ti(this.latestValues)&&(C.transform=v?v({},""):"none",this.hasProjected=!1),C}const S=b.animationValues||b.latestValues;this.applyTransformsToTarget(),y.transform=H4(this.projectionDeltaWithTransform,this.treeScale,S),v&&(y.transform=v(S,y.transform));const{x:E,y:N}=this.projectionDelta;y.transformOrigin=`${E.origin*100}% ${N.origin*100}% 0`,b.animationValues?y.opacity=b===this?(p=(h=S.opacity)!==null&&h!==void 0?h:this.latestValues.opacity)!==null&&p!==void 0?p:1:this.preserveOpacity?this.latestValues.opacity:S.opacityExit:y.opacity=b===this?S.opacity!==void 0?S.opacity:"":S.opacityExit!==void 0?S.opacityExit:0;for(const C in Vr){if(S[C]===void 0)continue;const{correct:R,applyTo:z,isCSSVariable:B}=Vr[C],H=y.transform==="none"?S[C]:R(S[C],b);if(z){const W=z.length;for(let O=0;O<W;O++)y[z[O]]=H}else B?this.options.visualElement.renderState.vars[C]=H:y[C]=H}return this.options.layoutId&&(y.pointerEvents=b===this?xo(d==null?void 0:d.pointerEvents)||"":"none"),y}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(d=>{var h;return(h=d.currentAnimation)===null||h===void 0?void 0:h.stop()}),this.root.nodes.forEach(yg),this.root.sharedNodes.clear()}}}function F4(t){t.updateLayout()}function G4(t){var a;const s=((a=t.resumeFrom)===null||a===void 0?void 0:a.snapshot)||t.snapshot;if(t.isLead()&&t.layout&&s&&t.hasListeners("didUpdate")){const{layoutBox:r,measuredBox:o}=t.layout,{animationType:c}=t.options,d=s.source!==t.layout.source;c==="size"?on(b=>{const S=d?s.measuredBox[b]:s.layoutBox[b],E=Ut(S);S.min=r[b].min,S.max=S.min+E}):Eg(c,s.layoutBox,r)&&on(b=>{const S=d?s.measuredBox[b]:s.layoutBox[b],E=Ut(r[b]);S.max=S.min+E,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[b].max=t.relativeTarget[b].min+E)});const h=bs();Yr(h,r,s.layoutBox);const p=bs();d?Yr(p,t.applyTransform(o,!0),s.measuredBox):Yr(p,r,s.layoutBox);const y=!lg(h);let v=!1;if(!t.resumeFrom){const b=t.getClosestProjectingParent();if(b&&!b.resumeFrom){const{snapshot:S,layout:E}=b;if(S&&E){const N=lt();Qr(N,s.layoutBox,S.layoutBox);const C=lt();Qr(C,r,E.layoutBox),cg(N,C)||(v=!0),b.options.layoutRoot&&(t.relativeTarget=C,t.relativeTargetOrigin=N,t.relativeParent=b)}}}t.notifyListeners("didUpdate",{layout:r,snapshot:s,delta:p,layoutDelta:h,hasLayoutChanged:y,hasRelativeLayoutChanged:v})}else if(t.isLead()){const{onExitComplete:r}=t.options;r&&r()}t.options.transition=void 0}function Y4(t){t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function Q4(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function X4(t){t.clearSnapshot()}function yg(t){t.clearMeasurements()}function K4(t){t.isLayoutDirty=!1}function Z4(t){const{visualElement:a}=t.options;a&&a.getProps().onBeforeLayoutMeasure&&a.notify("BeforeLayoutMeasure"),t.resetTransform()}function gg(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function W4(t){t.resolveTargetDelta()}function $4(t){t.calcProjection()}function J4(t){t.resetSkewAndRotation()}function I4(t){t.removeLeadSnapshot()}function vg(t,a,s){t.translate=Ie(a.translate,0,s),t.scale=Ie(a.scale,1,s),t.origin=a.origin,t.originPoint=a.originPoint}function bg(t,a,s,r){t.min=Ie(a.min,s.min,r),t.max=Ie(a.max,s.max,r)}function e3(t,a,s,r){bg(t.x,a.x,s.x,r),bg(t.y,a.y,s.y,r)}function t3(t){return t.animationValues&&t.animationValues.opacityExit!==void 0}const n3={duration:.45,ease:[.4,0,.1,1]},xg=t=>typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),Sg=xg("applewebkit/")&&!xg("chrome/")?Math.round:$t;function wg(t){t.min=Sg(t.min),t.max=Sg(t.max)}function a3(t){wg(t.x),wg(t.y)}function Eg(t,a,s){return t==="position"||t==="preserve-aspect"&&!s4(fg(a),fg(s),.2)}function i3(t){var a;return t!==t.root&&((a=t.scroll)===null||a===void 0?void 0:a.wasRoot)}const s3=pg({attachResizeListener:(t,a)=>qr(t,"resize",a),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),Pf={current:void 0},Tg=pg({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!Pf.current){const t=new s3({});t.mount(window),t.setOptions({layoutScroll:!0}),Pf.current=t}return Pf.current},resetTransform:(t,a)=>{t.style.transform=a!==void 0?a:"none"},checkIsScrollRoot:t=>window.getComputedStyle(t).position==="fixed"}),r3={pan:{Feature:w4},drag:{Feature:S4,ProjectionNode:Tg,MeasureLayout:Zy}};function Cg(t,a,s){const{props:r}=t;t.animationState&&r.whileHover&&t.animationState.setActive("whileHover",s==="Start");const o="onHover"+s,c=r[o];c&&Ke.postRender(()=>c(a,Fr(a)))}class l3 extends Ma{mount(){const{current:a}=this.node;a&&(this.unmount=g6(a,(s,r)=>(Cg(this.node,r,"Start"),o=>Cg(this.node,o,"End"))))}unmount(){}}class o3 extends Ma{constructor(){super(...arguments),this.isActive=!1}onFocus(){let a=!1;try{a=this.node.current.matches(":focus-visible")}catch{a=!0}!a||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){!this.isActive||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=Pr(qr(this.node.current,"focus",()=>this.onFocus()),qr(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}function jg(t,a,s){const{props:r}=t;if(t.current instanceof HTMLButtonElement&&t.current.disabled)return;t.animationState&&r.whileTap&&t.animationState.setActive("whileTap",s==="Start");const o="onTap"+(s==="End"?"":s),c=r[o];c&&Ke.postRender(()=>c(a,Fr(a)))}class u3 extends Ma{mount(){const{current:a}=this.node;a&&(this.unmount=S6(a,(s,r)=>(jg(this.node,r,"Start"),(o,{success:c})=>jg(this.node,o,c?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}const qf=new WeakMap,Ff=new WeakMap,c3=t=>{const a=qf.get(t.target);a&&a(t)},f3=t=>{t.forEach(c3)};function d3({root:t,...a}){const s=t||document;Ff.has(s)||Ff.set(s,{});const r=Ff.get(s),o=JSON.stringify(a);return r[o]||(r[o]=new IntersectionObserver(f3,{root:t,...a})),r[o]}function h3(t,a,s){const r=d3(a);return qf.set(t,s),r.observe(t),()=>{qf.delete(t),r.unobserve(t)}}const m3={some:0,all:1};class p3 extends Ma{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:a={}}=this.node.getProps(),{root:s,margin:r,amount:o="some",once:c}=a,d={root:s?s.current:void 0,rootMargin:r,threshold:typeof o=="number"?o:m3[o]},h=p=>{const{isIntersecting:y}=p;if(this.isInView===y||(this.isInView=y,c&&!y&&this.hasEnteredView))return;y&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",y);const{onViewportEnter:v,onViewportLeave:b}=this.node.getProps(),S=y?v:b;S&&S(p)};return h3(this.node.current,d,h)}mount(){this.startObserver()}update(){if(typeof IntersectionObserver>"u")return;const{props:a,prevProps:s}=this.node;["amount","margin","root"].some(y3(a,s))&&this.startObserver()}unmount(){}}function y3({viewport:t={}},{viewport:a={}}={}){return s=>t[s]!==a[s]}const g3={inView:{Feature:p3},tap:{Feature:u3},focus:{Feature:o3},hover:{Feature:l3}},v3={layout:{ProjectionNode:Tg,MeasureLayout:Zy}},Gf={current:null},Ag={current:!1};function b3(){if(Ag.current=!0,!!zc)if(window.matchMedia){const t=window.matchMedia("(prefers-reduced-motion)"),a=()=>Gf.current=t.matches;t.addListener(a),a()}else Gf.current=!1}const x3=[...ey,Ot,_a],S3=t=>x3.find(Ip(t)),w3=new WeakMap;function E3(t,a,s){for(const r in a){const o=a[r],c=s[r];if(Rt(o))t.addValue(r,o);else if(Rt(c))t.addValue(r,Rr(o,{owner:t}));else if(c!==o)if(t.hasValue(r)){const d=t.getValue(r);d.liveStyle===!0?d.jump(o):d.hasAnimated||d.set(o)}else{const d=t.getStaticValue(r);t.addValue(r,Rr(d!==void 0?d:o,{owner:t}))}}for(const r in s)a[r]===void 0&&t.removeValue(r);return a}const Dg=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class T3{scrapeMotionValuesFromProps(a,s,r){return{}}constructor({parent:a,props:s,presenceContext:r,reducedMotionConfig:o,blockInitialAnimation:c,visualState:d},h={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=Tf,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{const E=kn.now();this.renderScheduledAt<E&&(this.renderScheduledAt=E,Ke.render(this.render,!1,!0))};const{latestValues:p,renderState:y,onUpdate:v}=d;this.onUpdate=v,this.latestValues=p,this.baseTarget={...p},this.initialValues=s.initial?{...p}:{},this.renderState=y,this.parent=a,this.props=s,this.presenceContext=r,this.depth=a?a.depth+1:0,this.reducedMotionConfig=o,this.options=h,this.blockInitialAnimation=!!c,this.isControllingVariants=vo(s),this.isVariantNode=ip(s),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(a&&a.current);const{willChange:b,...S}=this.scrapeMotionValuesFromProps(s,{},this);for(const E in S){const N=S[E];p[E]!==void 0&&Rt(N)&&N.set(p[E],!1)}}mount(a){this.current=a,w3.set(a,this),this.projection&&!this.projection.instance&&this.projection.mount(a),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((s,r)=>this.bindToMotionValue(r,s)),Ag.current||b3(),this.shouldReduceMotion=this.reducedMotionConfig==="never"?!1:this.reducedMotionConfig==="always"?!0:Gf.current,this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){this.projection&&this.projection.unmount(),Aa(this.notifyUpdate),Aa(this.render),this.valueSubscriptions.forEach(a=>a()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const a in this.events)this.events[a].clear();for(const a in this.features){const s=this.features[a];s&&(s.unmount(),s.isMounted=!1)}this.current=null}bindToMotionValue(a,s){this.valueSubscriptions.has(a)&&this.valueSubscriptions.get(a)();const r=bi.has(a);r&&this.onBindTransform&&this.onBindTransform();const o=s.on("change",h=>{this.latestValues[a]=h,this.props.onUpdate&&Ke.preRender(this.notifyUpdate),r&&this.projection&&(this.projection.isTransformDirty=!0)}),c=s.on("renderRequest",this.scheduleRender);let d;window.MotionCheckAppearSync&&(d=window.MotionCheckAppearSync(this,a,s)),this.valueSubscriptions.set(a,()=>{o(),c(),d&&d(),s.owner&&s.stop()})}sortNodePosition(a){return!this.current||!this.sortInstanceNodePosition||this.type!==a.type?0:this.sortInstanceNodePosition(this.current,a.current)}updateFeatures(){let a="animation";for(a in hs){const s=hs[a];if(!s)continue;const{isEnabled:r,Feature:o}=s;if(!this.features[a]&&o&&r(this.props)&&(this.features[a]=new o(this)),this.features[a]){const c=this.features[a];c.isMounted?c.update():(c.mount(),c.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):lt()}getStaticValue(a){return this.latestValues[a]}setStaticValue(a,s){this.latestValues[a]=s}update(a,s){(a.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=a,this.prevPresenceContext=this.presenceContext,this.presenceContext=s;for(let r=0;r<Dg.length;r++){const o=Dg[r];this.propEventSubscriptions[o]&&(this.propEventSubscriptions[o](),delete this.propEventSubscriptions[o]);const c="on"+o,d=a[c];d&&(this.propEventSubscriptions[o]=this.on(o,d))}this.prevMotionValues=E3(this,this.scrapeMotionValuesFromProps(a,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue(),this.onUpdate&&this.onUpdate(this)}getProps(){return this.props}getVariant(a){return this.props.variants?this.props.variants[a]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(a){const s=this.getClosestVariantNode();if(s)return s.variantChildren&&s.variantChildren.add(a),()=>s.variantChildren.delete(a)}addValue(a,s){const r=this.values.get(a);s!==r&&(r&&this.removeValue(a),this.bindToMotionValue(a,s),this.values.set(a,s),this.latestValues[a]=s.get())}removeValue(a){this.values.delete(a);const s=this.valueSubscriptions.get(a);s&&(s(),this.valueSubscriptions.delete(a)),delete this.latestValues[a],this.removeValueFromRenderState(a,this.renderState)}hasValue(a){return this.values.has(a)}getValue(a,s){if(this.props.values&&this.props.values[a])return this.props.values[a];let r=this.values.get(a);return r===void 0&&s!==void 0&&(r=Rr(s===null?void 0:s,{owner:this}),this.addValue(a,r)),r}readValue(a,s){var r;let o=this.latestValues[a]!==void 0||!this.current?this.latestValues[a]:(r=this.getBaseTargetFromProps(this.props,a))!==null&&r!==void 0?r:this.readValueFromInstance(this.current,a,this.options);return o!=null&&(typeof o=="string"&&($p(o)||zp(o))?o=parseFloat(o):!S3(o)&&_a.test(s)&&(o=Fp(a,s)),this.setBaseTarget(a,Rt(o)?o.get():o)),Rt(o)?o.get():o}setBaseTarget(a,s){this.baseTarget[a]=s}getBaseTarget(a){var s;const{initial:r}=this.props;let o;if(typeof r=="string"||typeof r=="object"){const d=of(this.props,r,(s=this.presenceContext)===null||s===void 0?void 0:s.custom);d&&(o=d[a])}if(r&&o!==void 0)return o;const c=this.getBaseTargetFromProps(this.props,a);return c!==void 0&&!Rt(c)?c:this.initialValues[a]!==void 0&&o===void 0?void 0:this.baseTarget[a]}on(a,s){return this.events[a]||(this.events[a]=new Hc),this.events[a].add(s)}notify(a,...s){this.events[a]&&this.events[a].notify(...s)}}class _g extends T3{constructor(){super(...arguments),this.KeyframeResolver=ny}sortInstanceNodePosition(a,s){return a.compareDocumentPosition(s)&2?1:-1}getBaseTargetFromProps(a,s){return a.style?a.style[s]:void 0}removeValueFromRenderState(a,{vars:s,style:r}){delete s[a],delete r[a]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:a}=this.props;Rt(a)&&(this.childSubscription=a.on("change",s=>{this.current&&(this.current.textContent=`${s}`)}))}}function C3(t){return window.getComputedStyle(t)}class j3 extends _g{constructor(){super(...arguments),this.type="html",this.renderInstance=xp}readValueFromInstance(a,s){if(bi.has(s))return Uw(a,s);{const r=C3(a),o=(Ic(s)?r.getPropertyValue(s):r[s])||0;return typeof o=="string"?o.trim():o}}measureInstanceViewportBox(a,{transformPagePoint:s}){return Ly(a,s)}build(a,s,r){nf(a,s,r.transformTemplate)}scrapeMotionValuesFromProps(a,s,r){return cf(a,s,r)}}class A3 extends _g{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=lt,this.updateDimensions=()=>{this.current&&!this.renderState.dimensions&&bp(this.current,this.renderState)}}getBaseTargetFromProps(a,s){return a[s]}readValueFromInstance(a,s){if(bi.has(s)){const r=qp(s);return r&&r.default||0}return s=Sp.has(s)?s:Jc(s),a.getAttribute(s)}scrapeMotionValuesFromProps(a,s,r){return Ep(a,s,r)}onBindTransform(){this.current&&!this.renderState.dimensions&&Ke.postRender(this.updateDimensions)}build(a,s,r){rf(a,s,this.isSVGTag,r.transformTemplate)}renderInstance(a,s,r,o){wp(a,s,r,o)}mount(a){this.isSVGTag=lf(a.tagName),super.mount(a)}}const D3=(t,a)=>sf(t)?new A3(a):new j3(a,{allowProjection:t!==w.Fragment}),_3=fw({...$5,...g3,...r3,...v3},D3),Do=D6(_3);var Yf,Mg;function M3(){if(Mg)return Yf;Mg=1;var t="Expected a function",a=NaN,s="[object Symbol]",r=/^\s+|\s+$/g,o=/^[-+]0x[0-9a-f]+$/i,c=/^0b[01]+$/i,d=/^0o[0-7]+$/i,h=parseInt,p=typeof vt=="object"&&vt&&vt.Object===Object&&vt,y=typeof self=="object"&&self&&self.Object===Object&&self,v=p||y||Function("return this")(),b=Object.prototype,S=b.toString,E=Math.max,N=Math.min,C=function(){return v.Date.now()};function R(O,X,J){var Z,Y,te,je,le,ye,Ae=0,ze=!1,$=!1,ne=!0;if(typeof O!="function")throw new TypeError(t);X=W(X)||0,z(J)&&(ze=!!J.leading,$="maxWait"in J,te=$?E(W(J.maxWait)||0,X):te,ne="trailing"in J?!!J.trailing:ne);function re(Me){var Se=Z,Fe=Y;return Z=Y=void 0,Ae=Me,je=O.apply(Fe,Se),je}function De(Me){return Ae=Me,le=setTimeout(de,X),ze?re(Me):je}function D(Me){var Se=Me-ye,Fe=Me-Ae,Ge=X-Se;return $?N(Ge,te-Fe):Ge}function K(Me){var Se=Me-ye,Fe=Me-Ae;return ye===void 0||Se>=X||Se<0||$&&Fe>=te}function de(){var Me=C();if(K(Me))return he(Me);le=setTimeout(de,D(Me))}function he(Me){return le=void 0,ne&&Z?re(Me):(Z=Y=void 0,je)}function ie(){le!==void 0&&clearTimeout(le),Ae=0,Z=ye=Y=le=void 0}function _e(){return le===void 0?je:he(C())}function be(){var Me=C(),Se=K(Me);if(Z=arguments,Y=this,ye=Me,Se){if(le===void 0)return De(ye);if($)return le=setTimeout(de,X),re(ye)}return le===void 0&&(le=setTimeout(de,X)),je}return be.cancel=ie,be.flush=_e,be}function z(O){var X=typeof O;return!!O&&(X=="object"||X=="function")}function B(O){return!!O&&typeof O=="object"}function H(O){return typeof O=="symbol"||B(O)&&S.call(O)==s}function W(O){if(typeof O=="number")return O;if(H(O))return a;if(z(O)){var X=typeof O.valueOf=="function"?O.valueOf():O;O=z(X)?X+"":X}if(typeof O!="string")return O===0?O:+O;O=O.replace(r,"");var J=c.test(O);return J||d.test(O)?h(O.slice(2),J?2:8):o.test(O)?a:+O}return Yf=R,Yf}var N3=M3();const R3=Ca(N3);var Kr=t=>t.type==="checkbox",Ci=t=>t instanceof Date,Bt=t=>t==null;const Ng=t=>typeof t=="object";var ot=t=>!Bt(t)&&!Array.isArray(t)&&Ng(t)&&!Ci(t),O3=t=>ot(t)&&t.target?Kr(t.target)?t.target.checked:t.target.value:t,V3=t=>t.substring(0,t.search(/\.\d+(\.|$)/))||t,z3=(t,a)=>t.has(V3(a)),k3=t=>{const a=t.constructor&&t.constructor.prototype;return ot(a)&&a.hasOwnProperty("isPrototypeOf")},Qf=typeof window<"u"&&typeof window.HTMLElement<"u"&&typeof document<"u";function cn(t){let a;const s=Array.isArray(t),r=typeof FileList<"u"?t instanceof FileList:!1;if(t instanceof Date)a=new Date(t);else if(t instanceof Set)a=new Set(t);else if(!(Qf&&(t instanceof Blob||r))&&(s||ot(t)))if(a=s?[]:{},!s&&!k3(t))a=t;else for(const o in t)t.hasOwnProperty(o)&&(a[o]=cn(t[o]));else return t;return a}var _o=t=>Array.isArray(t)?t.filter(Boolean):[],ht=t=>t===void 0,oe=(t,a,s)=>{if(!a||!ot(t))return s;const r=_o(a.split(/[,[\].]+?/)).reduce((o,c)=>Bt(o)?o:o[c],t);return ht(r)||r===t?ht(t[a])?s:t[a]:r},Un=t=>typeof t=="boolean",Xf=t=>/^\w*$/.test(t),Rg=t=>_o(t.replace(/["|']|\]/g,"").split(/\.|\[/)),$e=(t,a,s)=>{let r=-1;const o=Xf(a)?[a]:Rg(a),c=o.length,d=c-1;for(;++r<c;){const h=o[r];let p=s;if(r!==d){const y=t[h];p=ot(y)||Array.isArray(y)?y:isNaN(+o[r+1])?{}:[]}if(h==="__proto__"||h==="constructor"||h==="prototype")return;t[h]=p,t=t[h]}return t};const Og={BLUR:"blur",FOCUS_OUT:"focusout"},An={onBlur:"onBlur",onChange:"onChange",onSubmit:"onSubmit",onTouched:"onTouched",all:"all"},ea={max:"max",min:"min",maxLength:"maxLength",minLength:"minLength",pattern:"pattern",required:"required",validate:"validate"},Vg=Lt.createContext(null),fn=()=>Lt.useContext(Vg),L3=t=>{const{children:a,...s}=t;return Lt.createElement(Vg.Provider,{value:s},a)};var U3=(t,a,s,r=!0)=>{const o={defaultValues:a._defaultValues};for(const c in t)Object.defineProperty(o,c,{get:()=>{const d=c;return a._proxyFormState[d]!==An.all&&(a._proxyFormState[d]=!r||An.all),t[d]}});return o},Yt=t=>ot(t)&&!Object.keys(t).length,B3=(t,a,s,r)=>{s(t);const{name:o,...c}=t;return Yt(c)||Object.keys(c).length>=Object.keys(a).length||Object.keys(c).find(d=>a[d]===An.all)},Mo=t=>Array.isArray(t)?t:[t];function H3(t){const a=Lt.useRef(t);a.current=t,Lt.useEffect(()=>{const s=!t.disabled&&a.current.subject&&a.current.subject.subscribe({next:a.current.next});return()=>{s&&s.unsubscribe()}},[t.disabled])}var Bn=t=>typeof t=="string",P3=(t,a,s,r,o)=>Bn(t)?(r&&a.watch.add(t),oe(s,t,o)):Array.isArray(t)?t.map(c=>(r&&a.watch.add(c),oe(s,c))):(r&&(a.watchAll=!0),s),q3=(t,a,s,r,o)=>a?{...s[t],types:{...s[t]&&s[t].types?s[t].types:{},[r]:o||!0}}:{},zg=t=>({isOnSubmit:!t||t===An.onSubmit,isOnBlur:t===An.onBlur,isOnChange:t===An.onChange,isOnAll:t===An.all,isOnTouch:t===An.onTouched}),kg=(t,a,s)=>!s&&(a.watchAll||a.watch.has(t)||[...a.watch].some(r=>t.startsWith(r)&&/^\.\w+/.test(t.slice(r.length))));const Zr=(t,a,s,r)=>{for(const o of s||Object.keys(t)){const c=oe(t,o);if(c){const{_f:d,...h}=c;if(d){if(d.refs&&d.refs[0]&&a(d.refs[0],o)&&!r)return!0;if(d.ref&&a(d.ref,d.name)&&!r)return!0;if(Zr(h,a))break}else if(ot(h)&&Zr(h,a))break}}};var F3=(t,a,s)=>{const r=Mo(oe(t,s));return $e(r,"root",a[s]),$e(t,s,r),t},Kf=t=>t.type==="file",Hn=t=>typeof t=="function",No=t=>{if(!Qf)return!1;const a=t?t.ownerDocument:0;return t instanceof(a&&a.defaultView?a.defaultView.HTMLElement:HTMLElement)},Ro=t=>Bn(t),Zf=t=>t.type==="radio",Oo=t=>t instanceof RegExp;const Lg={value:!1,isValid:!1},Ug={value:!0,isValid:!0};var Bg=t=>{if(Array.isArray(t)){if(t.length>1){const a=t.filter(s=>s&&s.checked&&!s.disabled).map(s=>s.value);return{value:a,isValid:!!a.length}}return t[0].checked&&!t[0].disabled?t[0].attributes&&!ht(t[0].attributes.value)?ht(t[0].value)||t[0].value===""?Ug:{value:t[0].value,isValid:!0}:Ug:Lg}return Lg};const Hg={isValid:!1,value:null};var Pg=t=>Array.isArray(t)?t.reduce((a,s)=>s&&s.checked&&!s.disabled?{isValid:!0,value:s.value}:a,Hg):Hg;function qg(t,a,s="validate"){if(Ro(t)||Array.isArray(t)&&t.every(Ro)||Un(t)&&!t)return{type:s,message:Ro(t)?t:"",ref:a}}var ws=t=>ot(t)&&!Oo(t)?t:{value:t,message:""},Fg=async(t,a,s,r,o,c)=>{const{ref:d,refs:h,required:p,maxLength:y,minLength:v,min:b,max:S,pattern:E,validate:N,name:C,valueAsNumber:R,mount:z}=t._f,B=oe(s,C);if(!z||a.has(C))return{};const H=h?h[0]:d,W=le=>{o&&H.reportValidity&&(H.setCustomValidity(Un(le)?"":le||""),H.reportValidity())},O={},X=Zf(d),J=Kr(d),Z=X||J,Y=(R||Kf(d))&&ht(d.value)&&ht(B)||No(d)&&d.value===""||B===""||Array.isArray(B)&&!B.length,te=q3.bind(null,C,r,O),je=(le,ye,Ae,ze=ea.maxLength,$=ea.minLength)=>{const ne=le?ye:Ae;O[C]={type:le?ze:$,message:ne,ref:d,...te(le?ze:$,ne)}};if(c?!Array.isArray(B)||!B.length:p&&(!Z&&(Y||Bt(B))||Un(B)&&!B||J&&!Bg(h).isValid||X&&!Pg(h).isValid)){const{value:le,message:ye}=Ro(p)?{value:!!p,message:p}:ws(p);if(le&&(O[C]={type:ea.required,message:ye,ref:H,...te(ea.required,ye)},!r))return W(ye),O}if(!Y&&(!Bt(b)||!Bt(S))){let le,ye;const Ae=ws(S),ze=ws(b);if(!Bt(B)&&!isNaN(B)){const $=d.valueAsNumber||B&&+B;Bt(Ae.value)||(le=$>Ae.value),Bt(ze.value)||(ye=$<ze.value)}else{const $=d.valueAsDate||new Date(B),ne=D=>new Date(new Date().toDateString()+" "+D),re=d.type=="time",De=d.type=="week";Bn(Ae.value)&&B&&(le=re?ne(B)>ne(Ae.value):De?B>Ae.value:$>new Date(Ae.value)),Bn(ze.value)&&B&&(ye=re?ne(B)<ne(ze.value):De?B<ze.value:$<new Date(ze.value))}if((le||ye)&&(je(!!le,Ae.message,ze.message,ea.max,ea.min),!r))return W(O[C].message),O}if((y||v)&&!Y&&(Bn(B)||c&&Array.isArray(B))){const le=ws(y),ye=ws(v),Ae=!Bt(le.value)&&B.length>+le.value,ze=!Bt(ye.value)&&B.length<+ye.value;if((Ae||ze)&&(je(Ae,le.message,ye.message),!r))return W(O[C].message),O}if(E&&!Y&&Bn(B)){const{value:le,message:ye}=ws(E);if(Oo(le)&&!B.match(le)&&(O[C]={type:ea.pattern,message:ye,ref:d,...te(ea.pattern,ye)},!r))return W(ye),O}if(N){if(Hn(N)){const le=await N(B,s),ye=qg(le,H);if(ye&&(O[C]={...ye,...te(ea.validate,ye.message)},!r))return W(ye.message),O}else if(ot(N)){let le={};for(const ye in N){if(!Yt(le)&&!r)break;const Ae=qg(await N[ye](B,s),H,ye);Ae&&(le={...Ae,...te(ye,Ae.message)},W(Ae.message),r&&(O[C]=le))}if(!Yt(le)&&(O[C]={ref:H,...le},!r))return O}}return W(!0),O};function G3(t,a){const s=a.slice(0,-1).length;let r=0;for(;r<s;)t=ht(t)?r++:t[a[r++]];return t}function Y3(t){for(const a in t)if(t.hasOwnProperty(a)&&!ht(t[a]))return!1;return!0}function xt(t,a){const s=Array.isArray(a)?a:Xf(a)?[a]:Rg(a),r=s.length===1?t:G3(t,s),o=s.length-1,c=s[o];return r&&delete r[c],o!==0&&(ot(r)&&Yt(r)||Array.isArray(r)&&Y3(r))&&xt(t,s.slice(0,-1)),t}var Wf=()=>{let t=[];return{get observers(){return t},next:o=>{for(const c of t)c.next&&c.next(o)},subscribe:o=>(t.push(o),{unsubscribe:()=>{t=t.filter(c=>c!==o)}}),unsubscribe:()=>{t=[]}}},$f=t=>Bt(t)||!Ng(t);function Na(t,a){if($f(t)||$f(a))return t===a;if(Ci(t)&&Ci(a))return t.getTime()===a.getTime();const s=Object.keys(t),r=Object.keys(a);if(s.length!==r.length)return!1;for(const o of s){const c=t[o];if(!r.includes(o))return!1;if(o!=="ref"){const d=a[o];if(Ci(c)&&Ci(d)||ot(c)&&ot(d)||Array.isArray(c)&&Array.isArray(d)?!Na(c,d):c!==d)return!1}}return!0}var Gg=t=>t.type==="select-multiple",Q3=t=>Zf(t)||Kr(t),Jf=t=>No(t)&&t.isConnected,Yg=t=>{for(const a in t)if(Hn(t[a]))return!0;return!1};function Vo(t,a={}){const s=Array.isArray(t);if(ot(t)||s)for(const r in t)Array.isArray(t[r])||ot(t[r])&&!Yg(t[r])?(a[r]=Array.isArray(t[r])?[]:{},Vo(t[r],a[r])):Bt(t[r])||(a[r]=!0);return a}function Qg(t,a,s){const r=Array.isArray(t);if(ot(t)||r)for(const o in t)Array.isArray(t[o])||ot(t[o])&&!Yg(t[o])?ht(a)||$f(s[o])?s[o]=Array.isArray(t[o])?Vo(t[o],[]):{...Vo(t[o])}:Qg(t[o],Bt(a)?{}:a[o],s[o]):s[o]=!Na(t[o],a[o]);return s}var Wr=(t,a)=>Qg(t,a,Vo(a)),Xg=(t,{valueAsNumber:a,valueAsDate:s,setValueAs:r})=>ht(t)?t:a?t===""?NaN:t&&+t:s&&Bn(t)?new Date(t):r?r(t):t;function If(t){const a=t.ref;return Kf(a)?a.files:Zf(a)?Pg(t.refs).value:Gg(a)?[...a.selectedOptions].map(({value:s})=>s):Kr(a)?Bg(t.refs).value:Xg(ht(a.value)?t.ref.value:a.value,t)}var X3=(t,a,s,r)=>{const o={};for(const c of t){const d=oe(a,c);d&&$e(o,c,d._f)}return{criteriaMode:s,names:[...t],fields:o,shouldUseNativeValidation:r}},$r=t=>ht(t)?t:Oo(t)?t.source:ot(t)?Oo(t.value)?t.value.source:t.value:t;const Kg="AsyncFunction";var K3=t=>!!t&&!!t.validate&&!!(Hn(t.validate)&&t.validate.constructor.name===Kg||ot(t.validate)&&Object.values(t.validate).find(a=>a.constructor.name===Kg)),Z3=t=>t.mount&&(t.required||t.min||t.max||t.maxLength||t.minLength||t.pattern||t.validate);function Zg(t,a,s){const r=oe(t,s);if(r||Xf(s))return{error:r,name:s};const o=s.split(".");for(;o.length;){const c=o.join("."),d=oe(a,c),h=oe(t,c);if(d&&!Array.isArray(d)&&s!==c)return{name:s};if(h&&h.type)return{name:c,error:h};o.pop()}return{name:s}}var W3=(t,a,s,r,o)=>o.isOnAll?!1:!s&&o.isOnTouch?!(a||t):(s?r.isOnBlur:o.isOnBlur)?!t:(s?r.isOnChange:o.isOnChange)?t:!0,$3=(t,a)=>!_o(oe(t,a)).length&&xt(t,a);const J3={mode:An.onSubmit,reValidateMode:An.onChange,shouldFocusError:!0};function I3(t={}){let a={...J3,...t},s={submitCount:0,isDirty:!1,isLoading:Hn(a.defaultValues),isValidating:!1,isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,touchedFields:{},dirtyFields:{},validatingFields:{},errors:a.errors||{},disabled:a.disabled||!1},r={},o=ot(a.defaultValues)||ot(a.values)?cn(a.defaultValues||a.values)||{}:{},c=a.shouldUnregister?{}:cn(o),d={action:!1,mount:!1,watch:!1},h={mount:new Set,disabled:new Set,unMount:new Set,array:new Set,watch:new Set},p,y=0;const v={isDirty:!1,dirtyFields:!1,validatingFields:!1,touchedFields:!1,isValidating:!1,isValid:!1,errors:!1},b={values:Wf(),array:Wf(),state:Wf()},S=zg(a.mode),E=zg(a.reValidateMode),N=a.criteriaMode===An.all,C=A=>L=>{clearTimeout(y),y=setTimeout(A,L)},R=async A=>{if(!a.disabled&&(v.isValid||A)){const L=a.resolver?Yt((await Z()).errors):await te(r,!0);L!==s.isValid&&b.state.next({isValid:L})}},z=(A,L)=>{!a.disabled&&(v.isValidating||v.validatingFields)&&((A||Array.from(h.mount)).forEach(P=>{P&&(L?$e(s.validatingFields,P,L):xt(s.validatingFields,P))}),b.state.next({validatingFields:s.validatingFields,isValidating:!Yt(s.validatingFields)}))},B=(A,L=[],P,ae,ee=!0,I=!0)=>{if(ae&&P&&!a.disabled){if(d.action=!0,I&&Array.isArray(oe(r,A))){const ce=P(oe(r,A),ae.argA,ae.argB);ee&&$e(r,A,ce)}if(I&&Array.isArray(oe(s.errors,A))){const ce=P(oe(s.errors,A),ae.argA,ae.argB);ee&&$e(s.errors,A,ce),$3(s.errors,A)}if(v.touchedFields&&I&&Array.isArray(oe(s.touchedFields,A))){const ce=P(oe(s.touchedFields,A),ae.argA,ae.argB);ee&&$e(s.touchedFields,A,ce)}v.dirtyFields&&(s.dirtyFields=Wr(o,c)),b.state.next({name:A,isDirty:le(A,L),dirtyFields:s.dirtyFields,errors:s.errors,isValid:s.isValid})}else $e(c,A,L)},H=(A,L)=>{$e(s.errors,A,L),b.state.next({errors:s.errors})},W=A=>{s.errors=A,b.state.next({errors:s.errors,isValid:!1})},O=(A,L,P,ae)=>{const ee=oe(r,A);if(ee){const I=oe(c,A,ht(P)?oe(o,A):P);ht(I)||ae&&ae.defaultChecked||L?$e(c,A,L?I:If(ee._f)):ze(A,I),d.mount&&R()}},X=(A,L,P,ae,ee)=>{let I=!1,ce=!1;const Ee={name:A};if(!a.disabled){const ct=!!(oe(r,A)&&oe(r,A)._f&&oe(r,A)._f.disabled);if(!P||ae){v.isDirty&&(ce=s.isDirty,s.isDirty=Ee.isDirty=le(),I=ce!==Ee.isDirty);const ft=ct||Na(oe(o,A),L);ce=!!(!ct&&oe(s.dirtyFields,A)),ft||ct?xt(s.dirtyFields,A):$e(s.dirtyFields,A,!0),Ee.dirtyFields=s.dirtyFields,I=I||v.dirtyFields&&ce!==!ft}if(P){const ft=oe(s.touchedFields,A);ft||($e(s.touchedFields,A,P),Ee.touchedFields=s.touchedFields,I=I||v.touchedFields&&ft!==P)}I&&ee&&b.state.next(Ee)}return I?Ee:{}},J=(A,L,P,ae)=>{const ee=oe(s.errors,A),I=v.isValid&&Un(L)&&s.isValid!==L;if(a.delayError&&P?(p=C(()=>H(A,P)),p(a.delayError)):(clearTimeout(y),p=null,P?$e(s.errors,A,P):xt(s.errors,A)),(P?!Na(ee,P):ee)||!Yt(ae)||I){const ce={...ae,...I&&Un(L)?{isValid:L}:{},errors:s.errors,name:A};s={...s,...ce},b.state.next(ce)}},Z=async A=>{z(A,!0);const L=await a.resolver(c,a.context,X3(A||h.mount,r,a.criteriaMode,a.shouldUseNativeValidation));return z(A),L},Y=async A=>{const{errors:L}=await Z(A);if(A)for(const P of A){const ae=oe(L,P);ae?$e(s.errors,P,ae):xt(s.errors,P)}else s.errors=L;return L},te=async(A,L,P={valid:!0})=>{for(const ae in A){const ee=A[ae];if(ee){const{_f:I,...ce}=ee;if(I){const Ee=h.array.has(I.name),ct=ee._f&&K3(ee._f);ct&&v.validatingFields&&z([ae],!0);const ft=await Fg(ee,h.disabled,c,N,a.shouldUseNativeValidation&&!L,Ee);if(ct&&v.validatingFields&&z([ae]),ft[I.name]&&(P.valid=!1,L))break;!L&&(oe(ft,I.name)?Ee?F3(s.errors,ft,I.name):$e(s.errors,I.name,ft[I.name]):xt(s.errors,I.name))}!Yt(ce)&&await te(ce,L,P)}}return P.valid},je=()=>{for(const A of h.unMount){const L=oe(r,A);L&&(L._f.refs?L._f.refs.every(P=>!Jf(P)):!Jf(L._f.ref))&&be(A)}h.unMount=new Set},le=(A,L)=>!a.disabled&&(A&&L&&$e(c,A,L),!Na(K(),o)),ye=(A,L,P)=>P3(A,h,{...d.mount?c:ht(L)?o:Bn(A)?{[A]:L}:L},P,L),Ae=A=>_o(oe(d.mount?c:o,A,a.shouldUnregister?oe(o,A,[]):[])),ze=(A,L,P={})=>{const ae=oe(r,A);let ee=L;if(ae){const I=ae._f;I&&(!I.disabled&&$e(c,A,Xg(L,I)),ee=No(I.ref)&&Bt(L)?"":L,Gg(I.ref)?[...I.ref.options].forEach(ce=>ce.selected=ee.includes(ce.value)):I.refs?Kr(I.ref)?I.refs.length>1?I.refs.forEach(ce=>(!ce.defaultChecked||!ce.disabled)&&(ce.checked=Array.isArray(ee)?!!ee.find(Ee=>Ee===ce.value):ee===ce.value)):I.refs[0]&&(I.refs[0].checked=!!ee):I.refs.forEach(ce=>ce.checked=ce.value===ee):Kf(I.ref)?I.ref.value="":(I.ref.value=ee,I.ref.type||b.values.next({name:A,values:{...c}})))}(P.shouldDirty||P.shouldTouch)&&X(A,ee,P.shouldTouch,P.shouldDirty,!0),P.shouldValidate&&D(A)},$=(A,L,P)=>{for(const ae in L){const ee=L[ae],I=`${A}.${ae}`,ce=oe(r,I);(h.array.has(A)||ot(ee)||ce&&!ce._f)&&!Ci(ee)?$(I,ee,P):ze(I,ee,P)}},ne=(A,L,P={})=>{const ae=oe(r,A),ee=h.array.has(A),I=cn(L);$e(c,A,I),ee?(b.array.next({name:A,values:{...c}}),(v.isDirty||v.dirtyFields)&&P.shouldDirty&&b.state.next({name:A,dirtyFields:Wr(o,c),isDirty:le(A,I)})):ae&&!ae._f&&!Bt(I)?$(A,I,P):ze(A,I,P),kg(A,h)&&b.state.next({...s}),b.values.next({name:d.mount?A:void 0,values:{...c}})},re=async A=>{d.mount=!0;const L=A.target;let P=L.name,ae=!0;const ee=oe(r,P),I=()=>L.type?If(ee._f):O3(A),ce=Ee=>{ae=Number.isNaN(Ee)||Ci(Ee)&&isNaN(Ee.getTime())||Na(Ee,oe(c,P,Ee))};if(ee){let Ee,ct;const ft=I(),It=A.type===Og.BLUR||A.type===Og.FOCUS_OUT,xd=!Z3(ee._f)&&!a.resolver&&!oe(s.errors,P)&&!ee._f.deps||W3(It,oe(s.touchedFields,P),s.isSubmitted,E,S),Ps=kg(P,h,It);$e(c,P,ft),It?(ee._f.onBlur&&ee._f.onBlur(A),p&&p(0)):ee._f.onChange&&ee._f.onChange(A);const rl=X(P,ft,It,!1),Sd=!Yt(rl)||Ps;if(!It&&b.values.next({name:P,type:A.type,values:{...c}}),xd)return v.isValid&&(a.mode==="onBlur"&&It?R():It||R()),Sd&&b.state.next({name:P,...Ps?{}:rl});if(!It&&Ps&&b.state.next({...s}),a.resolver){const{errors:Qa}=await Z([P]);if(ce(ft),ae){const qt=Zg(s.errors,r,P),Io=Zg(Qa,r,qt.name||P);Ee=Io.error,P=Io.name,ct=Yt(Qa)}}else z([P],!0),Ee=(await Fg(ee,h.disabled,c,N,a.shouldUseNativeValidation))[P],z([P]),ce(ft),ae&&(Ee?ct=!1:v.isValid&&(ct=await te(r,!0)));ae&&(ee._f.deps&&D(ee._f.deps),J(P,ct,Ee,rl))}},De=(A,L)=>{if(oe(s.errors,L)&&A.focus)return A.focus(),1},D=async(A,L={})=>{let P,ae;const ee=Mo(A);if(a.resolver){const I=await Y(ht(A)?A:ee);P=Yt(I),ae=A?!ee.some(ce=>oe(I,ce)):P}else A?(ae=(await Promise.all(ee.map(async I=>{const ce=oe(r,I);return await te(ce&&ce._f?{[I]:ce}:ce)}))).every(Boolean),!(!ae&&!s.isValid)&&R()):ae=P=await te(r);return b.state.next({...!Bn(A)||v.isValid&&P!==s.isValid?{}:{name:A},...a.resolver||!A?{isValid:P}:{},errors:s.errors}),L.shouldFocus&&!ae&&Zr(r,De,A?ee:h.mount),ae},K=A=>{const L={...d.mount?c:o};return ht(A)?L:Bn(A)?oe(L,A):A.map(P=>oe(L,P))},de=(A,L)=>({invalid:!!oe((L||s).errors,A),isDirty:!!oe((L||s).dirtyFields,A),error:oe((L||s).errors,A),isValidating:!!oe(s.validatingFields,A),isTouched:!!oe((L||s).touchedFields,A)}),he=A=>{A&&Mo(A).forEach(L=>xt(s.errors,L)),b.state.next({errors:A?s.errors:{}})},ie=(A,L,P)=>{const ae=(oe(r,A,{_f:{}})._f||{}).ref,ee=oe(s.errors,A)||{},{ref:I,message:ce,type:Ee,...ct}=ee;$e(s.errors,A,{...ct,...L,ref:ae}),b.state.next({name:A,errors:s.errors,isValid:!1}),P&&P.shouldFocus&&ae&&ae.focus&&ae.focus()},_e=(A,L)=>Hn(A)?b.values.subscribe({next:P=>A(ye(void 0,L),P)}):ye(A,L,!0),be=(A,L={})=>{for(const P of A?Mo(A):h.mount)h.mount.delete(P),h.array.delete(P),L.keepValue||(xt(r,P),xt(c,P)),!L.keepError&&xt(s.errors,P),!L.keepDirty&&xt(s.dirtyFields,P),!L.keepTouched&&xt(s.touchedFields,P),!L.keepIsValidating&&xt(s.validatingFields,P),!a.shouldUnregister&&!L.keepDefaultValue&&xt(o,P);b.values.next({values:{...c}}),b.state.next({...s,...L.keepDirty?{isDirty:le()}:{}}),!L.keepIsValid&&R()},Me=({disabled:A,name:L,field:P,fields:ae})=>{(Un(A)&&d.mount||A||h.disabled.has(L))&&(A?h.disabled.add(L):h.disabled.delete(L),X(L,If(P?P._f:oe(ae,L)._f),!1,!1,!0))},Se=(A,L={})=>{let P=oe(r,A);const ae=Un(L.disabled)||Un(a.disabled);return $e(r,A,{...P||{},_f:{...P&&P._f?P._f:{ref:{name:A}},name:A,mount:!0,...L}}),h.mount.add(A),P?Me({field:P,disabled:Un(L.disabled)?L.disabled:a.disabled,name:A}):O(A,!0,L.value),{...ae?{disabled:L.disabled||a.disabled}:{},...a.progressive?{required:!!L.required,min:$r(L.min),max:$r(L.max),minLength:$r(L.minLength),maxLength:$r(L.maxLength),pattern:$r(L.pattern)}:{},name:A,onChange:re,onBlur:re,ref:ee=>{if(ee){Se(A,L),P=oe(r,A);const I=ht(ee.value)&&ee.querySelectorAll&&ee.querySelectorAll("input,select,textarea")[0]||ee,ce=Q3(I),Ee=P._f.refs||[];if(ce?Ee.find(ct=>ct===I):I===P._f.ref)return;$e(r,A,{_f:{...P._f,...ce?{refs:[...Ee.filter(Jf),I,...Array.isArray(oe(o,A))?[{}]:[]],ref:{type:I.type,name:A}}:{ref:I}}}),O(A,!1,void 0,I)}else P=oe(r,A,{}),P._f&&(P._f.mount=!1),(a.shouldUnregister||L.shouldUnregister)&&!(z3(h.array,A)&&d.action)&&h.unMount.add(A)}}},Fe=()=>a.shouldFocusError&&Zr(r,De,h.mount),Ge=A=>{Un(A)&&(b.state.next({disabled:A}),Zr(r,(L,P)=>{const ae=oe(r,P);ae&&(L.disabled=ae._f.disabled||A,Array.isArray(ae._f.refs)&&ae._f.refs.forEach(ee=>{ee.disabled=ae._f.disabled||A}))},0,!1))},Jt=(A,L)=>async P=>{let ae;P&&(P.preventDefault&&P.preventDefault(),P.persist&&P.persist());let ee=cn(c);if(h.disabled.size)for(const I of h.disabled)$e(ee,I,void 0);if(b.state.next({isSubmitting:!0}),a.resolver){const{errors:I,values:ce}=await Z();s.errors=I,ee=ce}else await te(r);if(xt(s.errors,"root"),Yt(s.errors)){b.state.next({errors:{}});try{await A(ee,P)}catch(I){ae=I}}else L&&await L({...s.errors},P),Fe(),setTimeout(Fe);if(b.state.next({isSubmitted:!0,isSubmitting:!1,isSubmitSuccessful:Yt(s.errors)&&!ae,submitCount:s.submitCount+1,errors:s.errors}),ae)throw ae},Bi=(A,L={})=>{oe(r,A)&&(ht(L.defaultValue)?ne(A,cn(oe(o,A))):(ne(A,L.defaultValue),$e(o,A,cn(L.defaultValue))),L.keepTouched||xt(s.touchedFields,A),L.keepDirty||(xt(s.dirtyFields,A),s.isDirty=L.defaultValue?le(A,cn(oe(o,A))):le()),L.keepError||(xt(s.errors,A),v.isValid&&R()),b.state.next({...s}))},Nn=(A,L={})=>{const P=A?cn(A):o,ae=cn(P),ee=Yt(A),I=ee?o:ae;if(L.keepDefaultValues||(o=P),!L.keepValues){if(L.keepDirtyValues){const ce=new Set([...h.mount,...Object.keys(Wr(o,c))]);for(const Ee of Array.from(ce))oe(s.dirtyFields,Ee)?$e(I,Ee,oe(c,Ee)):ne(Ee,oe(I,Ee))}else{if(Qf&&ht(A))for(const ce of h.mount){const Ee=oe(r,ce);if(Ee&&Ee._f){const ct=Array.isArray(Ee._f.refs)?Ee._f.refs[0]:Ee._f.ref;if(No(ct)){const ft=ct.closest("form");if(ft){ft.reset();break}}}}r={}}c=a.shouldUnregister?L.keepDefaultValues?cn(o):{}:cn(I),b.array.next({values:{...I}}),b.values.next({values:{...I}})}h={mount:L.keepDirtyValues?h.mount:new Set,unMount:new Set,array:new Set,disabled:new Set,watch:new Set,watchAll:!1,focus:""},d.mount=!v.isValid||!!L.keepIsValid||!!L.keepDirtyValues,d.watch=!!a.shouldUnregister,b.state.next({submitCount:L.keepSubmitCount?s.submitCount:0,isDirty:ee?!1:L.keepDirty?s.isDirty:!!(L.keepDefaultValues&&!Na(A,o)),isSubmitted:L.keepIsSubmitted?s.isSubmitted:!1,dirtyFields:ee?{}:L.keepDirtyValues?L.keepDefaultValues&&c?Wr(o,c):s.dirtyFields:L.keepDefaultValues&&A?Wr(o,A):L.keepDirty?s.dirtyFields:{},touchedFields:L.keepTouched?s.touchedFields:{},errors:L.keepErrors?s.errors:{},isSubmitSuccessful:L.keepIsSubmitSuccessful?s.isSubmitSuccessful:!1,isSubmitting:!1})},Hi=(A,L)=>Nn(Hn(A)?A(c):A,L);return{control:{register:Se,unregister:be,getFieldState:de,handleSubmit:Jt,setError:ie,_executeSchema:Z,_getWatch:ye,_getDirty:le,_updateValid:R,_removeUnmounted:je,_updateFieldArray:B,_updateDisabledField:Me,_getFieldArray:Ae,_reset:Nn,_resetDefaultValues:()=>Hn(a.defaultValues)&&a.defaultValues().then(A=>{Hi(A,a.resetOptions),b.state.next({isLoading:!1})}),_updateFormState:A=>{s={...s,...A}},_disableForm:Ge,_subjects:b,_proxyFormState:v,_setErrors:W,get _fields(){return r},get _formValues(){return c},get _state(){return d},set _state(A){d=A},get _defaultValues(){return o},get _names(){return h},set _names(A){h=A},get _formState(){return s},set _formState(A){s=A},get _options(){return a},set _options(A){a={...a,...A}}},trigger:D,register:Se,handleSubmit:Jt,watch:_e,setValue:ne,getValues:K,reset:Hi,resetField:Bi,clearErrors:he,unregister:be,setError:ie,setFocus:(A,L={})=>{const P=oe(r,A),ae=P&&P._f;if(ae){const ee=ae.refs?ae.refs[0]:ae.ref;ee.focus&&(ee.focus(),L.shouldSelect&&Hn(ee.select)&&ee.select())}},getFieldState:de}}function ed(t={}){const a=Lt.useRef(void 0),s=Lt.useRef(void 0),[r,o]=Lt.useState({isDirty:!1,isValidating:!1,isLoading:Hn(t.defaultValues),isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,submitCount:0,dirtyFields:{},touchedFields:{},validatingFields:{},errors:t.errors||{},disabled:t.disabled||!1,defaultValues:Hn(t.defaultValues)?void 0:t.defaultValues});a.current||(a.current={...I3(t),formState:r});const c=a.current.control;return c._options=t,H3({subject:c._subjects.state,next:d=>{B3(d,c._proxyFormState,c._updateFormState)&&o({...c._formState})}}),Lt.useEffect(()=>c._disableForm(t.disabled),[c,t.disabled]),Lt.useEffect(()=>{if(c._proxyFormState.isDirty){const d=c._getDirty();d!==r.isDirty&&c._subjects.state.next({isDirty:d})}},[c,r.isDirty]),Lt.useEffect(()=>{t.values&&!Na(t.values,s.current)?(c._reset(t.values,c._options.resetOptions),s.current=t.values,o(d=>({...d}))):c._resetDefaultValues()},[t.values,c]),Lt.useEffect(()=>{t.errors&&c._setErrors(t.errors)},[t.errors,c]),Lt.useEffect(()=>{c._state.mount||(c._updateValid(),c._state.mount=!0),c._state.watch&&(c._state.watch=!1,c._subjects.state.next({...c._formState})),c._removeUnmounted()}),Lt.useEffect(()=>{t.shouldUnregister&&c._subjects.values.next({values:c._getWatch()})},[t.shouldUnregister,c]),a.current.formState=U3(r,c),a.current}let eE={data:""},tE=t=>typeof window=="object"?((t?t.querySelector("#_goober"):window._goober)||Object.assign((t||document.head).appendChild(document.createElement("style")),{innerHTML:" ",id:"_goober"})).firstChild:t||eE,nE=/(?:([\u0080-\uFFFF\w-%@]+) *:? *([^{;]+?);|([^;}{]*?) *{)|(}\s*)/g,aE=/\/\*[^]*?\*\/|  +/g,Wg=/\n+/g,Ra=(t,a)=>{let s="",r="",o="";for(let c in t){let d=t[c];c[0]=="@"?c[1]=="i"?s=c+" "+d+";":r+=c[1]=="f"?Ra(d,c):c+"{"+Ra(d,c[1]=="k"?"":a)+"}":typeof d=="object"?r+=Ra(d,a?a.replace(/([^,])+/g,h=>c.replace(/([^,]*:\S+\([^)]*\))|([^,])+/g,p=>/&/.test(p)?p.replace(/&/g,h):h?h+" "+p:p)):c):d!=null&&(c=/^--/.test(c)?c:c.replace(/[A-Z]/g,"-$&").toLowerCase(),o+=Ra.p?Ra.p(c,d):c+":"+d+";")}return s+(a&&o?a+"{"+o+"}":o)+r},ta={},$g=t=>{if(typeof t=="object"){let a="";for(let s in t)a+=s+$g(t[s]);return a}return t},iE=(t,a,s,r,o)=>{let c=$g(t),d=ta[c]||(ta[c]=(p=>{let y=0,v=11;for(;y<p.length;)v=101*v+p.charCodeAt(y++)>>>0;return"go"+v})(c));if(!ta[d]){let p=c!==t?t:(y=>{let v,b,S=[{}];for(;v=nE.exec(y.replace(aE,""));)v[4]?S.shift():v[3]?(b=v[3].replace(Wg," ").trim(),S.unshift(S[0][b]=S[0][b]||{})):S[0][v[1]]=v[2].replace(Wg," ").trim();return S[0]})(t);ta[d]=Ra(o?{["@keyframes "+d]:p}:p,s?"":"."+d)}let h=s&&ta.g?ta.g:null;return s&&(ta.g=ta[d]),((p,y,v,b)=>{b?y.data=y.data.replace(b,p):y.data.indexOf(p)===-1&&(y.data=v?p+y.data:y.data+p)})(ta[d],a,r,h),d},sE=(t,a,s)=>t.reduce((r,o,c)=>{let d=a[c];if(d&&d.call){let h=d(s),p=h&&h.props&&h.props.className||/^go/.test(h)&&h;d=p?"."+p:h&&typeof h=="object"?h.props?"":Ra(h,""):h===!1?"":h}return r+o+(d??"")},"");function zo(t){let a=this||{},s=t.call?t(a.p):t;return iE(s.unshift?s.raw?sE(s,[].slice.call(arguments,1),a.p):s.reduce((r,o)=>Object.assign(r,o&&o.call?o(a.p):o),{}):s,tE(a.target),a.g,a.o,a.k)}let Jg,td,nd;zo.bind({g:1});let na=zo.bind({k:1});function rE(t,a,s,r){Ra.p=a,Jg=t,td=s,nd=r}function Oa(t,a){let s=this||{};return function(){let r=arguments;function o(c,d){let h=Object.assign({},c),p=h.className||o.className;s.p=Object.assign({theme:td&&td()},h),s.o=/ *go\d+/.test(p),h.className=zo.apply(s,r)+(p?" "+p:"");let y=t;return t[0]&&(y=h.as||t,delete h.as),nd&&y[0]&&nd(h),Jg(y,h)}return o}}var lE=t=>typeof t=="function",ko=(t,a)=>lE(t)?t(a):t,oE=(()=>{let t=0;return()=>(++t).toString()})(),Ig=(()=>{let t;return()=>{if(t===void 0&&typeof window<"u"){let a=matchMedia("(prefers-reduced-motion: reduce)");t=!a||a.matches}return t}})(),uE=20,ev=(t,a)=>{switch(a.type){case 0:return{...t,toasts:[a.toast,...t.toasts].slice(0,uE)};case 1:return{...t,toasts:t.toasts.map(c=>c.id===a.toast.id?{...c,...a.toast}:c)};case 2:let{toast:s}=a;return ev(t,{type:t.toasts.find(c=>c.id===s.id)?1:0,toast:s});case 3:let{toastId:r}=a;return{...t,toasts:t.toasts.map(c=>c.id===r||r===void 0?{...c,dismissed:!0,visible:!1}:c)};case 4:return a.toastId===void 0?{...t,toasts:[]}:{...t,toasts:t.toasts.filter(c=>c.id!==a.toastId)};case 5:return{...t,pausedAt:a.time};case 6:let o=a.time-(t.pausedAt||0);return{...t,pausedAt:void 0,toasts:t.toasts.map(c=>({...c,pauseDuration:c.pauseDuration+o}))}}},Lo=[],ji={toasts:[],pausedAt:void 0},Ai=t=>{ji=ev(ji,t),Lo.forEach(a=>{a(ji)})},cE={blank:4e3,error:4e3,success:2e3,loading:1/0,custom:4e3},fE=(t={})=>{let[a,s]=w.useState(ji),r=w.useRef(ji);w.useEffect(()=>(r.current!==ji&&s(ji),Lo.push(s),()=>{let c=Lo.indexOf(s);c>-1&&Lo.splice(c,1)}),[]);let o=a.toasts.map(c=>{var d,h,p;return{...t,...t[c.type],...c,removeDelay:c.removeDelay||((d=t[c.type])==null?void 0:d.removeDelay)||(t==null?void 0:t.removeDelay),duration:c.duration||((h=t[c.type])==null?void 0:h.duration)||(t==null?void 0:t.duration)||cE[c.type],style:{...t.style,...(p=t[c.type])==null?void 0:p.style,...c.style}}});return{...a,toasts:o}},dE=(t,a="blank",s)=>({createdAt:Date.now(),visible:!0,dismissed:!1,type:a,ariaProps:{role:"status","aria-live":"polite"},message:t,pauseDuration:0,...s,id:(s==null?void 0:s.id)||oE()}),Jr=t=>(a,s)=>{let r=dE(a,t,s);return Ai({type:2,toast:r}),r.id},He=(t,a)=>Jr("blank")(t,a);He.error=Jr("error"),He.success=Jr("success"),He.loading=Jr("loading"),He.custom=Jr("custom"),He.dismiss=t=>{Ai({type:3,toastId:t})},He.remove=t=>Ai({type:4,toastId:t}),He.promise=(t,a,s)=>{let r=He.loading(a.loading,{...s,...s==null?void 0:s.loading});return typeof t=="function"&&(t=t()),t.then(o=>{let c=a.success?ko(a.success,o):void 0;return c?He.success(c,{id:r,...s,...s==null?void 0:s.success}):He.dismiss(r),o}).catch(o=>{let c=a.error?ko(a.error,o):void 0;c?He.error(c,{id:r,...s,...s==null?void 0:s.error}):He.dismiss(r)}),t};var hE=(t,a)=>{Ai({type:1,toast:{id:t,height:a}})},mE=()=>{Ai({type:5,time:Date.now()})},Ir=new Map,pE=1e3,yE=(t,a=pE)=>{if(Ir.has(t))return;let s=setTimeout(()=>{Ir.delete(t),Ai({type:4,toastId:t})},a);Ir.set(t,s)},gE=t=>{let{toasts:a,pausedAt:s}=fE(t);w.useEffect(()=>{if(s)return;let c=Date.now(),d=a.map(h=>{if(h.duration===1/0)return;let p=(h.duration||0)+h.pauseDuration-(c-h.createdAt);if(p<0){h.visible&&He.dismiss(h.id);return}return setTimeout(()=>He.dismiss(h.id),p)});return()=>{d.forEach(h=>h&&clearTimeout(h))}},[a,s]);let r=w.useCallback(()=>{s&&Ai({type:6,time:Date.now()})},[s]),o=w.useCallback((c,d)=>{let{reverseOrder:h=!1,gutter:p=8,defaultPosition:y}=d||{},v=a.filter(E=>(E.position||y)===(c.position||y)&&E.height),b=v.findIndex(E=>E.id===c.id),S=v.filter((E,N)=>N<b&&E.visible).length;return v.filter(E=>E.visible).slice(...h?[S+1]:[0,S]).reduce((E,N)=>E+(N.height||0)+p,0)},[a]);return w.useEffect(()=>{a.forEach(c=>{if(c.dismissed)yE(c.id,c.removeDelay);else{let d=Ir.get(c.id);d&&(clearTimeout(d),Ir.delete(c.id))}})},[a]),{toasts:a,handlers:{updateHeight:hE,startPause:mE,endPause:r,calculateOffset:o}}},vE=na`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
 transform: scale(1) rotate(45deg);
  opacity: 1;
}`,bE=na`
from {
  transform: scale(0);
  opacity: 0;
}
to {
  transform: scale(1);
  opacity: 1;
}`,xE=na`
from {
  transform: scale(0) rotate(90deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(90deg);
	opacity: 1;
}`,SE=Oa("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${t=>t.primary||"#ff4b4b"};
  position: relative;
  transform: rotate(45deg);

  animation: ${vE} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;

  &:after,
  &:before {
    content: '';
    animation: ${bE} 0.15s ease-out forwards;
    animation-delay: 150ms;
    position: absolute;
    border-radius: 3px;
    opacity: 0;
    background: ${t=>t.secondary||"#fff"};
    bottom: 9px;
    left: 4px;
    height: 2px;
    width: 12px;
  }

  &:before {
    animation: ${xE} 0.15s ease-out forwards;
    animation-delay: 180ms;
    transform: rotate(90deg);
  }
`,wE=na`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
`,EE=Oa("div")`
  width: 12px;
  height: 12px;
  box-sizing: border-box;
  border: 2px solid;
  border-radius: 100%;
  border-color: ${t=>t.secondary||"#e0e0e0"};
  border-right-color: ${t=>t.primary||"#616161"};
  animation: ${wE} 1s linear infinite;
`,TE=na`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(45deg);
	opacity: 1;
}`,CE=na`
0% {
	height: 0;
	width: 0;
	opacity: 0;
}
40% {
  height: 0;
	width: 6px;
	opacity: 1;
}
100% {
  opacity: 1;
  height: 10px;
}`,jE=Oa("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${t=>t.primary||"#61d345"};
  position: relative;
  transform: rotate(45deg);

  animation: ${TE} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;
  &:after {
    content: '';
    box-sizing: border-box;
    animation: ${CE} 0.2s ease-out forwards;
    opacity: 0;
    animation-delay: 200ms;
    position: absolute;
    border-right: 2px solid;
    border-bottom: 2px solid;
    border-color: ${t=>t.secondary||"#fff"};
    bottom: 6px;
    left: 6px;
    height: 10px;
    width: 6px;
  }
`,AE=Oa("div")`
  position: absolute;
`,DE=Oa("div")`
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 20px;
  min-height: 20px;
`,_E=na`
from {
  transform: scale(0.6);
  opacity: 0.4;
}
to {
  transform: scale(1);
  opacity: 1;
}`,ME=Oa("div")`
  position: relative;
  transform: scale(0.6);
  opacity: 0.4;
  min-width: 20px;
  animation: ${_E} 0.3s 0.12s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
`,NE=({toast:t})=>{let{icon:a,type:s,iconTheme:r}=t;return a!==void 0?typeof a=="string"?w.createElement(ME,null,a):a:s==="blank"?null:w.createElement(DE,null,w.createElement(EE,{...r}),s!=="loading"&&w.createElement(AE,null,s==="error"?w.createElement(SE,{...r}):w.createElement(jE,{...r})))},RE=t=>`
0% {transform: translate3d(0,${t*-200}%,0) scale(.6); opacity:.5;}
100% {transform: translate3d(0,0,0) scale(1); opacity:1;}
`,OE=t=>`
0% {transform: translate3d(0,0,-1px) scale(1); opacity:1;}
100% {transform: translate3d(0,${t*-150}%,-1px) scale(.6); opacity:0;}
`,VE="0%{opacity:0;} 100%{opacity:1;}",zE="0%{opacity:1;} 100%{opacity:0;}",kE=Oa("div")`
  display: flex;
  align-items: center;
  background: #fff;
  color: #363636;
  line-height: 1.3;
  will-change: transform;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);
  max-width: 350px;
  pointer-events: auto;
  padding: 8px 10px;
  border-radius: 8px;
`,LE=Oa("div")`
  display: flex;
  justify-content: center;
  margin: 4px 10px;
  color: inherit;
  flex: 1 1 auto;
  white-space: pre-line;
`,UE=(t,a)=>{let s=t.includes("top")?1:-1,[r,o]=Ig()?[VE,zE]:[RE(s),OE(s)];return{animation:a?`${na(r)} 0.35s cubic-bezier(.21,1.02,.73,1) forwards`:`${na(o)} 0.4s forwards cubic-bezier(.06,.71,.55,1)`}},BE=w.memo(({toast:t,position:a,style:s,children:r})=>{let o=t.height?UE(t.position||a||"top-center",t.visible):{opacity:0},c=w.createElement(NE,{toast:t}),d=w.createElement(LE,{...t.ariaProps},ko(t.message,t));return w.createElement(kE,{className:t.className,style:{...o,...s,...t.style}},typeof r=="function"?r({icon:c,message:d}):w.createElement(w.Fragment,null,c,d))});rE(w.createElement);var HE=({id:t,className:a,style:s,onHeightUpdate:r,children:o})=>{let c=w.useCallback(d=>{if(d){let h=()=>{let p=d.getBoundingClientRect().height;r(t,p)};h(),new MutationObserver(h).observe(d,{subtree:!0,childList:!0,characterData:!0})}},[t,r]);return w.createElement("div",{ref:c,className:a,style:s},o)},PE=(t,a)=>{let s=t.includes("top"),r=s?{top:0}:{bottom:0},o=t.includes("center")?{justifyContent:"center"}:t.includes("right")?{justifyContent:"flex-end"}:{};return{left:0,right:0,display:"flex",position:"absolute",transition:Ig()?void 0:"all 230ms cubic-bezier(.21,1.02,.73,1)",transform:`translateY(${a*(s?1:-1)}px)`,...r,...o}},qE=zo`
  z-index: 9999;
  > * {
    pointer-events: auto;
  }
`,Uo=16,FE=({reverseOrder:t,position:a="top-center",toastOptions:s,gutter:r,children:o,containerStyle:c,containerClassName:d})=>{let{toasts:h,handlers:p}=gE(s);return w.createElement("div",{id:"_rht_toaster",style:{position:"fixed",zIndex:9999,top:Uo,left:Uo,right:Uo,bottom:Uo,pointerEvents:"none",...c},className:d,onMouseEnter:p.startPause,onMouseLeave:p.endPause},h.map(y=>{let v=y.position||a,b=p.calculateOffset(y,{reverseOrder:t,gutter:r,defaultPosition:a}),S=PE(v,b);return w.createElement(HE,{id:y.id,key:y.id,onHeightUpdate:p.updateHeight,className:y.visible?qE:"",style:S},y.type==="custom"?ko(y.message,y):o?o(y):w.createElement(BE,{toast:y,position:v}))}))};/**
 * @license @tabler/icons-react v3.17.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var GE={outline:{xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"},filled:{xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"currentColor",stroke:"none"}};/**
 * @license @tabler/icons-react v3.17.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */const qe=(t,a,s,r)=>{const o=w.forwardRef(({color:c="currentColor",size:d=24,stroke:h=2,title:p,className:y,children:v,...b},S)=>w.createElement("svg",{ref:S,...GE[t],width:d,height:d,className:["tabler-icon",`tabler-icon-${a}`,y].join(" "),...t==="filled"?{fill:c}:{strokeWidth:h,stroke:c},...b},[p&&w.createElement("title",{key:"svg-title"},p),...r.map(([E,N])=>w.createElement(E,N)),...Array.isArray(v)?v:[v]]));return o.displayName=`${s}`,o};/**
 * @license @tabler/icons-react v3.17.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var YE=qe("outline","brand-youtube","IconBrandYoutube",[["path",{d:"M2 8a4 4 0 0 1 4 -4h12a4 4 0 0 1 4 4v8a4 4 0 0 1 -4 4h-12a4 4 0 0 1 -4 -4v-8z",key:"svg-0"}],["path",{d:"M10 9l5 3l-5 3z",key:"svg-1"}]]);/**
 * @license @tabler/icons-react v3.17.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var QE=qe("outline","camera","IconCamera",[["path",{d:"M5 7h1a2 2 0 0 0 2 -2a1 1 0 0 1 1 -1h6a1 1 0 0 1 1 1a2 2 0 0 0 2 2h1a2 2 0 0 1 2 2v9a2 2 0 0 1 -2 2h-14a2 2 0 0 1 -2 -2v-9a2 2 0 0 1 2 -2",key:"svg-0"}],["path",{d:"M9 13a3 3 0 1 0 6 0a3 3 0 0 0 -6 0",key:"svg-1"}]]);/**
 * @license @tabler/icons-react v3.17.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var XE=qe("outline","check","IconCheck",[["path",{d:"M5 12l5 5l10 -10",key:"svg-0"}]]);/**
 * @license @tabler/icons-react v3.17.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var KE=qe("outline","code","IconCode",[["path",{d:"M7 8l-4 4l4 4",key:"svg-0"}],["path",{d:"M17 8l4 4l-4 4",key:"svg-1"}],["path",{d:"M14 4l-4 16",key:"svg-2"}]]);/**
 * @license @tabler/icons-react v3.17.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var ZE=qe("outline","cut","IconCut",[["path",{d:"M7 17m-3 0a3 3 0 1 0 6 0a3 3 0 1 0 -6 0",key:"svg-0"}],["path",{d:"M17 17m-3 0a3 3 0 1 0 6 0a3 3 0 1 0 -6 0",key:"svg-1"}],["path",{d:"M9.15 14.85l8.85 -10.85",key:"svg-2"}],["path",{d:"M6 4l8.85 10.85",key:"svg-3"}]]);/**
 * @license @tabler/icons-react v3.17.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var tv=qe("outline","database","IconDatabase",[["path",{d:"M12 6m-8 0a8 3 0 1 0 16 0a8 3 0 1 0 -16 0",key:"svg-0"}],["path",{d:"M4 6v6a8 3 0 0 0 16 0v-6",key:"svg-1"}],["path",{d:"M4 12v6a8 3 0 0 0 16 0v-6",key:"svg-2"}]]);/**
 * @license @tabler/icons-react v3.17.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var WE=qe("outline","file-search","IconFileSearch",[["path",{d:"M14 3v4a1 1 0 0 0 1 1h4",key:"svg-0"}],["path",{d:"M12 21h-5a2 2 0 0 1 -2 -2v-14a2 2 0 0 1 2 -2h7l5 5v4.5",key:"svg-1"}],["path",{d:"M16.5 17.5m-2.5 0a2.5 2.5 0 1 0 5 0a2.5 2.5 0 1 0 -5 0",key:"svg-2"}],["path",{d:"M18.5 19.5l2.5 2.5",key:"svg-3"}]]);/**
 * @license @tabler/icons-react v3.17.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var $E=qe("outline","file-settings","IconFileSettings",[["path",{d:"M12 14m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0",key:"svg-0"}],["path",{d:"M12 10.5v1.5",key:"svg-1"}],["path",{d:"M12 16v1.5",key:"svg-2"}],["path",{d:"M15.031 12.25l-1.299 .75",key:"svg-3"}],["path",{d:"M10.268 15l-1.3 .75",key:"svg-4"}],["path",{d:"M15 15.803l-1.285 -.773",key:"svg-5"}],["path",{d:"M10.285 12.97l-1.285 -.773",key:"svg-6"}],["path",{d:"M14 3v4a1 1 0 0 0 1 1h4",key:"svg-7"}],["path",{d:"M17 21h-10a2 2 0 0 1 -2 -2v-14a2 2 0 0 1 2 -2h7l5 5v11a2 2 0 0 1 -2 2z",key:"svg-8"}]]);/**
 * @license @tabler/icons-react v3.17.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var nv=qe("outline","files","IconFiles",[["path",{d:"M15 3v4a1 1 0 0 0 1 1h4",key:"svg-0"}],["path",{d:"M18 17h-7a2 2 0 0 1 -2 -2v-10a2 2 0 0 1 2 -2h4l5 5v7a2 2 0 0 1 -2 2z",key:"svg-1"}],["path",{d:"M16 17v2a2 2 0 0 1 -2 2h-7a2 2 0 0 1 -2 -2v-10a2 2 0 0 1 2 -2h2",key:"svg-2"}]]);/**
 * @license @tabler/icons-react v3.17.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var av=qe("outline","key","IconKey",[["path",{d:"M16.555 3.843l3.602 3.602a2.877 2.877 0 0 1 0 4.069l-2.643 2.643a2.877 2.877 0 0 1 -4.069 0l-.301 -.301l-6.558 6.558a2 2 0 0 1 -1.239 .578l-.175 .008h-1.172a1 1 0 0 1 -.993 -.883l-.007 -.117v-1.172a2 2 0 0 1 .467 -1.284l.119 -.13l.414 -.414h2v-2h2v-2l2.144 -2.144l-.301 -.301a2.877 2.877 0 0 1 0 -4.069l2.643 -2.643a2.877 2.877 0 0 1 4.069 0z",key:"svg-0"}],["path",{d:"M15 9h.01",key:"svg-1"}]]);/**
 * @license @tabler/icons-react v3.17.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var JE=qe("outline","loader-2","IconLoader2",[["path",{d:"M12 3a9 9 0 1 0 9 9",key:"svg-0"}]]);/**
 * @license @tabler/icons-react v3.17.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var IE=qe("outline","message","IconMessage",[["path",{d:"M8 9h8",key:"svg-0"}],["path",{d:"M8 13h6",key:"svg-1"}],["path",{d:"M18 4a3 3 0 0 1 3 3v8a3 3 0 0 1 -3 3h-5l-5 3v-3h-2a3 3 0 0 1 -3 -3v-8a3 3 0 0 1 3 -3h12z",key:"svg-2"}]]);/**
 * @license @tabler/icons-react v3.17.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var eT=qe("outline","refresh-alert","IconRefreshAlert",[["path",{d:"M20 11a8.1 8.1 0 0 0 -15.5 -2m-.5 -4v4h4",key:"svg-0"}],["path",{d:"M4 13a8.1 8.1 0 0 0 15.5 2m.5 4v-4h-4",key:"svg-1"}],["path",{d:"M12 9l0 3",key:"svg-2"}],["path",{d:"M12 15l.01 0",key:"svg-3"}]]);/**
 * @license @tabler/icons-react v3.17.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var tT=qe("outline","refresh","IconRefresh",[["path",{d:"M20 11a8.1 8.1 0 0 0 -15.5 -2m-.5 -4v4h4",key:"svg-0"}],["path",{d:"M4 13a8.1 8.1 0 0 0 15.5 2m.5 4v-4h-4",key:"svg-1"}]]);/**
 * @license @tabler/icons-react v3.17.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var iv=qe("outline","reload","IconReload",[["path",{d:"M19.933 13.041a8 8 0 1 1 -9.925 -8.788c3.899 -1 7.935 1.007 9.425 4.747",key:"svg-0"}],["path",{d:"M20 4v5h-5",key:"svg-1"}]]);/**
 * @license @tabler/icons-react v3.17.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var nT=qe("outline","settings-2","IconSettings2",[["path",{d:"M19.875 6.27a2.225 2.225 0 0 1 1.125 1.948v7.284c0 .809 -.443 1.555 -1.158 1.948l-6.75 4.27a2.269 2.269 0 0 1 -2.184 0l-6.75 -4.27a2.225 2.225 0 0 1 -1.158 -1.948v-7.285c0 -.809 .443 -1.554 1.158 -1.947l6.75 -3.98a2.33 2.33 0 0 1 2.25 0l6.75 3.98h-.033z",key:"svg-0"}],["path",{d:"M12 12m-3 0a3 3 0 1 0 6 0a3 3 0 1 0 -6 0",key:"svg-1"}]]);/**
 * @license @tabler/icons-react v3.17.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var aT=qe("outline","settings","IconSettings",[["path",{d:"M10.325 4.317c.426 -1.756 2.924 -1.756 3.35 0a1.724 1.724 0 0 0 2.573 1.066c1.543 -.94 3.31 .826 2.37 2.37a1.724 1.724 0 0 0 1.065 2.572c1.756 .426 1.756 2.924 0 3.35a1.724 1.724 0 0 0 -1.066 2.573c.94 1.543 -.826 3.31 -2.37 2.37a1.724 1.724 0 0 0 -2.572 1.065c-.426 1.756 -2.924 1.756 -3.35 0a1.724 1.724 0 0 0 -2.573 -1.066c-1.543 .94 -3.31 -.826 -2.37 -2.37a1.724 1.724 0 0 0 -1.065 -2.572c-1.756 -.426 -1.756 -2.924 0 -3.35a1.724 1.724 0 0 0 1.066 -2.573c-.94 -1.543 .826 -3.31 2.37 -2.37c1 .608 2.296 .07 2.572 -1.065z",key:"svg-0"}],["path",{d:"M9 12a3 3 0 1 0 6 0a3 3 0 0 0 -6 0",key:"svg-1"}]]);/**
 * @license @tabler/icons-react v3.17.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var iT=qe("outline","square-toggle-horizontal","IconSquareToggleHorizontal",[["path",{d:"M22 12h-20",key:"svg-0"}],["path",{d:"M4 14v-8a2 2 0 0 1 2 -2h12a2 2 0 0 1 2 2v8",key:"svg-1"}],["path",{d:"M18 20a2 2 0 0 0 2 -2",key:"svg-2"}],["path",{d:"M4 18a2 2 0 0 0 2 2",key:"svg-3"}],["path",{d:"M14 20l-4 0",key:"svg-4"}]]);/**
 * @license @tabler/icons-react v3.17.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var sT=qe("outline","table","IconTable",[["path",{d:"M3 5a2 2 0 0 1 2 -2h14a2 2 0 0 1 2 2v14a2 2 0 0 1 -2 2h-14a2 2 0 0 1 -2 -2v-14z",key:"svg-0"}],["path",{d:"M3 10h18",key:"svg-1"}],["path",{d:"M10 3v18",key:"svg-2"}]]);/**
 * @license @tabler/icons-react v3.17.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var rT=qe("outline","text-size","IconTextSize",[["path",{d:"M3 7v-2h13v2",key:"svg-0"}],["path",{d:"M10 5v14",key:"svg-1"}],["path",{d:"M12 19h-4",key:"svg-2"}],["path",{d:"M15 13v-1h6v1",key:"svg-3"}],["path",{d:"M18 12v7",key:"svg-4"}],["path",{d:"M17 19h2",key:"svg-5"}]]);/**
 * @license @tabler/icons-react v3.17.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var lT=qe("outline","ticket","IconTicket",[["path",{d:"M15 5l0 2",key:"svg-0"}],["path",{d:"M15 11l0 2",key:"svg-1"}],["path",{d:"M15 17l0 2",key:"svg-2"}],["path",{d:"M5 5h14a2 2 0 0 1 2 2v3a2 2 0 0 0 0 4v3a2 2 0 0 1 -2 2h-14a2 2 0 0 1 -2 -2v-3a2 2 0 0 0 0 -4v-3a2 2 0 0 1 2 -2",key:"svg-3"}]]);/**
 * @license @tabler/icons-react v3.17.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var ad=qe("outline","trash","IconTrash",[["path",{d:"M4 7l16 0",key:"svg-0"}],["path",{d:"M10 11l0 6",key:"svg-1"}],["path",{d:"M14 11l0 6",key:"svg-2"}],["path",{d:"M5 7l1 12a2 2 0 0 0 2 2h8a2 2 0 0 0 2 -2l1 -12",key:"svg-3"}],["path",{d:"M9 7v-3a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v3",key:"svg-4"}]]);/**
 * @license @tabler/icons-react v3.17.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var oT=qe("outline","users","IconUsers",[["path",{d:"M9 7m-4 0a4 4 0 1 0 8 0a4 4 0 1 0 -8 0",key:"svg-0"}],["path",{d:"M3 21v-2a4 4 0 0 1 4 -4h4a4 4 0 0 1 4 4v2",key:"svg-1"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"svg-2"}],["path",{d:"M21 21v-2a4 4 0 0 0 -3 -3.85",key:"svg-3"}]]);/**
 * @license @tabler/icons-react v3.17.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var Bo=qe("outline","world","IconWorld",[["path",{d:"M3 12a9 9 0 1 0 18 0a9 9 0 0 0 -18 0",key:"svg-0"}],["path",{d:"M3.6 9h16.8",key:"svg-1"}],["path",{d:"M3.6 15h16.8",key:"svg-2"}],["path",{d:"M11.5 3a17 17 0 0 0 0 18",key:"svg-3"}],["path",{d:"M12.5 3a17 17 0 0 1 0 18",key:"svg-4"}]]);/**
 * @license @tabler/icons-react v3.17.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var uT=qe("outline","x","IconX",[["path",{d:"M18 6l-12 12",key:"svg-0"}],["path",{d:"M6 6l12 12",key:"svg-1"}]]);/**
 * @license @tabler/icons-react v3.17.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var cT=qe("filled","circle-check-filled","IconCircleCheckFilled",[["path",{d:"M17 3.34a10 10 0 1 1 -14.995 8.984l-.005 -.324l.005 -.324a10 10 0 0 1 14.995 -8.336zm-1.293 5.953a1 1 0 0 0 -1.32 -.083l-.094 .083l-3.293 3.292l-1.293 -1.292l-.094 -.083a1 1 0 0 0 -1.403 1.403l.083 .094l2 2l.094 .083a1 1 0 0 0 1.226 0l.094 -.083l4 -4l.083 -.094a1 1 0 0 0 -.083 -1.32z",key:"svg-0"}]]);/**
 * @license @tabler/icons-react v3.17.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var fT=qe("filled","dashboard-filled","IconDashboardFilled",[["path",{d:"M12 2.954a10 10 0 0 1 6.222 17.829a1 1 0 0 1 -.622 .217h-11.2a1 1 0 0 1 -.622 -.217a10 10 0 0 1 6.222 -17.829m4.207 5.839a1 1 0 0 0 -1.414 0l-2.276 2.274a2.003 2.003 0 0 0 -2.514 1.815l-.003 .118a2 2 0 1 0 3.933 -.517l2.274 -2.276a1 1 0 0 0 0 -1.414",key:"svg-0"}]]);/**
 * @license @tabler/icons-react v3.17.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var dT=qe("filled","layout-dashboard-filled","IconLayoutDashboardFilled",[["path",{d:"M9 3a2 2 0 0 1 2 2v6a2 2 0 0 1 -2 2h-4a2 2 0 0 1 -2 -2v-6a2 2 0 0 1 2 -2zm0 12a2 2 0 0 1 2 2v2a2 2 0 0 1 -2 2h-4a2 2 0 0 1 -2 -2v-2a2 2 0 0 1 2 -2zm10 -4a2 2 0 0 1 2 2v6a2 2 0 0 1 -2 2h-4a2 2 0 0 1 -2 -2v-6a2 2 0 0 1 2 -2zm0 -8a2 2 0 0 1 2 2v2a2 2 0 0 1 -2 2h-4a2 2 0 0 1 -2 -2v-2a2 2 0 0 1 2 -2z",key:"svg-0"}]]);/**
 * @license @tabler/icons-react v3.17.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var hT=qe("filled","stack-2-filled","IconStack2Filled",[["path",{d:"M20.894 15.553a1 1 0 0 1 -.447 1.341l-8 4a1 1 0 0 1 -.894 0l-8 -4a1 1 0 0 1 .894 -1.788l7.553 3.774l7.554 -3.775a1 1 0 0 1 1.341 .447m0 -4a1 1 0 0 1 -.447 1.341l-8 4a1 1 0 0 1 -.894 0l-8 -4a1 1 0 0 1 .894 -1.788l7.552 3.775l7.554 -3.775a1 1 0 0 1 1.341 .447m-8.887 -8.552q .056 0 .111 .007l.111 .02l.086 .024l.012 .006l.012 .002l.029 .014l.05 .019l.016 .009l.012 .005l8 4a1 1 0 0 1 0 1.788l-8 4a1 1 0 0 1 -.894 0l-8 -4a1 1 0 0 1 0 -1.788l8 -4l.011 -.005l.018 -.01l.078 -.032l.011 -.002l.013 -.006l.086 -.024l.11 -.02l.056 -.005z",key:"svg-0"}]]);const Es=window.flying_press_rest_url,mT="https://license.flyingpress.com";function sv(){return o0({queryKey:["cacheStatus"],queryFn:async()=>{const t=await fetch(`${Es}/cache_status/`,{method:"POST"});if(!t.ok)throw He.error("Failed to fetch cache status"),new Error("Failed to fetch cache status");return t.json()},refetchInterval:1e4,retry:!1})}function rv(){const t=jr();return Ar({mutationFn:async()=>{const a=await fetch(`${Es}/preload-cache/`,{method:"POST"});if(!a.ok)throw new Error("Failed to start cache preloading");return a.json()},onSuccess:()=>{He.success("Cache preloading started"),t.invalidateQueries(["cacheStatus"])},onError:()=>He.error("Failed to start cache preloading"),retry:!1})}function pT(){const t=jr();return Ar({mutationFn:async()=>{const a=await fetch(`${Es}/purge-pages-and-preload/`,{method:"POST"});if(!a.ok)throw new Error("Failed to purge and preload cache");return a.json()},onSuccess:()=>{He.success("Cache purged and preloading started"),t.invalidateQueries(["cacheStatus"])},onError:()=>He.error("Failed to purge and preload cache"),retry:!1})}function yT(){const t=jr();return Ar({mutationFn:async()=>{const a=await fetch(`${Es}/purge-everything/`,{method:"POST"});if(!a.ok)throw new Error("Failed to purge entire cache");return a.json()},onSuccess:()=>{He.success("Purged entire cache"),t.invalidateQueries(["cacheStatus"])},onError:()=>He.error("Failed to purge entire cache"),retry:!1})}function gT(){return Ar({mutationFn:async t=>{const a=He.loading("Saving configuration...");if(!(await fetch(`${Es}/config/`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)})).ok)throw He.dismiss(a),new Error("Failed to update config");return He.success("Configuration saved successfully",{id:a}),t},onError:()=>He.error("Failed to save configuration"),retry:!1})}function vT(){var a,s;const t=(s=(a=window==null?void 0:window.flying_press)==null?void 0:a.config)==null?void 0:s.license_key;return o0({queryKey:["validateLicense"],queryFn:async()=>{const r=await fetch(`${mT}/validate/${t}`);if(!r.ok)throw new Error("Failed to validate license");return r.json()},retry:!1})}function lv(){return Ar({mutationFn:async t=>{const a=await fetch(`${Es}/activate-license/`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({license_key:t})});if(!a.ok){const s=await a.json();throw new Error(s.message||"Failed to activate license")}window.flying_press.config=await a.json()},onError:t=>He.error(t.message),onSuccess:()=>He.success("License activated successfully")})}function ut({variant:t="default",size:a="md",className:s="",loading:r,children:o,onClick:c,...d}){const h={default:"text-gray-600 ring-1 ring-gray-300 ring-inset",outline:"text-indigo-600 ring-1 ring-indigo-600 ring-inset",selected:"text-indigo-600 ring-2 ring-indigo-600 ring-inset",primary:"bg-indigo-600 text-white hover:bg-indigo-700",danger:"bg-red-600 text-white hover:bg-red-700"},p={sm:"px-2.5 py-1.5 text-xs",md:"px-4 py-2 text-sm",lg:"px-6 py-3 text-base"};return m.jsxs("button",{type:"button",className:`flex items-center justify-center gap-2 rounded-lg font-medium shadow-xs transition-all focus:outline-none hover:cursor-pointer whitespace-nowrap ${h[t]} ${p[a]} ${s}`,onClick:c,...d,children:[o,r&&m.jsx(JE,{className:"size-4 animate-spin"})]})}function bT({className:t=""}){return m.jsxs("svg",{className:t,width:"1299",height:"235",viewBox:"0 0 1299 235",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[m.jsx("path",{d:"M158.65 0C138.27 0 119.28 10.31 108.17 27.4L79.69 71.23L0 194.09H23.93C37.42 194.09 49.99 187.25 57.32 175.92L104.91 102.74C117.62 83.09 139.42 71.23 162.81 71.23H290.92C311.3 71.23 330.29 60.92 341.4 43.83L369.88 0H158.65Z",fill:"#4F46E5"}),m.jsx("path",{d:"M173.03 94.3984C152.65 94.3984 133.65 104.708 122.54 121.798L122.2 122.318L116.12 131.678L94.0602 165.628H94.0702L49.1602 234.778H73.2902C86.6602 234.778 99.1201 228.018 106.4 216.808L130.01 180.468C136.02 171.208 146.31 165.628 157.35 165.628H244.64C265.02 165.628 284.01 155.318 295.12 138.228L323.6 94.3984H173.03Z",fill:"#4F46E5"}),m.jsx("path",{d:"M477.915 49.2406V64.8646H428.355V99.3046H466.995V114.929H428.355V166.001H409.203V49.2406H477.915Z",fill:"#1E1B4B"}),m.jsx("path",{d:"M511.456 41.6806V166.001H492.304V41.6806H511.456Z",fill:"#1E1B4B"}),m.jsx("path",{d:"M616.569 73.4326L559.785 209.513H539.961L558.777 164.489L522.321 73.4326H543.657L569.697 143.993L596.745 73.4326H616.569Z",fill:"#1E1B4B"}),m.jsx("path",{d:"M637.406 61.1686C633.934 61.1686 631.022 59.9926 628.67 57.6406C626.318 55.2886 625.142 52.3766 625.142 48.9046C625.142 45.4326 626.318 42.5206 628.67 40.1686C631.022 37.8166 633.934 36.6406 637.406 36.6406C640.766 36.6406 643.622 37.8166 645.974 40.1686C648.326 42.5206 649.502 45.4326 649.502 48.9046C649.502 52.3766 648.326 55.2886 645.974 57.6406C643.622 59.9926 640.766 61.1686 637.406 61.1686ZM646.814 73.4326V166.001H627.662V73.4326H646.814Z",fill:"#1E1B4B"}),m.jsx("path",{d:"M715.303 71.9206C722.583 71.9206 729.079 73.4326 734.791 76.4566C740.615 79.4806 745.151 83.9606 748.399 89.8966C751.647 95.8326 753.271 103.001 753.271 111.401V166.001H734.287V114.257C734.287 105.969 732.215 99.6406 728.071 95.2726C723.927 90.7926 718.271 88.5526 711.103 88.5526C703.935 88.5526 698.223 90.7926 693.967 95.2726C689.823 99.6406 687.751 105.969 687.751 114.257V166.001H668.599V73.4326H687.751V84.0166C690.887 80.2086 694.863 77.2406 699.679 75.1126C704.607 72.9846 709.815 71.9206 715.303 71.9206Z",fill:"#1E1B4B"}),m.jsx("path",{d:"M811.637 71.9206C818.805 71.9206 825.133 73.3766 830.621 76.2886C836.221 79.0886 840.589 82.6166 843.725 86.8726V73.4326H863.045V167.513C863.045 176.025 861.253 183.585 857.669 190.193C854.085 196.913 848.877 202.177 842.045 205.985C835.325 209.793 827.261 211.697 817.853 211.697C805.309 211.697 794.893 208.729 786.605 202.793C778.317 196.969 773.613 189.017 772.493 178.937H791.477C792.933 183.753 796.013 187.617 800.717 190.529C805.533 193.553 811.245 195.065 817.853 195.065C825.581 195.065 831.797 192.713 836.501 188.009C841.317 183.305 843.725 176.473 843.725 167.513V152.057C840.477 156.425 836.053 160.121 830.453 163.145C824.965 166.057 818.693 167.513 811.637 167.513C803.573 167.513 796.181 165.497 789.461 161.465C782.853 157.321 777.589 151.609 773.669 144.329C769.861 136.937 767.957 128.593 767.957 119.297C767.957 110.001 769.861 101.769 773.669 94.6006C777.589 87.4326 782.853 81.8886 789.461 77.9686C796.181 73.9366 803.573 71.9206 811.637 71.9206ZM843.725 119.633C843.725 113.249 842.381 107.705 839.693 103.001C837.117 98.2966 833.701 94.7126 829.445 92.2486C825.189 89.7846 820.597 88.5526 815.669 88.5526C810.741 88.5526 806.149 89.7846 801.893 92.2486C797.637 94.6006 794.165 98.1286 791.477 102.833C788.901 107.425 787.613 112.913 787.613 119.297C787.613 125.681 788.901 131.281 791.477 136.097C794.165 140.913 797.637 144.609 801.893 147.185C806.261 149.649 810.853 150.881 815.669 150.881C820.597 150.881 825.189 149.649 829.445 147.185C833.701 144.721 837.117 141.137 839.693 136.433C842.381 131.617 843.725 126.017 843.725 119.633Z",fill:"#1E1B4B"}),m.jsx("path",{d:"M966.488 84.0166C966.488 89.9526 965.088 95.5526 962.288 100.817C959.488 106.081 955.008 110.393 948.848 113.753C942.688 117.001 934.792 118.625 925.16 118.625H903.992V166.001H884.84V49.2406H925.16C934.12 49.2406 941.68 50.8086 947.84 53.9446C954.112 56.9686 958.76 61.1126 961.784 66.3766C964.92 71.6406 966.488 77.5206 966.488 84.0166ZM925.16 103.001C932.44 103.001 937.872 101.377 941.456 98.1286C945.04 94.7686 946.832 90.0646 946.832 84.0166C946.832 71.2486 939.608 64.8646 925.16 64.8646H903.992V103.001H925.16Z",fill:"#1E1B4B"}),m.jsx("path",{d:"M1000.55 86.8726C1003.35 82.1686 1007.04 78.5286 1011.63 75.9526C1016.34 73.2646 1021.88 71.9206 1028.27 71.9206V91.7446H1023.39C1015.89 91.7446 1010.18 93.6486 1006.26 97.4566C1002.45 101.265 1000.55 107.873 1000.55 117.281V166.001H981.394V73.4326H1000.55V86.8726Z",fill:"#1E1B4B"}),m.jsx("path",{d:"M1127.35 117.449C1127.35 120.921 1127.13 124.057 1126.68 126.857H1055.95C1056.51 134.249 1059.25 140.185 1064.18 144.665C1069.11 149.145 1075.16 151.385 1082.33 151.385C1092.63 151.385 1099.91 147.073 1104.17 138.449H1124.83C1122.03 146.961 1116.93 153.961 1109.54 159.449C1102.26 164.825 1093.19 167.513 1082.33 167.513C1073.48 167.513 1065.53 165.553 1058.47 161.633C1051.53 157.601 1046.04 152.001 1042.01 144.833C1038.09 137.553 1036.13 129.153 1036.13 119.633C1036.13 110.113 1038.03 101.769 1041.84 94.6006C1045.76 87.3206 1051.19 81.7206 1058.13 77.8006C1065.19 73.8806 1073.25 71.9206 1082.33 71.9206C1091.06 71.9206 1098.85 73.8246 1105.68 77.6326C1112.51 81.4406 1117.83 86.8166 1121.64 93.7606C1125.45 100.593 1127.35 108.489 1127.35 117.449ZM1107.36 111.401C1107.25 104.345 1104.73 98.6886 1099.8 94.4326C1094.87 90.1766 1088.77 88.0486 1081.49 88.0486C1074.88 88.0486 1069.22 90.1766 1064.52 94.4326C1059.81 98.5766 1057.01 104.233 1056.12 111.401H1107.36Z",fill:"#1E1B4B"}),m.jsx("path",{d:"M1176.44 167.513C1169.16 167.513 1162.61 166.225 1156.78 163.649C1151.07 160.961 1146.53 157.377 1143.17 152.897C1139.81 148.305 1138.02 143.209 1137.8 137.609H1157.62C1157.96 141.529 1159.81 144.833 1163.17 147.521C1166.64 150.097 1170.95 151.385 1176.1 151.385C1181.48 151.385 1185.62 150.377 1188.53 148.361C1191.56 146.233 1193.07 143.545 1193.07 140.297C1193.07 136.825 1191.39 134.249 1188.03 132.569C1184.78 130.889 1179.57 129.041 1172.41 127.025C1165.46 125.121 1159.81 123.273 1155.44 121.481C1151.07 119.689 1147.26 116.945 1144.01 113.249C1140.88 109.553 1139.31 104.681 1139.31 98.6326C1139.31 93.7046 1140.77 89.2246 1143.68 85.1926C1146.59 81.0486 1150.73 77.8006 1156.11 75.4486C1161.6 73.0966 1167.87 71.9206 1174.93 71.9206C1185.45 71.9206 1193.91 74.6086 1200.29 79.9846C1206.79 85.2486 1210.26 92.4726 1210.71 101.657H1191.56C1191.22 97.5126 1189.54 94.2086 1186.52 91.7446C1183.49 89.2806 1179.41 88.0486 1174.25 88.0486C1169.21 88.0486 1165.35 89.0006 1162.66 90.9046C1159.97 92.8086 1158.63 95.3286 1158.63 98.4646C1158.63 100.929 1159.53 103.001 1161.32 104.681C1163.11 106.361 1165.29 107.705 1167.87 108.713C1170.45 109.609 1174.25 110.785 1179.29 112.241C1186.01 114.033 1191.5 115.881 1195.76 117.785C1200.13 119.577 1203.88 122.265 1207.01 125.849C1210.15 129.433 1211.77 134.193 1211.89 140.129C1211.89 145.393 1210.43 150.097 1207.52 154.241C1204.61 158.385 1200.46 161.633 1195.09 163.985C1189.82 166.337 1183.61 167.513 1176.44 167.513Z",fill:"#1E1B4B"}),m.jsx("path",{d:"M1262.82 167.513C1255.54 167.513 1248.99 166.225 1243.16 163.649C1237.45 160.961 1232.92 157.377 1229.56 152.897C1226.2 148.305 1224.4 143.209 1224.18 137.609H1244C1244.34 141.529 1246.19 144.833 1249.55 147.521C1253.02 150.097 1257.33 151.385 1262.48 151.385C1267.86 151.385 1272 150.377 1274.92 148.361C1277.94 146.233 1279.45 143.545 1279.45 140.297C1279.45 136.825 1277.77 134.249 1274.41 132.569C1271.16 130.889 1265.96 129.041 1258.79 127.025C1251.84 125.121 1246.19 123.273 1241.82 121.481C1237.45 119.689 1233.64 116.945 1230.4 113.249C1227.26 109.553 1225.69 104.681 1225.69 98.6326C1225.69 93.7046 1227.15 89.2246 1230.06 85.1926C1232.97 81.0486 1237.12 77.8006 1242.49 75.4486C1247.98 73.0966 1254.25 71.9206 1261.31 71.9206C1271.84 71.9206 1280.29 74.6086 1286.68 79.9846C1293.17 85.2486 1296.64 92.4726 1297.09 101.657H1277.94C1277.6 97.5126 1275.92 94.2086 1272.9 91.7446C1269.88 89.2806 1265.79 88.0486 1260.64 88.0486C1255.6 88.0486 1251.73 89.0006 1249.04 90.9046C1246.36 92.8086 1245.01 95.3286 1245.01 98.4646C1245.01 100.929 1245.91 103.001 1247.7 104.681C1249.49 106.361 1251.68 107.705 1254.25 108.713C1256.83 109.609 1260.64 110.785 1265.68 112.241C1272.4 114.033 1277.88 115.881 1282.14 117.785C1286.51 119.577 1290.26 122.265 1293.4 125.849C1296.53 129.433 1298.16 134.193 1298.27 140.129C1298.27 145.393 1296.81 150.097 1293.9 154.241C1290.99 158.385 1286.84 161.633 1281.47 163.985C1276.2 166.337 1269.99 167.513 1262.82 167.513Z",fill:"#1E1B4B"})]})}const xT=((f1=window==null?void 0:window.flying_press)==null?void 0:f1.version)||"5.0.0";function ST(){const{data:t,isFetching:a,refetch:s}=sv(),{mutate:r,isPending:o}=rv();return m.jsxs("header",{className:"flex flex-col md:flex-row justify-between items-start md:items-center py-8 gap-4",children:[m.jsxs("div",{className:"flex items-center space-x-4 mb-4 md:mb-0",children:[m.jsx(bT,{className:"w-44 h-auto"}),m.jsxs("p",{className:"text-xs font-mono text-gray-500",children:["v",xT]})]}),m.jsxs("div",{className:"flex space-x-4 w-full md:w-auto",children:[m.jsxs(ut,{variant:"default",className:"w-full",onClick:s,children:[(t==null?void 0:t.pages_cached)||0," pages cached",m.jsx(iv,{className:`size-4 ${a?"animate-spin":""}`})]}),m.jsx(ut,{variant:"primary",className:"w-full",onClick:r,loading:o,children:"Preload cache"})]})]})}const wT=[{name:"Dashboard",path:"/",icon:dT},{name:"Optimization",path:"/optimization",icon:fT},{name:"Caching",path:"/caching",icon:nv},{name:"CDN",path:"/cdn",icon:Bo},{name:"Database",path:"/database",icon:tv},{name:"Bloat",path:"/bloat",icon:ZE},{name:"Settings",path:"/settings",icon:nT}];function ET(){const[t]=oo();return m.jsx("nav",{className:"flex overflow-x-auto relative",children:wT.map(a=>{const s=t===a.path,r=a.icon;return m.jsxs($S,{href:a.path,className:`relative flex items-center gap-2 px-4 py-2 hover:text-indigo-600 cursor-pointer text-base font-medium transition whitespace-nowrap !shadow-none ${s?"text-indigo-600":"text-gray-500"}`,children:[m.jsx(r,{className:"size-5"}),a.name,s&&m.jsx(Do.div,{layoutId:"underline",className:"absolute bottom-0 left-0 h-[3px] bg-indigo-600 w-full",transition:{type:"spring",stiffness:500,damping:40}})]},a.path)})})}function Oe({name:t}){const{register:a}=fn();return m.jsxs("label",{className:"relative inline-flex items-center cursor-pointer",children:[m.jsx("input",{type:"checkbox",className:"peer sr-only",...a(t)}),m.jsx("div",{className:"peer-checked:after:translate-x-5 peer-checked:bg-indigo-600 after:content-[''] after:absolute after:top-0.5 after:left-[2px] after:bg-white after:rounded-full after:h-4 after:w-4 after:transition-all relative h-5 w-10 rounded-full bg-gray-200 transition-colors duration-200 ease-in-out"})]})}function TT(){return m.jsx("section",{className:"space-y-6",children:m.jsxs("div",{className:"bg-white shadow-xs border border-gray-200 rounded-xl p-6 space-y-4",children:[m.jsxs("h3",{className:"font-medium text-base text-gray-900",children:[m.jsx(ad,{className:"size-6 inline-block mr-2 mb-1 text-indigo-600"}),"Remove WordPress Bloat"]}),m.jsx("div",{className:"flex flex-col gap-2",children:m.jsxs("div",{className:"flex justify-between gap-2",children:[m.jsxs("div",{children:[m.jsx("span",{className:"font-normal text-base text-gray-900",children:"Disable Block Editor CSS"}),m.jsx("p",{className:"text-sm text-gray-500 mt-1",children:"Removes styles added by the block editor and WooCommerce blocks"})]}),m.jsx(Oe,{name:"bloat_disable_block_css"})]})}),m.jsx("div",{className:"border-t border-gray-200"}),m.jsx("div",{className:"flex flex-col gap-2",children:m.jsxs("div",{className:"flex justify-between gap-2",children:[m.jsxs("div",{children:[m.jsx("span",{className:"font-normal text-base text-gray-900",children:"Remove Dashicons"}),m.jsx("p",{className:"text-sm text-gray-500 mt-1",children:"Stops loading the admin icon font for visitors"})]}),m.jsx(Oe,{name:"bloat_disable_dashicons"})]})}),m.jsx("div",{className:"border-t border-gray-200"}),m.jsx("div",{className:"flex flex-col gap-2",children:m.jsxs("div",{className:"flex justify-between gap-2",children:[m.jsxs("div",{children:[m.jsx("span",{className:"font-normal text-base text-gray-900",children:"Disable Emojis"}),m.jsx("p",{className:"text-sm text-gray-500 mt-1",children:"Prevents loading emoji-related scripts and styles"})]}),m.jsx(Oe,{name:"bloat_disable_emojis"})]})}),m.jsx("div",{className:"border-t border-gray-200"}),m.jsx("div",{className:"flex flex-col gap-2",children:m.jsxs("div",{className:"flex justify-between gap-2",children:[m.jsxs("div",{children:[m.jsx("span",{className:"font-normal text-base text-gray-900",children:"Disable jQuery Migrate"}),m.jsx("p",{className:"text-sm text-gray-500 mt-1",children:"Stops loading old compatibility scripts related to jQuery"})]}),m.jsx(Oe,{name:"bloat_disable_jquery_migrate"})]})}),m.jsx("div",{className:"border-t border-gray-200"}),m.jsx("div",{className:"flex flex-col gap-2",children:m.jsxs("div",{className:"flex justify-between gap-2",children:[m.jsxs("div",{children:[m.jsx("span",{className:"font-normal text-base text-gray-900",children:"Disable XML-RPC"}),m.jsx("p",{className:"text-sm text-gray-500 mt-1",children:"Blocks remote access features not used by most sites"})]}),m.jsx(Oe,{name:"bloat_disable_xml_rpc"})]})}),m.jsx("div",{className:"border-t border-gray-200"}),m.jsx("div",{className:"flex flex-col gap-2",children:m.jsxs("div",{className:"flex justify-between gap-2",children:[m.jsxs("div",{children:[m.jsx("span",{className:"font-normal text-base text-gray-900",children:"Disable RSS Feed"}),m.jsx("p",{className:"text-sm text-gray-500 mt-1",children:"Turns off RSS and feed links from your site"})]}),m.jsx(Oe,{name:"bloat_disable_rss_feed"})]})}),m.jsx("div",{className:"border-t border-gray-200"}),m.jsx("div",{className:"flex flex-col gap-2",children:m.jsxs("div",{className:"flex justify-between gap-2",children:[m.jsxs("div",{children:[m.jsx("span",{className:"font-normal text-base text-gray-900",children:"Disable oEmbeds"}),m.jsx("p",{className:"text-sm text-gray-500 mt-1",children:"Prevents WordPress from automatically embedding external content"})]}),m.jsx(Oe,{name:"bloat_disable_oembeds"})]})}),m.jsx("div",{className:"border-t border-gray-200"}),m.jsx("div",{className:"flex flex-col gap-2",children:m.jsxs("div",{className:"flex justify-between gap-2",children:[m.jsxs("div",{children:[m.jsx("span",{className:"font-normal text-base text-gray-900",children:"Disable WP Cron"}),m.jsx("p",{className:"text-sm text-gray-500 mt-1",children:"Turns off WordPress's built-in background task scheduler"})]}),m.jsx(Oe,{name:"bloat_disable_cron"})]})}),m.jsx("div",{className:"border-t border-gray-200"}),m.jsx("div",{className:"flex flex-col gap-2",children:m.jsxs("div",{className:"flex justify-between gap-2",children:[m.jsxs("div",{children:[m.jsx("span",{className:"font-normal text-base text-gray-900",children:"Limit Post Revisions"}),m.jsx("p",{className:"text-sm text-gray-500 mt-1",children:"Limits WordPress to keeping only 3 revisions for each post update"})]}),m.jsx(Oe,{name:"bloat_post_revisions_control"})]})}),m.jsx("div",{className:"border-t border-gray-200"}),m.jsx("div",{className:"flex flex-col gap-2",children:m.jsxs("div",{className:"flex justify-between gap-2",children:[m.jsxs("div",{children:[m.jsx("span",{className:"font-normal text-base text-gray-900",children:"Throttle Heartbeat API"}),m.jsx("p",{className:"text-sm text-gray-500 mt-1",children:"Limits background activity in the browser to once every 60 seconds"})]}),m.jsx(Oe,{name:"bloat_heartbeat_control"})]})})]})})}function Va(t,a,{checkForDefaultPrevented:s=!0}={}){return function(o){if(t==null||t(o),s===!1||!o.defaultPrevented)return a==null?void 0:a(o)}}function ov(t,a){if(typeof t=="function")return t(a);t!=null&&(t.current=a)}function uv(...t){return a=>{let s=!1;const r=t.map(o=>{const c=ov(o,a);return!s&&typeof c=="function"&&(s=!0),c});if(s)return()=>{for(let o=0;o<r.length;o++){const c=r[o];typeof c=="function"?c():ov(t[o],null)}}}}function Di(...t){return w.useCallback(uv(...t),t)}function CT(t,a){const s=w.createContext(a),r=c=>{const{children:d,...h}=c,p=w.useMemo(()=>h,Object.values(h));return m.jsx(s.Provider,{value:p,children:d})};r.displayName=t+"Provider";function o(c){const d=w.useContext(s);if(d)return d;if(a!==void 0)return a;throw new Error(`\`${c}\` must be used within \`${t}\``)}return[r,o]}function jT(t,a=[]){let s=[];function r(c,d){const h=w.createContext(d),p=s.length;s=[...s,d];const y=b=>{var z;const{scope:S,children:E,...N}=b,C=((z=S==null?void 0:S[t])==null?void 0:z[p])||h,R=w.useMemo(()=>N,Object.values(N));return m.jsx(C.Provider,{value:R,children:E})};y.displayName=c+"Provider";function v(b,S){var C;const E=((C=S==null?void 0:S[t])==null?void 0:C[p])||h,N=w.useContext(E);if(N)return N;if(d!==void 0)return d;throw new Error(`\`${b}\` must be used within \`${c}\``)}return[y,v]}const o=()=>{const c=s.map(d=>w.createContext(d));return function(h){const p=(h==null?void 0:h[t])||c;return w.useMemo(()=>({[`__scope${t}`]:{...h,[t]:p}}),[h,p])}};return o.scopeName=t,[r,AT(o,...a)]}function AT(...t){const a=t[0];if(t.length===1)return a;const s=()=>{const r=t.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(c){const d=r.reduce((h,{useScope:p,scopeName:y})=>{const b=p(c)[`__scope${y}`];return{...h,...b}},{});return w.useMemo(()=>({[`__scope${a.scopeName}`]:d}),[d])}};return s.scopeName=a.scopeName,s}var Ho=globalThis!=null&&globalThis.document?w.useLayoutEffect:()=>{},DT=a0.useId||(()=>{}),_T=0;function id(t){const[a,s]=w.useState(DT());return Ho(()=>{s(r=>r??String(_T++))},[t]),t||(a?`radix-${a}`:"")}function _i(t){const a=w.useRef(t);return w.useEffect(()=>{a.current=t}),w.useMemo(()=>(...s)=>{var r;return(r=a.current)==null?void 0:r.call(a,...s)},[])}function MT({prop:t,defaultProp:a,onChange:s=()=>{}}){const[r,o]=NT({defaultProp:a,onChange:s}),c=t!==void 0,d=c?t:r,h=_i(s),p=w.useCallback(y=>{if(c){const b=typeof y=="function"?y(t):y;b!==t&&h(b)}else o(y)},[c,t,o,h]);return[d,p]}function NT({defaultProp:t,onChange:a}){const s=w.useState(t),[r]=s,o=w.useRef(r),c=_i(a);return w.useEffect(()=>{o.current!==r&&(c(r),o.current=r)},[r,o,c]),s}var cv=h0();const RT=Ca(cv);var sd=w.forwardRef((t,a)=>{const{children:s,...r}=t,o=w.Children.toArray(s),c=o.find(VT);if(c){const d=c.props.children,h=o.map(p=>p===c?w.Children.count(d)>1?w.Children.only(null):w.isValidElement(d)?d.props.children:null:p);return m.jsx(rd,{...r,ref:a,children:w.isValidElement(d)?w.cloneElement(d,void 0,h):null})}return m.jsx(rd,{...r,ref:a,children:s})});sd.displayName="Slot";var rd=w.forwardRef((t,a)=>{const{children:s,...r}=t;if(w.isValidElement(s)){const o=kT(s),c=zT(r,s.props);return s.type!==w.Fragment&&(c.ref=a?uv(a,o):o),w.cloneElement(s,c)}return w.Children.count(s)>1?w.Children.only(null):null});rd.displayName="SlotClone";var OT=({children:t})=>m.jsx(m.Fragment,{children:t});function VT(t){return w.isValidElement(t)&&t.type===OT}function zT(t,a){const s={...a};for(const r in a){const o=t[r],c=a[r];/^on[A-Z]/.test(r)?o&&c?s[r]=(...h)=>{c(...h),o(...h)}:o&&(s[r]=o):r==="style"?s[r]={...o,...c}:r==="className"&&(s[r]=[o,c].filter(Boolean).join(" "))}return{...t,...s}}function kT(t){var r,o;let a=(r=Object.getOwnPropertyDescriptor(t.props,"ref"))==null?void 0:r.get,s=a&&"isReactWarning"in a&&a.isReactWarning;return s?t.ref:(a=(o=Object.getOwnPropertyDescriptor(t,"ref"))==null?void 0:o.get,s=a&&"isReactWarning"in a&&a.isReactWarning,s?t.props.ref:t.props.ref||t.ref)}var LT=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"],aa=LT.reduce((t,a)=>{const s=w.forwardRef((r,o)=>{const{asChild:c,...d}=r,h=c?sd:a;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),m.jsx(h,{...d,ref:o})});return s.displayName=`Primitive.${a}`,{...t,[a]:s}},{});function UT(t,a){t&&cv.flushSync(()=>t.dispatchEvent(a))}function BT(t,a=globalThis==null?void 0:globalThis.document){const s=_i(t);w.useEffect(()=>{const r=o=>{o.key==="Escape"&&s(o)};return a.addEventListener("keydown",r,{capture:!0}),()=>a.removeEventListener("keydown",r,{capture:!0})},[s,a])}var HT="DismissableLayer",ld="dismissableLayer.update",PT="dismissableLayer.pointerDownOutside",qT="dismissableLayer.focusOutside",fv,dv=w.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),hv=w.forwardRef((t,a)=>{const{disableOutsidePointerEvents:s=!1,onEscapeKeyDown:r,onPointerDownOutside:o,onFocusOutside:c,onInteractOutside:d,onDismiss:h,...p}=t,y=w.useContext(dv),[v,b]=w.useState(null),S=(v==null?void 0:v.ownerDocument)??(globalThis==null?void 0:globalThis.document),[,E]=w.useState({}),N=Di(a,J=>b(J)),C=Array.from(y.layers),[R]=[...y.layersWithOutsidePointerEventsDisabled].slice(-1),z=C.indexOf(R),B=v?C.indexOf(v):-1,H=y.layersWithOutsidePointerEventsDisabled.size>0,W=B>=z,O=YT(J=>{const Z=J.target,Y=[...y.branches].some(te=>te.contains(Z));!W||Y||(o==null||o(J),d==null||d(J),J.defaultPrevented||h==null||h())},S),X=QT(J=>{const Z=J.target;[...y.branches].some(te=>te.contains(Z))||(c==null||c(J),d==null||d(J),J.defaultPrevented||h==null||h())},S);return BT(J=>{B===y.layers.size-1&&(r==null||r(J),!J.defaultPrevented&&h&&(J.preventDefault(),h()))},S),w.useEffect(()=>{if(v)return s&&(y.layersWithOutsidePointerEventsDisabled.size===0&&(fv=S.body.style.pointerEvents,S.body.style.pointerEvents="none"),y.layersWithOutsidePointerEventsDisabled.add(v)),y.layers.add(v),mv(),()=>{s&&y.layersWithOutsidePointerEventsDisabled.size===1&&(S.body.style.pointerEvents=fv)}},[v,S,s,y]),w.useEffect(()=>()=>{v&&(y.layers.delete(v),y.layersWithOutsidePointerEventsDisabled.delete(v),mv())},[v,y]),w.useEffect(()=>{const J=()=>E({});return document.addEventListener(ld,J),()=>document.removeEventListener(ld,J)},[]),m.jsx(aa.div,{...p,ref:N,style:{pointerEvents:H?W?"auto":"none":void 0,...t.style},onFocusCapture:Va(t.onFocusCapture,X.onFocusCapture),onBlurCapture:Va(t.onBlurCapture,X.onBlurCapture),onPointerDownCapture:Va(t.onPointerDownCapture,O.onPointerDownCapture)})});hv.displayName=HT;var FT="DismissableLayerBranch",GT=w.forwardRef((t,a)=>{const s=w.useContext(dv),r=w.useRef(null),o=Di(a,r);return w.useEffect(()=>{const c=r.current;if(c)return s.branches.add(c),()=>{s.branches.delete(c)}},[s.branches]),m.jsx(aa.div,{...t,ref:o})});GT.displayName=FT;function YT(t,a=globalThis==null?void 0:globalThis.document){const s=_i(t),r=w.useRef(!1),o=w.useRef(()=>{});return w.useEffect(()=>{const c=h=>{if(h.target&&!r.current){let p=function(){pv(PT,s,y,{discrete:!0})};const y={originalEvent:h};h.pointerType==="touch"?(a.removeEventListener("click",o.current),o.current=p,a.addEventListener("click",o.current,{once:!0})):p()}else a.removeEventListener("click",o.current);r.current=!1},d=window.setTimeout(()=>{a.addEventListener("pointerdown",c)},0);return()=>{window.clearTimeout(d),a.removeEventListener("pointerdown",c),a.removeEventListener("click",o.current)}},[a,s]),{onPointerDownCapture:()=>r.current=!0}}function QT(t,a=globalThis==null?void 0:globalThis.document){const s=_i(t),r=w.useRef(!1);return w.useEffect(()=>{const o=c=>{c.target&&!r.current&&pv(qT,s,{originalEvent:c},{discrete:!1})};return a.addEventListener("focusin",o),()=>a.removeEventListener("focusin",o)},[a,s]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}function mv(){const t=new CustomEvent(ld);document.dispatchEvent(t)}function pv(t,a,s,{discrete:r}){const o=s.originalEvent.target,c=new CustomEvent(t,{bubbles:!1,cancelable:!0,detail:s});a&&o.addEventListener(t,a,{once:!0}),r?UT(o,c):o.dispatchEvent(c)}var od="focusScope.autoFocusOnMount",ud="focusScope.autoFocusOnUnmount",yv={bubbles:!1,cancelable:!0},XT="FocusScope",gv=w.forwardRef((t,a)=>{const{loop:s=!1,trapped:r=!1,onMountAutoFocus:o,onUnmountAutoFocus:c,...d}=t,[h,p]=w.useState(null),y=_i(o),v=_i(c),b=w.useRef(null),S=Di(a,C=>p(C)),E=w.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;w.useEffect(()=>{if(r){let C=function(H){if(E.paused||!h)return;const W=H.target;h.contains(W)?b.current=W:za(b.current,{select:!0})},R=function(H){if(E.paused||!h)return;const W=H.relatedTarget;W!==null&&(h.contains(W)||za(b.current,{select:!0}))},z=function(H){if(document.activeElement===document.body)for(const O of H)O.removedNodes.length>0&&za(h)};document.addEventListener("focusin",C),document.addEventListener("focusout",R);const B=new MutationObserver(z);return h&&B.observe(h,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",C),document.removeEventListener("focusout",R),B.disconnect()}}},[r,h,E.paused]),w.useEffect(()=>{if(h){xv.add(E);const C=document.activeElement;if(!h.contains(C)){const z=new CustomEvent(od,yv);h.addEventListener(od,y),h.dispatchEvent(z),z.defaultPrevented||(KT(IT(vv(h)),{select:!0}),document.activeElement===C&&za(h))}return()=>{h.removeEventListener(od,y),setTimeout(()=>{const z=new CustomEvent(ud,yv);h.addEventListener(ud,v),h.dispatchEvent(z),z.defaultPrevented||za(C??document.body,{select:!0}),h.removeEventListener(ud,v),xv.remove(E)},0)}}},[h,y,v,E]);const N=w.useCallback(C=>{if(!s&&!r||E.paused)return;const R=C.key==="Tab"&&!C.altKey&&!C.ctrlKey&&!C.metaKey,z=document.activeElement;if(R&&z){const B=C.currentTarget,[H,W]=ZT(B);H&&W?!C.shiftKey&&z===W?(C.preventDefault(),s&&za(H,{select:!0})):C.shiftKey&&z===H&&(C.preventDefault(),s&&za(W,{select:!0})):z===B&&C.preventDefault()}},[s,r,E.paused]);return m.jsx(aa.div,{tabIndex:-1,...d,ref:S,onKeyDown:N})});gv.displayName=XT;function KT(t,{select:a=!1}={}){const s=document.activeElement;for(const r of t)if(za(r,{select:a}),document.activeElement!==s)return}function ZT(t){const a=vv(t),s=bv(a,t),r=bv(a.reverse(),t);return[s,r]}function vv(t){const a=[],s=document.createTreeWalker(t,NodeFilter.SHOW_ELEMENT,{acceptNode:r=>{const o=r.tagName==="INPUT"&&r.type==="hidden";return r.disabled||r.hidden||o?NodeFilter.FILTER_SKIP:r.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;s.nextNode();)a.push(s.currentNode);return a}function bv(t,a){for(const s of t)if(!WT(s,{upTo:a}))return s}function WT(t,{upTo:a}){if(getComputedStyle(t).visibility==="hidden")return!0;for(;t;){if(a!==void 0&&t===a)return!1;if(getComputedStyle(t).display==="none")return!0;t=t.parentElement}return!1}function $T(t){return t instanceof HTMLInputElement&&"select"in t}function za(t,{select:a=!1}={}){if(t&&t.focus){const s=document.activeElement;t.focus({preventScroll:!0}),t!==s&&$T(t)&&a&&t.select()}}var xv=JT();function JT(){let t=[];return{add(a){const s=t[0];a!==s&&(s==null||s.pause()),t=Sv(t,a),t.unshift(a)},remove(a){var s;t=Sv(t,a),(s=t[0])==null||s.resume()}}}function Sv(t,a){const s=[...t],r=s.indexOf(a);return r!==-1&&s.splice(r,1),s}function IT(t){return t.filter(a=>a.tagName!=="A")}var eC="Portal",wv=w.forwardRef((t,a)=>{var h;const{container:s,...r}=t,[o,c]=w.useState(!1);Ho(()=>c(!0),[]);const d=s||o&&((h=globalThis==null?void 0:globalThis.document)==null?void 0:h.body);return d?RT.createPortal(m.jsx(aa.div,{...r,ref:a}),d):null});wv.displayName=eC;function tC(t,a){return w.useReducer((s,r)=>a[s][r]??s,t)}var Po=t=>{const{present:a,children:s}=t,r=nC(a),o=typeof s=="function"?s({present:r.isPresent}):w.Children.only(s),c=Di(r.ref,aC(o));return typeof s=="function"||r.isPresent?w.cloneElement(o,{ref:c}):null};Po.displayName="Presence";function nC(t){const[a,s]=w.useState(),r=w.useRef({}),o=w.useRef(t),c=w.useRef("none"),d=t?"mounted":"unmounted",[h,p]=tC(d,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return w.useEffect(()=>{const y=qo(r.current);c.current=h==="mounted"?y:"none"},[h]),Ho(()=>{const y=r.current,v=o.current;if(v!==t){const S=c.current,E=qo(y);t?p("MOUNT"):E==="none"||(y==null?void 0:y.display)==="none"?p("UNMOUNT"):p(v&&S!==E?"ANIMATION_OUT":"UNMOUNT"),o.current=t}},[t,p]),Ho(()=>{if(a){let y;const v=a.ownerDocument.defaultView??window,b=E=>{const C=qo(r.current).includes(E.animationName);if(E.target===a&&C&&(p("ANIMATION_END"),!o.current)){const R=a.style.animationFillMode;a.style.animationFillMode="forwards",y=v.setTimeout(()=>{a.style.animationFillMode==="forwards"&&(a.style.animationFillMode=R)})}},S=E=>{E.target===a&&(c.current=qo(r.current))};return a.addEventListener("animationstart",S),a.addEventListener("animationcancel",b),a.addEventListener("animationend",b),()=>{v.clearTimeout(y),a.removeEventListener("animationstart",S),a.removeEventListener("animationcancel",b),a.removeEventListener("animationend",b)}}else p("ANIMATION_END")},[a,p]),{isPresent:["mounted","unmountSuspended"].includes(h),ref:w.useCallback(y=>{y&&(r.current=getComputedStyle(y)),s(y)},[])}}function qo(t){return(t==null?void 0:t.animationName)||"none"}function aC(t){var r,o;let a=(r=Object.getOwnPropertyDescriptor(t.props,"ref"))==null?void 0:r.get,s=a&&"isReactWarning"in a&&a.isReactWarning;return s?t.ref:(a=(o=Object.getOwnPropertyDescriptor(t,"ref"))==null?void 0:o.get,s=a&&"isReactWarning"in a&&a.isReactWarning,s?t.props.ref:t.props.ref||t.ref)}var cd=0;function iC(){w.useEffect(()=>{const t=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",t[0]??Ev()),document.body.insertAdjacentElement("beforeend",t[1]??Ev()),cd++,()=>{cd===1&&document.querySelectorAll("[data-radix-focus-guard]").forEach(a=>a.remove()),cd--}},[])}function Ev(){const t=document.createElement("span");return t.setAttribute("data-radix-focus-guard",""),t.tabIndex=0,t.style.outline="none",t.style.opacity="0",t.style.position="fixed",t.style.pointerEvents="none",t}var Pn=function(){return Pn=Object.assign||function(a){for(var s,r=1,o=arguments.length;r<o;r++){s=arguments[r];for(var c in s)Object.prototype.hasOwnProperty.call(s,c)&&(a[c]=s[c])}return a},Pn.apply(this,arguments)};function Tv(t,a){var s={};for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&a.indexOf(r)<0&&(s[r]=t[r]);if(t!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(t);o<r.length;o++)a.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(t,r[o])&&(s[r[o]]=t[r[o]]);return s}function sC(t,a,s){if(s||arguments.length===2)for(var r=0,o=a.length,c;r<o;r++)(c||!(r in a))&&(c||(c=Array.prototype.slice.call(a,0,r)),c[r]=a[r]);return t.concat(c||Array.prototype.slice.call(a))}typeof SuppressedError=="function"&&SuppressedError;var Fo="right-scroll-bar-position",Go="width-before-scroll-bar",rC="with-scroll-bars-hidden",lC="--removed-body-scroll-bar-size";function fd(t,a){return typeof t=="function"?t(a):t&&(t.current=a),t}function oC(t,a){var s=w.useState(function(){return{value:t,callback:a,facade:{get current(){return s.value},set current(r){var o=s.value;o!==r&&(s.value=r,s.callback(r,o))}}}})[0];return s.callback=a,s.facade}var uC=typeof window<"u"?w.useLayoutEffect:w.useEffect,Cv=new WeakMap;function cC(t,a){var s=oC(null,function(r){return t.forEach(function(o){return fd(o,r)})});return uC(function(){var r=Cv.get(s);if(r){var o=new Set(r),c=new Set(t),d=s.current;o.forEach(function(h){c.has(h)||fd(h,null)}),c.forEach(function(h){o.has(h)||fd(h,d)})}Cv.set(s,t)},[t]),s}function fC(t){return t}function dC(t,a){a===void 0&&(a=fC);var s=[],r=!1,o={read:function(){if(r)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return s.length?s[s.length-1]:t},useMedium:function(c){var d=a(c,r);return s.push(d),function(){s=s.filter(function(h){return h!==d})}},assignSyncMedium:function(c){for(r=!0;s.length;){var d=s;s=[],d.forEach(c)}s={push:function(h){return c(h)},filter:function(){return s}}},assignMedium:function(c){r=!0;var d=[];if(s.length){var h=s;s=[],h.forEach(c),d=s}var p=function(){var v=d;d=[],v.forEach(c)},y=function(){return Promise.resolve().then(p)};y(),s={push:function(v){d.push(v),y()},filter:function(v){return d=d.filter(v),s}}}};return o}function hC(t){t===void 0&&(t={});var a=dC(null);return a.options=Pn({async:!0,ssr:!1},t),a}var jv=function(t){var a=t.sideCar,s=Tv(t,["sideCar"]);if(!a)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var r=a.read();if(!r)throw new Error("Sidecar medium not found");return w.createElement(r,Pn({},s))};jv.isSideCarExport=!0;function mC(t,a){return t.useMedium(a),jv}var Av=hC(),dd=function(){},Yo=w.forwardRef(function(t,a){var s=w.useRef(null),r=w.useState({onScrollCapture:dd,onWheelCapture:dd,onTouchMoveCapture:dd}),o=r[0],c=r[1],d=t.forwardProps,h=t.children,p=t.className,y=t.removeScrollBar,v=t.enabled,b=t.shards,S=t.sideCar,E=t.noIsolation,N=t.inert,C=t.allowPinchZoom,R=t.as,z=R===void 0?"div":R,B=t.gapMode,H=Tv(t,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as","gapMode"]),W=S,O=cC([s,a]),X=Pn(Pn({},H),o);return w.createElement(w.Fragment,null,v&&w.createElement(W,{sideCar:Av,removeScrollBar:y,shards:b,noIsolation:E,inert:N,setCallbacks:c,allowPinchZoom:!!C,lockRef:s,gapMode:B}),d?w.cloneElement(w.Children.only(h),Pn(Pn({},X),{ref:O})):w.createElement(z,Pn({},X,{className:p,ref:O}),h))});Yo.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},Yo.classNames={fullWidth:Go,zeroRight:Fo};var pC=function(){if(typeof __webpack_nonce__<"u")return __webpack_nonce__};function yC(){if(!document)return null;var t=document.createElement("style");t.type="text/css";var a=pC();return a&&t.setAttribute("nonce",a),t}function gC(t,a){t.styleSheet?t.styleSheet.cssText=a:t.appendChild(document.createTextNode(a))}function vC(t){var a=document.head||document.getElementsByTagName("head")[0];a.appendChild(t)}var bC=function(){var t=0,a=null;return{add:function(s){t==0&&(a=yC())&&(gC(a,s),vC(a)),t++},remove:function(){t--,!t&&a&&(a.parentNode&&a.parentNode.removeChild(a),a=null)}}},xC=function(){var t=bC();return function(a,s){w.useEffect(function(){return t.add(a),function(){t.remove()}},[a&&s])}},Dv=function(){var t=xC(),a=function(s){var r=s.styles,o=s.dynamic;return t(r,o),null};return a},SC={left:0,top:0,right:0,gap:0},hd=function(t){return parseInt(t||"",10)||0},wC=function(t){var a=window.getComputedStyle(document.body),s=a[t==="padding"?"paddingLeft":"marginLeft"],r=a[t==="padding"?"paddingTop":"marginTop"],o=a[t==="padding"?"paddingRight":"marginRight"];return[hd(s),hd(r),hd(o)]},EC=function(t){if(t===void 0&&(t="margin"),typeof window>"u")return SC;var a=wC(t),s=document.documentElement.clientWidth,r=window.innerWidth;return{left:a[0],top:a[1],right:a[2],gap:Math.max(0,r-s+a[2]-a[0])}},TC=Dv(),Ts="data-scroll-locked",CC=function(t,a,s,r){var o=t.left,c=t.top,d=t.right,h=t.gap;return s===void 0&&(s="margin"),`
  .`.concat(rC,` {
   overflow: hidden `).concat(r,`;
   padding-right: `).concat(h,"px ").concat(r,`;
  }
  body[`).concat(Ts,`] {
    overflow: hidden `).concat(r,`;
    overscroll-behavior: contain;
    `).concat([a&&"position: relative ".concat(r,";"),s==="margin"&&`
    padding-left: `.concat(o,`px;
    padding-top: `).concat(c,`px;
    padding-right: `).concat(d,`px;
    margin-left:0;
    margin-top:0;
    margin-right: `).concat(h,"px ").concat(r,`;
    `),s==="padding"&&"padding-right: ".concat(h,"px ").concat(r,";")].filter(Boolean).join(""),`
  }
  
  .`).concat(Fo,` {
    right: `).concat(h,"px ").concat(r,`;
  }
  
  .`).concat(Go,` {
    margin-right: `).concat(h,"px ").concat(r,`;
  }
  
  .`).concat(Fo," .").concat(Fo,` {
    right: 0 `).concat(r,`;
  }
  
  .`).concat(Go," .").concat(Go,` {
    margin-right: 0 `).concat(r,`;
  }
  
  body[`).concat(Ts,`] {
    `).concat(lC,": ").concat(h,`px;
  }
`)},_v=function(){var t=parseInt(document.body.getAttribute(Ts)||"0",10);return isFinite(t)?t:0},jC=function(){w.useEffect(function(){return document.body.setAttribute(Ts,(_v()+1).toString()),function(){var t=_v()-1;t<=0?document.body.removeAttribute(Ts):document.body.setAttribute(Ts,t.toString())}},[])},AC=function(t){var a=t.noRelative,s=t.noImportant,r=t.gapMode,o=r===void 0?"margin":r;jC();var c=w.useMemo(function(){return EC(o)},[o]);return w.createElement(TC,{styles:CC(c,!a,o,s?"":"!important")})},md=!1;if(typeof window<"u")try{var Qo=Object.defineProperty({},"passive",{get:function(){return md=!0,!0}});window.addEventListener("test",Qo,Qo),window.removeEventListener("test",Qo,Qo)}catch{md=!1}var Cs=md?{passive:!1}:!1,DC=function(t){return t.tagName==="TEXTAREA"},Mv=function(t,a){if(!(t instanceof Element))return!1;var s=window.getComputedStyle(t);return s[a]!=="hidden"&&!(s.overflowY===s.overflowX&&!DC(t)&&s[a]==="visible")},_C=function(t){return Mv(t,"overflowY")},MC=function(t){return Mv(t,"overflowX")},Nv=function(t,a){var s=a.ownerDocument,r=a;do{typeof ShadowRoot<"u"&&r instanceof ShadowRoot&&(r=r.host);var o=Rv(t,r);if(o){var c=Ov(t,r),d=c[1],h=c[2];if(d>h)return!0}r=r.parentNode}while(r&&r!==s.body);return!1},NC=function(t){var a=t.scrollTop,s=t.scrollHeight,r=t.clientHeight;return[a,s,r]},RC=function(t){var a=t.scrollLeft,s=t.scrollWidth,r=t.clientWidth;return[a,s,r]},Rv=function(t,a){return t==="v"?_C(a):MC(a)},Ov=function(t,a){return t==="v"?NC(a):RC(a)},OC=function(t,a){return t==="h"&&a==="rtl"?-1:1},VC=function(t,a,s,r,o){var c=OC(t,window.getComputedStyle(a).direction),d=c*r,h=s.target,p=a.contains(h),y=!1,v=d>0,b=0,S=0;do{var E=Ov(t,h),N=E[0],C=E[1],R=E[2],z=C-R-c*N;(N||z)&&Rv(t,h)&&(b+=z,S+=N),h instanceof ShadowRoot?h=h.host:h=h.parentNode}while(!p&&h!==document.body||p&&(a.contains(h)||a===h));return(v&&Math.abs(b)<1||!v&&Math.abs(S)<1)&&(y=!0),y},Xo=function(t){return"changedTouches"in t?[t.changedTouches[0].clientX,t.changedTouches[0].clientY]:[0,0]},Vv=function(t){return[t.deltaX,t.deltaY]},zv=function(t){return t&&"current"in t?t.current:t},zC=function(t,a){return t[0]===a[0]&&t[1]===a[1]},kC=function(t){return`
  .block-interactivity-`.concat(t,` {pointer-events: none;}
  .allow-interactivity-`).concat(t,` {pointer-events: all;}
`)},LC=0,js=[];function UC(t){var a=w.useRef([]),s=w.useRef([0,0]),r=w.useRef(),o=w.useState(LC++)[0],c=w.useState(Dv)[0],d=w.useRef(t);w.useEffect(function(){d.current=t},[t]),w.useEffect(function(){if(t.inert){document.body.classList.add("block-interactivity-".concat(o));var C=sC([t.lockRef.current],(t.shards||[]).map(zv),!0).filter(Boolean);return C.forEach(function(R){return R.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),C.forEach(function(R){return R.classList.remove("allow-interactivity-".concat(o))})}}},[t.inert,t.lockRef.current,t.shards]);var h=w.useCallback(function(C,R){if("touches"in C&&C.touches.length===2||C.type==="wheel"&&C.ctrlKey)return!d.current.allowPinchZoom;var z=Xo(C),B=s.current,H="deltaX"in C?C.deltaX:B[0]-z[0],W="deltaY"in C?C.deltaY:B[1]-z[1],O,X=C.target,J=Math.abs(H)>Math.abs(W)?"h":"v";if("touches"in C&&J==="h"&&X.type==="range")return!1;var Z=Nv(J,X);if(!Z)return!0;if(Z?O=J:(O=J==="v"?"h":"v",Z=Nv(J,X)),!Z)return!1;if(!r.current&&"changedTouches"in C&&(H||W)&&(r.current=O),!O)return!0;var Y=r.current||O;return VC(Y,R,C,Y==="h"?H:W)},[]),p=w.useCallback(function(C){var R=C;if(!(!js.length||js[js.length-1]!==c)){var z="deltaY"in R?Vv(R):Xo(R),B=a.current.filter(function(O){return O.name===R.type&&(O.target===R.target||R.target===O.shadowParent)&&zC(O.delta,z)})[0];if(B&&B.should){R.cancelable&&R.preventDefault();return}if(!B){var H=(d.current.shards||[]).map(zv).filter(Boolean).filter(function(O){return O.contains(R.target)}),W=H.length>0?h(R,H[0]):!d.current.noIsolation;W&&R.cancelable&&R.preventDefault()}}},[]),y=w.useCallback(function(C,R,z,B){var H={name:C,delta:R,target:z,should:B,shadowParent:BC(z)};a.current.push(H),setTimeout(function(){a.current=a.current.filter(function(W){return W!==H})},1)},[]),v=w.useCallback(function(C){s.current=Xo(C),r.current=void 0},[]),b=w.useCallback(function(C){y(C.type,Vv(C),C.target,h(C,t.lockRef.current))},[]),S=w.useCallback(function(C){y(C.type,Xo(C),C.target,h(C,t.lockRef.current))},[]);w.useEffect(function(){return js.push(c),t.setCallbacks({onScrollCapture:b,onWheelCapture:b,onTouchMoveCapture:S}),document.addEventListener("wheel",p,Cs),document.addEventListener("touchmove",p,Cs),document.addEventListener("touchstart",v,Cs),function(){js=js.filter(function(C){return C!==c}),document.removeEventListener("wheel",p,Cs),document.removeEventListener("touchmove",p,Cs),document.removeEventListener("touchstart",v,Cs)}},[]);var E=t.removeScrollBar,N=t.inert;return w.createElement(w.Fragment,null,N?w.createElement(c,{styles:kC(o)}):null,E?w.createElement(AC,{gapMode:t.gapMode}):null)}function BC(t){for(var a=null;t!==null;)t instanceof ShadowRoot&&(a=t.host,t=t.host),t=t.parentNode;return a}const HC=mC(Av,UC);var kv=w.forwardRef(function(t,a){return w.createElement(Yo,Pn({},t,{ref:a,sideCar:HC}))});kv.classNames=Yo.classNames;var PC=function(t){if(typeof document>"u")return null;var a=Array.isArray(t)?t[0]:t;return a.ownerDocument.body},As=new WeakMap,Ko=new WeakMap,Zo={},pd=0,Lv=function(t){return t&&(t.host||Lv(t.parentNode))},qC=function(t,a){return a.map(function(s){if(t.contains(s))return s;var r=Lv(s);return r&&t.contains(r)?r:(console.error("aria-hidden",s,"in not contained inside",t,". Doing nothing"),null)}).filter(function(s){return!!s})},FC=function(t,a,s,r){var o=qC(a,Array.isArray(t)?t:[t]);Zo[s]||(Zo[s]=new WeakMap);var c=Zo[s],d=[],h=new Set,p=new Set(o),y=function(b){!b||h.has(b)||(h.add(b),y(b.parentNode))};o.forEach(y);var v=function(b){!b||p.has(b)||Array.prototype.forEach.call(b.children,function(S){if(h.has(S))v(S);else try{var E=S.getAttribute(r),N=E!==null&&E!=="false",C=(As.get(S)||0)+1,R=(c.get(S)||0)+1;As.set(S,C),c.set(S,R),d.push(S),C===1&&N&&Ko.set(S,!0),R===1&&S.setAttribute(s,"true"),N||S.setAttribute(r,"true")}catch(z){console.error("aria-hidden: cannot operate on ",S,z)}})};return v(a),h.clear(),pd++,function(){d.forEach(function(b){var S=As.get(b)-1,E=c.get(b)-1;As.set(b,S),c.set(b,E),S||(Ko.has(b)||b.removeAttribute(r),Ko.delete(b)),E||b.removeAttribute(s)}),pd--,pd||(As=new WeakMap,As=new WeakMap,Ko=new WeakMap,Zo={})}},GC=function(t,a,s){s===void 0&&(s="data-aria-hidden");var r=Array.from(Array.isArray(t)?t:[t]),o=PC(t);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live]"))),FC(r,o,s,"aria-hidden")):function(){return null}},yd="Dialog",[Uv,Z9]=jT(yd),[YC,Dn]=Uv(yd),Bv=t=>{const{__scopeDialog:a,children:s,open:r,defaultOpen:o,onOpenChange:c,modal:d=!0}=t,h=w.useRef(null),p=w.useRef(null),[y=!1,v]=MT({prop:r,defaultProp:o,onChange:c});return m.jsx(YC,{scope:a,triggerRef:h,contentRef:p,contentId:id(),titleId:id(),descriptionId:id(),open:y,onOpenChange:v,onOpenToggle:w.useCallback(()=>v(b=>!b),[v]),modal:d,children:s})};Bv.displayName=yd;var Hv="DialogTrigger",QC=w.forwardRef((t,a)=>{const{__scopeDialog:s,...r}=t,o=Dn(Hv,s),c=Di(a,o.triggerRef);return m.jsx(aa.button,{type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":bd(o.open),...r,ref:c,onClick:Va(t.onClick,o.onOpenToggle)})});QC.displayName=Hv;var gd="DialogPortal",[XC,Pv]=Uv(gd,{forceMount:void 0}),qv=t=>{const{__scopeDialog:a,forceMount:s,children:r,container:o}=t,c=Dn(gd,a);return m.jsx(XC,{scope:a,forceMount:s,children:w.Children.map(r,d=>m.jsx(Po,{present:s||c.open,children:m.jsx(wv,{asChild:!0,container:o,children:d})}))})};qv.displayName=gd;var Wo="DialogOverlay",Fv=w.forwardRef((t,a)=>{const s=Pv(Wo,t.__scopeDialog),{forceMount:r=s.forceMount,...o}=t,c=Dn(Wo,t.__scopeDialog);return c.modal?m.jsx(Po,{present:r||c.open,children:m.jsx(KC,{...o,ref:a})}):null});Fv.displayName=Wo;var KC=w.forwardRef((t,a)=>{const{__scopeDialog:s,...r}=t,o=Dn(Wo,s);return m.jsx(kv,{as:sd,allowPinchZoom:!0,shards:[o.contentRef],children:m.jsx(aa.div,{"data-state":bd(o.open),...r,ref:a,style:{pointerEvents:"auto",...r.style}})})}),Mi="DialogContent",Gv=w.forwardRef((t,a)=>{const s=Pv(Mi,t.__scopeDialog),{forceMount:r=s.forceMount,...o}=t,c=Dn(Mi,t.__scopeDialog);return m.jsx(Po,{present:r||c.open,children:c.modal?m.jsx(ZC,{...o,ref:a}):m.jsx(WC,{...o,ref:a})})});Gv.displayName=Mi;var ZC=w.forwardRef((t,a)=>{const s=Dn(Mi,t.__scopeDialog),r=w.useRef(null),o=Di(a,s.contentRef,r);return w.useEffect(()=>{const c=r.current;if(c)return GC(c)},[]),m.jsx(Yv,{...t,ref:o,trapFocus:s.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:Va(t.onCloseAutoFocus,c=>{var d;c.preventDefault(),(d=s.triggerRef.current)==null||d.focus()}),onPointerDownOutside:Va(t.onPointerDownOutside,c=>{const d=c.detail.originalEvent,h=d.button===0&&d.ctrlKey===!0;(d.button===2||h)&&c.preventDefault()}),onFocusOutside:Va(t.onFocusOutside,c=>c.preventDefault())})}),WC=w.forwardRef((t,a)=>{const s=Dn(Mi,t.__scopeDialog),r=w.useRef(!1),o=w.useRef(!1);return m.jsx(Yv,{...t,ref:a,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:c=>{var d,h;(d=t.onCloseAutoFocus)==null||d.call(t,c),c.defaultPrevented||(r.current||(h=s.triggerRef.current)==null||h.focus(),c.preventDefault()),r.current=!1,o.current=!1},onInteractOutside:c=>{var p,y;(p=t.onInteractOutside)==null||p.call(t,c),c.defaultPrevented||(r.current=!0,c.detail.originalEvent.type==="pointerdown"&&(o.current=!0));const d=c.target;((y=s.triggerRef.current)==null?void 0:y.contains(d))&&c.preventDefault(),c.detail.originalEvent.type==="focusin"&&o.current&&c.preventDefault()}})}),Yv=w.forwardRef((t,a)=>{const{__scopeDialog:s,trapFocus:r,onOpenAutoFocus:o,onCloseAutoFocus:c,...d}=t,h=Dn(Mi,s),p=w.useRef(null),y=Di(a,p);return iC(),m.jsxs(m.Fragment,{children:[m.jsx(gv,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:o,onUnmountAutoFocus:c,children:m.jsx(hv,{role:"dialog",id:h.contentId,"aria-describedby":h.descriptionId,"aria-labelledby":h.titleId,"data-state":bd(h.open),...d,ref:y,onDismiss:()=>h.onOpenChange(!1)})}),m.jsxs(m.Fragment,{children:[m.jsx($C,{titleId:h.titleId}),m.jsx(IC,{contentRef:p,descriptionId:h.descriptionId})]})]})}),vd="DialogTitle",Qv=w.forwardRef((t,a)=>{const{__scopeDialog:s,...r}=t,o=Dn(vd,s);return m.jsx(aa.h2,{id:o.titleId,...r,ref:a})});Qv.displayName=vd;var Xv="DialogDescription",Kv=w.forwardRef((t,a)=>{const{__scopeDialog:s,...r}=t,o=Dn(Xv,s);return m.jsx(aa.p,{id:o.descriptionId,...r,ref:a})});Kv.displayName=Xv;var Zv="DialogClose",Wv=w.forwardRef((t,a)=>{const{__scopeDialog:s,...r}=t,o=Dn(Zv,s);return m.jsx(aa.button,{type:"button",...r,ref:a,onClick:Va(t.onClick,()=>o.onOpenChange(!1))})});Wv.displayName=Zv;function bd(t){return t?"open":"closed"}var $v="DialogTitleWarning",[W9,Jv]=CT($v,{contentName:Mi,titleName:vd,docsSlug:"dialog"}),$C=({titleId:t})=>{const a=Jv($v),s=`\`${a.contentName}\` requires a \`${a.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${a.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${a.docsSlug}`;return w.useEffect(()=>{t&&(document.getElementById(t)||console.error(s))},[s,t]),null},JC="DialogDescriptionWarning",IC=({contentRef:t,descriptionId:a})=>{const r=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${Jv(JC).contentName}}.`;return w.useEffect(()=>{var c;const o=(c=t.current)==null?void 0:c.getAttribute("aria-describedby");a&&o&&(document.getElementById(a)||console.warn(r))},[r,t,a]),null},e8=Bv,t8=qv,n8=Fv,a8=Gv,i8=Qv,s8=Kv,r8=Wv;function l8({open:t,onClose:a,title:s,description:r,children:o,onSave:c,onCancel:d}){return m.jsx(e8,{open:t,onOpenChange:h=>!h&&a(),children:m.jsx(U0,{children:t&&m.jsxs(t8,{forceMount:!0,children:[m.jsx(n8,{asChild:!0,children:m.jsx(Do.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 bg-black/50"})}),m.jsx(a8,{asChild:!0,children:m.jsx("div",{className:"fixed inset-0 flex items-center justify-center",onClick:a,children:m.jsxs(Do.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:20},className:"bg-white rounded-xl shadow-lg p-6 w-[500px] max-w-[90vw] max-h-[85vh] overflow-y-auto",onClick:h=>h.stopPropagation(),children:[m.jsxs("div",{className:"flex justify-between items-start mb-4",children:[m.jsxs("div",{children:[m.jsx(i8,{className:"text-lg font-medium text-gray-900",children:s}),r&&m.jsx(s8,{className:"text-sm text-gray-500 mt-1",children:r})]}),m.jsx(r8,{asChild:!0,children:m.jsx("button",{className:"text-gray-400 hover:text-gray-500","aria-label":"Close",onClick:a,children:m.jsx(uT,{className:"size-5"})})})]}),o,m.jsxs("div",{className:"flex justify-end gap-2 mt-6",children:[m.jsx(ut,{variant:"default",onClick:d,children:"Cancel"}),m.jsx(ut,{variant:"primary",onClick:c,children:"Save changes"})]})]})})})]})})})}function o8({name:t,id:a,placeholder:s,value:r,onChange:o,className:c="",...d}){return m.jsx("textarea",{name:t,id:a,placeholder:s,value:r,onChange:h=>o(h.target.value),className:`block w-full rounded-md bg-white px-3 py-1.5 text-base text-gray-900 outline outline-1 -outline-offset-1 outline-gray-300 placeholder:text-gray-400 focus:outline focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 focus:!border-none !border-none !shadow-none sm:text-sm/6 ${c}`,...d})}function ka({open:t,onClose:a,initialValue:s=[],onSave:r,title:o,description:c}){const[d,h]=w.useState(s.join(`
`)),p=()=>{const y=d.split(`
`).map(v=>v.trim()).filter(v=>v.length>0);r(y),a()};return m.jsx(l8,{open:t,onClose:a,title:o,description:c,onSave:p,onCancel:a,children:m.jsx(o8,{value:d,onChange:h,className:"h-48",placeholder:"Enter keywords or patterns (one per line)..."})})}function el({className:t="",options:a,name:s}){const{register:r}=fn();return m.jsx("div",{className:`flex flex-wrap gap-2 ${t}`,children:a.map(o=>m.jsxs("label",{className:"relative inline-flex items-center cursor-pointer",children:[m.jsx("input",{type:"radio",className:"peer sr-only",value:o.value,...r(s)}),m.jsx("div",{className:"flex items-center justify-center gap-2 rounded-lg font-medium shadow-xs transition-all focus:outline-none hover:cursor-pointer whitespace-nowrap px-4 py-2 text-sm peer-checked:bg-indigo-600 peer-checked:text-white peer-checked:ring-2 peer-checked:ring-indigo-600 peer-checked:ring-inset text-gray-600 ring-1 ring-gray-300 ring-inset",children:o.label})]},o.value))})}function u8(){const{watch:t,setValue:a}=fn(),s=t("cache_refresh"),r=t("cache_bypass_urls"),o=t("cache_include_queries"),c=t("cache_bypass_cookies"),[d,h]=w.useState(null);return m.jsxs("section",{className:"space-y-6",children:[m.jsxs("div",{className:"bg-white shadow-xs border border-gray-200 rounded-xl p-6 space-y-4",children:[m.jsxs("h3",{className:"font-medium text-base text-gray-900",children:[m.jsx(nv,{className:"size-6 inline-block mr-2 mb-1 text-indigo-600"}),"Basic Caching"]}),m.jsxs("div",{className:"flex justify-between gap-2",children:[m.jsxs("div",{children:[m.jsx("span",{className:"font-normal text-base text-gray-900",children:"Preload Links on Hover"}),m.jsx("p",{className:"text-sm text-gray-500 mt-1",children:"Starts loading pages when users hover over links for faster navigation"})]}),m.jsx(Oe,{name:"cache_link_prefetch"})]}),m.jsx("div",{className:"border-t border-gray-200"}),m.jsxs("div",{className:"flex justify-between gap-2",children:[m.jsxs("div",{children:[m.jsx("span",{className:"font-normal text-base text-gray-900",children:"Separate Mobile Cache & Optimizations"}),m.jsx("p",{className:"text-sm text-gray-500 mt-1",children:"Creates separate versions for mobile with their own cache and optimizations"})]}),m.jsx(Oe,{name:"cache_mobile"})]}),m.jsx("div",{className:"border-t border-gray-200"}),m.jsxs("div",{className:"flex justify-between gap-2",children:[m.jsxs("div",{children:[m.jsx("span",{className:"font-normal text-base text-gray-900",children:"Cache for Logged-in Users by Role"}),m.jsx("p",{className:"text-sm text-gray-500 mt-1",children:"Maintains different cache versions for users based on their WordPress roles"})]}),m.jsx(Oe,{name:"cache_logged_in"})]}),m.jsx("div",{className:"border-t border-gray-200"}),m.jsxs("div",{className:"flex flex-col gap-2",children:[m.jsxs("div",{className:"flex justify-between gap-2",children:[m.jsxs("div",{children:[m.jsx("span",{className:"font-normal text-base text-gray-900",children:"Auto-refresh Cache"}),m.jsx("p",{className:"text-sm text-gray-500 mt-1",children:"Automatically clears and rebuilds cache at set intervals to keep content fresh"})]}),m.jsx(Oe,{name:"cache_refresh"})]}),s&&m.jsx(el,{name:"cache_refresh_interval",options:[{label:"2 hours",value:"2hours"},{label:"6 hours",value:"6hours"},{label:"12 hours",value:"twicedaily"},{label:"24 hours",value:"daily"}]})]})]}),m.jsxs("div",{className:"bg-white shadow-xs border border-gray-200 rounded-xl p-6 space-y-4",children:[m.jsxs("h3",{className:"font-medium text-base text-gray-900",children:[m.jsx($E,{className:"size-6 inline-block mr-2 mb-1 text-indigo-600"}),"Advanced Caching"]}),m.jsxs("div",{className:"flex items-start justify-between gap-2",children:[m.jsxs("div",{className:"flex-1",children:[m.jsx("span",{className:"font-normal text-base text-gray-900",children:"Exclude Pages from Cache"}),m.jsx("p",{className:"text-sm text-gray-500 mt-1",children:"Prevents specific pages from being cached when they need real-time content"})]}),m.jsxs(ut,{onClick:()=>h("page_exclusions"),className:"shrink-0",children:["Edit exclusions"," ",!!r.length&&`(${r.length})`]})]}),m.jsx("div",{className:"border-t border-gray-200"}),m.jsxs("div",{className:"flex items-start justify-between gap-2",children:[m.jsxs("div",{className:"flex-1",children:[m.jsx("span",{className:"font-normal text-base text-gray-900",children:"Separate Cache for Query Parameters"}),m.jsx("p",{className:"text-sm text-gray-500 mt-1",children:"Specify which query parameters should create unique cache versions"})]}),m.jsxs(ut,{onClick:()=>h("query_strings"),className:"shrink-0",children:["Edit parameters"," ",!!o.length&&`(${o.length})`]})]}),m.jsx("div",{className:"border-t border-gray-200"}),m.jsxs("div",{className:"flex items-start justify-between gap-2",children:[m.jsxs("div",{className:"flex-1",children:[m.jsx("span",{className:"font-normal text-base text-gray-900",children:"Bypass Cache for Cookies"}),m.jsx("p",{className:"text-sm text-gray-500 mt-1",children:"Skips cache when specific cookies are present in the request"})]}),m.jsxs(ut,{onClick:()=>h("cookie_bypass"),className:"shrink-0",children:["Edit exclusions"," ",!!c.length&&`(${c.length})`]})]})]}),m.jsx(ka,{open:d==="page_exclusions",onClose:()=>h(null),initialValue:r??[],onSave:p=>{a("cache_bypass_urls",p,{shouldDirty:!0}),h(null)},title:"Page Exclusions",description:"Enter URLs or patterns of pages to exclude from caching"}),m.jsx(ka,{open:d==="query_strings",onClose:()=>h(null),initialValue:o??[],onSave:p=>{a("cache_include_queries",p,{shouldDirty:!0}),h(null)},title:"Query Strings to Cache",description:"Enter query parameters that should create unique cache versions"}),m.jsx(ka,{open:d==="cookie_bypass",onClose:()=>h(null),initialValue:c??[],onSave:p=>{a("cache_bypass_cookies",p,{shouldDirty:!0}),h(null)},title:"Cookie Bypass Rules",description:"Enter cookie names that should trigger cache bypass"})]})}function $o({type:t="text",name:a,id:s,placeholder:r,className:o="",...c}){const{register:d}=fn();return m.jsx("input",{type:t,id:s,placeholder:r,className:`block rounded-md bg-white !px-3 !py-1 text-base text-gray-900 outline outline-1 -outline-offset-1 outline-gray-300 placeholder:text-gray-400 focus:!outline focus:!outline-2 focus:!-outline-offset-2 focus:!outline-indigo-600 sm:text-sm/6 focus:!border-none !border-none !shadow-none ${o}`,...d(a),...c})}function c8(){const{watch:t}=fn(),a=t("cdn"),s=t("cdn_type");return m.jsxs("section",{className:"space-y-6",children:[m.jsx(f8,{}),m.jsxs("div",{className:"bg-white shadow-xs border border-gray-200 rounded-xl p-6 space-y-4",children:[m.jsxs("h3",{className:"font-medium text-base text-gray-900",children:[m.jsx(Bo,{className:"size-6 inline-block mr-2 mb-1 text-indigo-600"}),"Content Delivery Network"]}),m.jsx("div",{className:"flex flex-col gap-2",children:m.jsxs("div",{className:"flex justify-between gap-2",children:[m.jsxs("div",{children:[m.jsx("span",{className:"font-normal text-base text-gray-800",children:"Enable CDN"}),m.jsx("p",{className:"text-sm text-gray-500 mt-1",children:"Distribute your content through a global network of servers for faster delivery"})]}),m.jsx(Oe,{name:"cdn"})]})}),a&&m.jsxs(m.Fragment,{children:[m.jsx("div",{className:"border-t border-gray-200"}),m.jsxs("div",{className:"flex flex-col gap-2",children:[m.jsxs("div",{children:[m.jsx("span",{className:"font-normal text-base text-gray-900",children:"CDN Provider"}),m.jsx("p",{className:"text-sm text-gray-500 mt-1",children:"Choose your preferred CDN service provider"})]}),m.jsx(el,{name:"cdn_type",options:[{label:"Custom URL",value:"custom"},{label:"FlyingCDN",value:"flying_cdn"},{label:"Cloudflare",value:"cloudflare"}]})]}),s==="custom"&&m.jsxs(m.Fragment,{children:[m.jsx("div",{className:"border-t border-gray-200"}),m.jsxs("div",{className:"flex flex-col gap-2",children:[m.jsxs("div",{children:[m.jsx("span",{className:"font-normal text-base text-gray-900",children:"File Types"}),m.jsx("p",{className:"text-sm text-gray-500 mt-1",children:"Select which types of files to serve through the CDN"})]}),m.jsx(el,{name:"cdn_file_types",options:[{label:"All Files",value:"all"},{label:"CSS, JavaScript & Fonts",value:"css_js_font"},{label:"Images",value:"image"}]})]}),m.jsx("div",{className:"border-t border-gray-200"}),m.jsxs("div",{className:"flex flex-col gap-2",children:[m.jsxs("div",{children:[m.jsx("span",{className:"font-normal text-base text-gray-900",children:"CDN URL"}),m.jsx("p",{className:"text-sm text-gray-500 mt-1",children:"Enter the URL of your CDN endpoint where content will be served from"})]}),m.jsx($o,{type:"url",name:"cdn_url",className:"w-80",placeholder:"https://cdn.yourdomain.com"})]})]}),s==="flying_cdn"&&m.jsxs(m.Fragment,{children:[m.jsx("div",{className:"border-t border-gray-200"}),m.jsxs("div",{className:"flex flex-col gap-2",children:[m.jsxs("div",{children:[m.jsx("span",{className:"font-normal text-base text-gray-900",children:"FlyingCDN API Key"}),m.jsx("p",{className:"text-sm text-gray-500 mt-1",children:"Enter your FlyingCDN API key to enable the service"})]}),m.jsx($o,{name:"flying_cdn_api_key",className:"w-80",type:"password",placeholder:"Enter your API key"})]})]}),s==="cloudflare"&&m.jsx(m.Fragment,{children:m.jsx("p",{className:"text-sm text-gray-500",children:"No additional configuration needed"})})]})]})]})}function f8(){const{watch:t}=fn(),a=t("cdn"),s=t("cdn_type"),r=t("flying_cdn_api_key");if(a&&s==="flying_cdn"&&r)return null;const o=["Powered by Cloudflare Enterprise","Handle traffic spikes with ease","Cache pages and static files in the cloud","Reduce server load and bandwidth usage","Image compression and WebP delivery","Firewall and DDoS protection"];return m.jsx("div",{className:"bg-white shadow-xs border border-gray-200 rounded-xl p-6 space-y-4",children:m.jsxs("div",{className:"flex flex-col lg:flex-row gap-4 items-start lg:items-center",children:[m.jsxs("div",{children:[m.jsxs("div",{className:"flex justify-start items-start gap-4",children:[m.jsx("div",{className:"bg-indigo-50 p-2 rounded-lg",children:m.jsx(Bo,{className:"text-indigo-600 size-6"})}),m.jsxs("div",{children:[m.jsx("h3",{className:"font-normal text-lg text-gray-900",children:"FlyingCDN — The Next-Gen CDN for WordPress"}),m.jsx("p",{className:"text-gray-500 font-normal text-base",children:"Delivers pages from the cloud, optimizes images, reduces server load, and enhances security"})]})]}),m.jsx("div",{className:"border-t border-gray-200 my-4"}),m.jsx("ul",{className:"grid grid-cols-1 md:grid-cols-2 gap-2 text-gray-700 text-base",children:o.map((c,d)=>m.jsxs("li",{className:"flex items-start",children:[m.jsx(XE,{className:"mr-2 text-indigo-600 size-6 flex-shrink-0"}),m.jsx("span",{children:c})]},d))})]}),m.jsxs("div",{className:"grow flex flex-row lg:flex-col items-center justify-center gap-2",children:[m.jsx("a",{href:"https://flyingcdn.com",target:"_blank",rel:"noreferrer",children:m.jsx(ut,{variant:"primary",children:"Try FlyingCDN"})}),m.jsxs("p",{className:"text-gray-900",children:[m.jsx("span",{className:"text-2xl",children:"$5"})," / month"]})]})]})})}function Iv({value:t}){const[a,s]=w.useState(0);return w.useEffect(()=>{let r=a,o=t,c=500,d=performance.now();const h=p=>{let y=p-d,v=Math.min(y/c,1),b=Math.floor(r+(o-r)*v);s(b),v<1&&requestAnimationFrame(h)};requestAnimationFrame(h)},[t]),m.jsx(m.Fragment,{children:a.toLocaleString()})}function e1(){return m.jsxs("section",{className:"space-y-6",children:[m.jsx(d8,{}),m.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[m.jsx(h8,{}),m.jsx(m8,{}),m.jsx(p8,{})]}),m.jsx(y8,{}),m.jsx(g8,{})]})}function d8(){var d,h;const{isError:t}=vT(),{register:a,handleSubmit:s}=ed(),{mutate:r,isPending:o,isSuccess:c}=lv();return!t&&((h=(d=window==null?void 0:window.flying_press)==null?void 0:d.config)!=null&&h.license_key)||c?null:m.jsx("div",{className:"bg-white shadow-xs border border-gray-200 rounded-xl p-6",children:m.jsxs("div",{className:"flex flex-col md:flex-row items-start md:items-center gap-4",children:[m.jsx("div",{className:"bg-red-50 p-2 rounded-lg",children:m.jsx(av,{className:"text-red-600 size-6"})}),m.jsxs("div",{className:"grow",children:[m.jsx("h2",{className:"text-lg font-base text-gray-900",children:"FlyingPress is Not Activated"}),m.jsx("p",{className:"text-base text-gray-500",children:"License activation is required to enable cloud optimization and receive plugin updates"})]}),m.jsxs("form",{onSubmit:s(p=>r(p.license_key)),className:"flex gap-3 w-full md:w-auto",children:[m.jsx($o,{...a("license_key"),required:!0,placeholder:"Enter license key",className:"flex-1 md:w-64",disabled:o}),m.jsx(ut,{variant:"primary",type:"submit",loading:o,disabled:o,children:"Activate"})]})]})})}function h8(){const{data:t,isFetching:a,refetch:s}=sv();return m.jsxs("div",{className:"bg-white shadow-xs border border-gray-200 rounded-xl p-6 space-y-4",children:[m.jsxs("div",{className:"flex justify-between items-center",children:[m.jsx("h3",{className:"font-normal text-lg text-gray-900",children:"Cache Status"}),m.jsx("button",{className:"text-indigo-600 cursor-pointer",onClick:s,children:m.jsx(iv,{className:`size-5 ${a?"animate-spin":""}`})})]}),m.jsxs("div",{className:"flex justify-between items-center",children:[m.jsxs("p",{className:"flex items-center gap-2 text-base text-gray-700",children:[m.jsx(cT,{className:"size-5 text-green-500"}),"Pages Cached"]}),m.jsx("p",{className:"font-mono text-base text-gray-900",children:m.jsx(Iv,{value:(t==null?void 0:t.pages_cached)||0})})]}),m.jsx("div",{className:"border-t border-gray-200 my-4"}),m.jsxs("div",{className:"flex justify-between items-center",children:[m.jsxs("p",{className:"flex items-center gap-2 text-base text-gray-700",children:[m.jsx(hT,{className:"size-5 text-orange-400"}),"URLs in Queue"]}),m.jsx("p",{className:"font-mono text-base text-gray-900",children:m.jsx(Iv,{value:(t==null?void 0:t.pages_in_queue)||0})})]})]})}function m8(){const{mutate:t,isPending:a}=rv();return m.jsxs("div",{className:"bg-white shadow-xs border border-gray-200 rounded-xl p-6 space-y-4",children:[m.jsxs("div",{className:"flex justify-start items-start gap-4",children:[m.jsx("div",{className:"bg-indigo-50 p-2 rounded-lg",children:m.jsx(tT,{className:"text-indigo-600 size-6"})}),m.jsxs("div",{children:[m.jsx("h3",{className:"font-normal text-lg text-gray-900",children:"Preload Cache"}),m.jsx("p",{className:"text-gray-500 font-normal text-base",children:"Regenerates cache without clearing existing cached pages"})]})]}),m.jsx(ut,{variant:"outline",onClick:t,loading:a,children:"Preload Cache"})]})}function p8(){const{mutate:t,isPending:a}=pT();return m.jsxs("div",{className:"bg-white shadow-xs border border-gray-200 rounded-xl p-6 space-y-4",children:[m.jsxs("div",{className:"flex justify-start items-start gap-4",children:[m.jsx("div",{className:"bg-indigo-50 p-2 rounded-lg",children:m.jsx(eT,{className:"text-indigo-600 size-6"})}),m.jsxs("div",{children:[m.jsx("h3",{className:"font-normal text-lg text-gray-900",children:"Purges & Preload Cache"}),m.jsx("p",{className:"text-gray-500 font-normal text-base",children:"Clears all cached pages and then regenerates the cache"})]})]}),m.jsx(ut,{variant:"outline",onClick:t,loading:a,children:"Purge & Preload Cache"})]})}function y8(){const{watch:t}=fn(),a=t("cdn"),s=t("cdn_type"),r=t("flying_cdn_api_key");return a&&s==="flying_cdn"&&r?null:m.jsx("div",{className:"bg-white shadow-xs border border-gray-200 rounded-xl p-6 space-y-4",children:m.jsxs("div",{className:"flex flex-col md:flex-row items-start md:items-center gap-4",children:[m.jsx("div",{className:"bg-indigo-50 p-2 rounded-lg",children:m.jsx(Bo,{className:"text-indigo-600 size-6"})}),m.jsxs("div",{className:"grow",children:[m.jsx("h2",{className:"text-lg font-base text-gray-900",children:"FlyingCDN — The Next-Gen CDN for WordPress"}),m.jsx("p",{className:"text-base text-gray-500",children:"Delivers pages from the cloud, optimizes images, reduces server load, and enhances security"})]}),m.jsx("a",{href:"https://flyingcdn.com/",target:"_blank",rel:"noreferrer",children:m.jsx(ut,{variant:"primary",children:"Try FlyingCDN"})})]})})}function g8(){const t=[{icon:YE,title:"YouTube Channel",description:"Explore tutorials, tips, and best practices to make the most of FlyingPress for your WordPress site",link:"https://www.youtube.com/@FlyingPress_",linkText:"Watch Videos"},{icon:WE,title:"Documentation",description:"Step-by-step guides to help you configure, customize, and optimize FlyingPress with ease",link:"https://docs.flyingpress.com/",linkText:"View Docs"},{icon:oT,title:"Facebook Community",description:"Join fellow FlyingPress users to ask questions, share insights, and learn from real-world experiences",link:"https://www.facebook.com/groups/flyingpress",linkText:"Join Community"},{icon:lT,title:"Open Ticket",description:"Facing an issue or need personalized help? Reach out to our support team anytime",link:"https://flyingpress.com/account/?tab=support",linkText:"Get Support"}];return m.jsxs("div",{children:[m.jsx("h4",{className:"text-gray-600 text-base",children:"NEED HELP? WE'VE GOT YOU COVERED"}),m.jsx("div",{className:"divide-y divide-gray-200",children:t.map(({icon:a,title:s,description:r,link:o,linkText:c})=>m.jsxs("div",{className:"flex gap-4 py-4",children:[m.jsx(a,{className:"size-5 mt-1 shrink-0"}),m.jsxs("div",{className:"flex flex-col md:flex-row gap-2 grow justify-between",children:[m.jsxs("div",{children:[m.jsx("h3",{className:"text-lg text-gray-900",children:s}),m.jsx("p",{className:"text-base text-gray-500",children:r})]}),m.jsx("a",{href:o,target:"_blank",rel:"noreferrer",className:"text-indigo-600 text-base hover:text-indigo-700",children:c})]})]},s))})]})}function v8(){const{watch:t}=fn(),a=t("db_auto_clean");return m.jsxs("section",{className:"space-y-6",children:[m.jsxs("div",{className:"bg-white shadow-xs border border-gray-200 rounded-xl p-6 space-y-4",children:[m.jsxs("h3",{className:"font-medium text-base text-gray-900",children:[m.jsx(ad,{className:"size-6 inline-block mr-2 mb-1 text-indigo-600"}),"Automatic Cleaning"]}),m.jsxs("div",{className:"flex flex-col gap-2",children:[m.jsxs("div",{className:"flex justify-between gap-2",children:[m.jsxs("div",{children:[m.jsx("span",{className:"font-normal text-base text-gray-900",children:"Auto Clean Database"}),m.jsx("p",{className:"text-sm text-gray-500 mt-1",children:"Schedule automatic cleanup of your database to maintain optimal performance"})]}),m.jsx(Oe,{name:"db_auto_clean"})]}),a&&m.jsx(el,{options:[{label:"Daily",value:"daily"},{label:"Weekly",value:"weekly"},{label:"Monthly",value:"monthly"}],name:"db_auto_clean_interval"})]})]}),m.jsxs("div",{className:"bg-white shadow-xs border border-gray-200 rounded-xl p-6 space-y-4",children:[m.jsxs("h3",{className:"font-medium text-base text-gray-900",children:[m.jsx(tv,{className:"size-6 inline-block mr-2 mb-1 text-indigo-600"}),"Post Cleanup"]}),m.jsxs("div",{className:"flex justify-between gap-2",children:[m.jsxs("div",{children:[m.jsx("span",{className:"font-normal text-base text-gray-900",children:"Delete Post Revisions"}),m.jsx("p",{className:"text-sm text-gray-500 mt-1",children:"Remove old versions of posts that are no longer needed"})]}),m.jsx(Oe,{name:"db_post_revisions"})]}),m.jsx("div",{className:"border-t border-gray-200"}),m.jsxs("div",{className:"flex justify-between gap-2",children:[m.jsxs("div",{children:[m.jsx("span",{className:"font-normal text-base text-gray-900",children:"Delete Post Auto Drafts"}),m.jsx("p",{className:"text-sm text-gray-500 mt-1",children:"Clean up automatically saved drafts that were never published"})]}),m.jsx(Oe,{name:"db_post_auto_drafts"})]}),m.jsx("div",{className:"border-t border-gray-200"}),m.jsxs("div",{className:"flex justify-between gap-2",children:[m.jsxs("div",{children:[m.jsx("span",{className:"font-normal text-base text-gray-900",children:"Delete Trashed Posts"}),m.jsx("p",{className:"text-sm text-gray-500 mt-1",children:"Permanently remove posts from trash to free up space"})]}),m.jsx(Oe,{name:"db_post_trashed"})]})]}),m.jsxs("div",{className:"bg-white shadow-xs border border-gray-200 rounded-xl p-6 space-y-4",children:[m.jsxs("h3",{className:"font-medium text-base text-gray-900",children:[m.jsx(IE,{className:"size-6 inline-block mr-2 mb-1 text-indigo-600"}),"Comment Cleanup"]}),m.jsxs("div",{className:"flex justify-between gap-2",children:[m.jsxs("div",{children:[m.jsx("span",{className:"font-normal text-base text-gray-900",children:"Delete Spam Comments"}),m.jsx("p",{className:"text-sm text-gray-500 mt-1",children:"Remove comments marked as spam to keep your database clean"})]}),m.jsx(Oe,{name:"db_comments_spam"})]}),m.jsx("div",{className:"border-t border-gray-200"}),m.jsxs("div",{className:"flex justify-between gap-2",children:[m.jsxs("div",{children:[m.jsx("span",{className:"font-normal text-base text-gray-900",children:"Delete Trashed Comments"}),m.jsx("p",{className:"text-sm text-gray-500 mt-1",children:"Permanently remove comments from trash to free up space"})]}),m.jsx(Oe,{name:"db_comments_trashed"})]})]}),m.jsxs("div",{className:"bg-white shadow-xs border border-gray-200 rounded-xl p-6 space-y-4",children:[m.jsxs("h3",{className:"font-medium text-base text-gray-900",children:[m.jsx(sT,{className:"size-6 inline-block mr-2 mb-1 text-indigo-600"}),"Table Optimization"]}),m.jsxs("div",{className:"flex justify-between gap-2",children:[m.jsxs("div",{children:[m.jsx("span",{className:"font-normal text-base text-gray-900",children:"Delete Expired Transients"}),m.jsx("p",{className:"text-sm text-gray-500 mt-1",children:"Remove temporary cached data that has expired"})]}),m.jsx(Oe,{name:"db_transients_expired"})]}),m.jsx("div",{className:"border-t border-gray-200"}),m.jsxs("div",{className:"flex justify-between gap-2",children:[m.jsxs("div",{children:[m.jsx("span",{className:"font-normal text-base text-gray-900",children:"Optimize Database Tables"}),m.jsx("p",{className:"text-sm text-gray-500 mt-1",children:"Reorganize database tables to reduce fragmentation and improve performance"})]}),m.jsx(Oe,{name:"db_optimize_tables"})]})]})]})}function b8(){return m.jsxs("section",{className:"space-y-6",children:[m.jsx(x8,{}),m.jsx(S8,{}),m.jsx(w8,{}),m.jsx(E8,{})]})}function x8(){const{watch:t,setValue:a}=fn(),s=t("js_delay"),r=t("js_delay_third_party"),o=t("css_rucss"),c=t("css_rucss_include_selectors"),d=t("js_delay_excludes"),h=t("js_delay_method"),p=t("js_delay_third_party_excludes"),[y,v]=w.useState(!1),[b,S]=w.useState(!1),[E,N]=w.useState(!1);return m.jsxs("div",{className:"bg-white shadow-xs border border-gray-200 rounded-xl p-6 space-y-4",children:[m.jsxs("h3",{className:"font-medium text-base text-gray-900",children:[m.jsx(KE,{className:"size-6 inline-block mr-2 mb-1 text-indigo-600"}),"CSS & JavaScript"]}),m.jsx("div",{className:"flex flex-col gap-2",children:m.jsxs("div",{className:"flex justify-between gap-2",children:[m.jsxs("div",{children:[m.jsx("span",{className:"font-normal text-base text-gray-900",children:"Minify CSS and JavaScript"}),m.jsx("p",{className:"text-sm text-gray-500 mt-1",children:"Reduces file size by removing unnecessary characters without changing functionality"})]}),m.jsx(Oe,{name:"css_js_minify"})]})}),m.jsx("div",{className:"border-t border-gray-200"}),m.jsx("div",{className:"flex flex-col gap-2",children:m.jsxs("div",{className:"flex justify-between gap-2",children:[m.jsxs("div",{children:[m.jsx("span",{className:"font-normal text-base text-gray-900",children:"Remove Unused CSS"}),m.jsx("p",{className:"text-sm text-gray-500 mt-1",children:"Loads only the CSS used on the initial view. Defers the rest until user interaction"})]}),m.jsxs("div",{className:"flex items-center gap-4",children:[o&&m.jsxs(ut,{onClick:()=>N(!0),children:["Edit includes"," ",!!c.length&&`(${c.length})`]}),m.jsx(Oe,{name:"css_rucss"})]})]})}),m.jsx(ka,{open:E,onClose:()=>N(!1),initialValue:c??[],onSave:C=>{a("css_rucss_include_selectors",C,{shouldDirty:!0}),N(!1)},title:"Include CSS Selectors",description:"Add keywords to forcefully include matching CSS selectors in the generated CSS"}),m.jsx("div",{className:"border-t border-gray-200"}),m.jsxs("div",{className:"flex flex-col gap-2",children:[m.jsxs("div",{className:"flex justify-between gap-2",children:[m.jsxs("div",{children:[m.jsx("span",{className:"font-normal text-base text-gray-900",children:"Delay All JavaScript"}),m.jsx("p",{className:"text-sm text-gray-500 mt-1",children:"Delays all scripts from your site and third-party sources during page load"})]}),m.jsxs("div",{className:"flex items-center gap-4",children:[s&&m.jsxs(ut,{onClick:()=>v(!0),children:["Edit exclusions"," ",!!d.length&&`(${d.length})`]}),m.jsx(Oe,{name:"js_delay"})]})]}),s&&m.jsx(el,{name:"js_delay_method",options:[{label:"Load when idle",value:"background"},{label:"Load after interaction",value:"user-interaction"}]})]}),m.jsx(ka,{open:y,onClose:()=>v(!1),initialValue:d??[],onSave:C=>{a("js_delay_excludes",C,{shouldDirty:!0}),v(!1)},title:"Site Script Exclusions",description:"Enter scripts that should not be delayed"}),s&&h==="background"&&m.jsxs(m.Fragment,{children:[m.jsx("div",{className:"border-t border-gray-200"}),m.jsx("div",{className:"flex flex-col gap-2",children:m.jsxs("div",{className:"flex justify-between gap-2",children:[m.jsxs("div",{children:[m.jsx("span",{className:"font-normal text-base text-gray-900",children:"Load Third-party Scripts on Interaction"}),m.jsx("p",{className:"text-sm text-gray-500 mt-1",children:"Waits for user interaction before running third-party scripts like ads and analytics"})]}),m.jsxs("div",{className:"flex items-center gap-4",children:[r&&m.jsxs(ut,{onClick:()=>S(!0),children:["Edit exclusions"," ",!!p.length&&`(${p.length})`]}),m.jsx(Oe,{name:"js_delay_third_party"})]})]})}),m.jsx(ka,{open:b,onClose:()=>S(!1),initialValue:p??[],onSave:C=>{a("js_delay_third_party_excludes",C,{shouldDirty:!0}),S(!1)},title:"Third-Party Script Exclusions",description:"Enter external scripts that should not be delayed"})]}),m.jsx("div",{className:"border-t border-gray-200"}),m.jsx("div",{className:"flex flex-col gap-2",children:m.jsxs("div",{className:"flex justify-between gap-2",children:[m.jsxs("div",{children:[m.jsx("span",{className:"font-normal text-base text-gray-900",children:"Self-host External CSS and JavaScript"}),m.jsx("p",{className:"text-sm text-gray-500 mt-1",children:"Downloads and serves important external resources locally"})]}),m.jsx(Oe,{name:"css_js_self_host_third_party"})]})})]})}function S8(){const{watch:t,setValue:a}=fn(),s=t("lazy_load"),r=t("lazy_load_exclusions"),[o,c]=w.useState(!1);return m.jsxs("div",{className:"bg-white shadow-xs border border-gray-200 rounded-xl p-6 space-y-4",children:[m.jsxs("h3",{className:"font-medium text-base text-gray-900",children:[m.jsx(QE,{className:"size-6 inline-block mr-2 mb-1 text-indigo-600"}),"Images, Videos & Iframes"]}),m.jsx("div",{className:"flex flex-col gap-2",children:m.jsxs("div",{className:"flex justify-between gap-2",children:[m.jsxs("div",{children:[m.jsx("span",{className:"font-normal text-base text-gray-900",children:"Lazy Load Images, Videos, and Iframes"}),m.jsx("p",{className:"text-sm text-gray-500 mt-1",children:"Delays below-the-fold media while loading above-the-fold content immediately for faster rendering"})]}),m.jsxs("div",{className:"flex items-center gap-4",children:[s&&m.jsxs(ut,{onClick:()=>c(!0),children:["Edit exclusions"," ",!!r.length&&`(${r.length})`]}),m.jsx(Oe,{name:"lazy_load"})]})]})}),m.jsx(ka,{open:o,onClose:()=>c(!1),initialValue:r??[],onSave:d=>{a("lazy_load_exclusions",d,{shouldDirty:!0}),c(!1)},title:"Media Exclusions",description:"Enter media elements that should not be lazy loaded"}),m.jsx("div",{className:"border-t border-gray-200"}),m.jsx("div",{className:"flex flex-col gap-2",children:m.jsxs("div",{className:"flex justify-between gap-2",children:[m.jsxs("div",{children:[m.jsx("span",{className:"font-normal text-base text-gray-900",children:"Properly Size Images"}),m.jsx("p",{className:"text-sm text-gray-500 mt-1",children:"Ensures images load with correct dimensions to avoid visual disruptions during page load"})]}),m.jsx(Oe,{name:"properly_size_images"})]})}),m.jsx("div",{className:"border-t border-gray-200"}),m.jsx("div",{className:"flex flex-col gap-2",children:m.jsxs("div",{className:"flex justify-between gap-2",children:[m.jsxs("div",{children:[m.jsx("span",{className:"font-normal text-base text-gray-900",children:"Lightweight YouTube Previews"}),m.jsx("p",{className:"text-sm text-gray-500 mt-1",children:"Replaces heavy YouTube iframes with lightweight previews that load on click"})]}),m.jsx(Oe,{name:"youtube_placeholder"})]})}),m.jsx("div",{className:"border-t border-gray-200"}),m.jsx("div",{className:"flex flex-col gap-2",children:m.jsxs("div",{className:"flex justify-between gap-2",children:[m.jsxs("div",{children:[m.jsx("span",{className:"font-normal text-base text-gray-900",children:"Self-host Gravatar Images"}),m.jsx("p",{className:"text-sm text-gray-500 mt-1",children:"Stores avatar images locally instead of loading them from Gravatar servers"})]}),m.jsx(Oe,{name:"self_host_gravatars"})]})})]})}function w8(){return m.jsxs("div",{className:"bg-white shadow-xs border border-gray-200 rounded-xl p-6 space-y-4",children:[m.jsxs("h3",{className:"font-medium text-base text-gray-900",children:[m.jsx(rT,{className:"size-6 inline-block mr-2 mb-1 text-indigo-600"}),"Fonts"]}),m.jsx("div",{className:"flex flex-col gap-2",children:m.jsxs("div",{className:"flex justify-between gap-2",children:[m.jsxs("div",{children:[m.jsx("span",{className:"font-normal text-base text-gray-900",children:"Preload Fonts"}),m.jsx("p",{className:"text-sm text-gray-500 mt-1",children:"Prioritizes loading of essential font files to speed up initial text display"})]}),m.jsx(Oe,{name:"fonts_preload"})]})}),m.jsx("div",{className:"border-t border-gray-200"}),m.jsx("div",{className:"flex flex-col gap-2",children:m.jsxs("div",{className:"flex justify-between gap-2",children:[m.jsxs("div",{children:[m.jsx("span",{className:"font-normal text-base text-gray-900",children:"Self-host Google Fonts"}),m.jsx("p",{className:"text-sm text-gray-500 mt-1",children:"Downloads and serves Google Fonts locally for better performance and privacy"})]}),m.jsx(Oe,{name:"fonts_optimize_google"})]})}),m.jsx("div",{className:"border-t border-gray-200"}),m.jsx("div",{className:"flex flex-col gap-2",children:m.jsxs("div",{className:"flex justify-between gap-2",children:[m.jsxs("div",{children:[m.jsx("span",{className:"font-normal text-base text-gray-900",children:"Use System Fonts First"}),m.jsx("p",{className:"text-sm text-gray-500 mt-1",children:"Displays system fonts temporarily to prevent blank text while custom fonts load"})]}),m.jsx(Oe,{name:"fonts_display_swap"})]})})]})}function E8(){const{watch:t,setValue:a}=fn(),s=t("lazy_render"),r=t("lazy_render_excludes"),[o,c]=w.useState(!1);return m.jsxs("div",{className:"bg-white shadow-xs border border-gray-200 rounded-xl p-6 space-y-4",children:[m.jsxs("h3",{className:"font-medium text-base text-gray-900",children:[m.jsx(iT,{className:"size-6 inline-block mr-2 mb-1 text-indigo-600"}),"Rendering"]}),m.jsxs("div",{className:"flex flex-col gap-2",children:[m.jsxs("div",{className:"flex justify-between gap-2",children:[m.jsxs("div",{children:[m.jsx("span",{className:"font-normal text-base text-gray-900",children:"Lazy Render Elements"}),m.jsx("p",{className:"text-sm text-gray-500 mt-1",children:"Delays rendering of complex elements until they're needed to improve initial page speed"})]}),m.jsxs("div",{className:"flex items-center gap-4",children:[s&&m.jsxs(ut,{onClick:()=>c(!0),children:["Edit exclusions"," ",!!r.length&&`(${r.length})`]}),m.jsx(Oe,{name:"lazy_render"})]})]}),m.jsx(ka,{open:o,onClose:()=>c(!1),initialValue:r??[],onSave:d=>{a("lazy_render_excludes",d,{shouldDirty:!0}),c(!1)},title:"Lazy Render Exclusions",description:"Enter elements that should not be lazily rendered"})]})]})}function T8(){const{watch:t,setValue:a}=fn(),s=t(),[r,o]=w.useState(!1),c=w.useRef(null),d=()=>{c.current.click()},h=w.useCallback(()=>{try{const y=["license_key","license_active","license_status","cdn_url","flying_cdn_api_key"],v={...s};y.forEach(C=>{delete v[C]});const b=JSON.stringify(v,null,2),S=new Blob([b],{type:"application/json"}),E=URL.createObjectURL(S),N=document.createElement("a");N.href=E,N.download=`${window.location.hostname}-flying-press-config.json`,document.body.appendChild(N),N.click(),URL.revokeObjectURL(E),document.body.removeChild(N)}catch(y){console.error("Failed to export configuration:",y),He.error("Failed to export configuration")}},[s]),p=w.useCallback(y=>{const v=y.target.files[0];if(!v)return;o(!0);const b=new FileReader;b.onload=S=>{try{const E=JSON.parse(S.target.result);if(!E||typeof E!="object")throw new Error("Invalid configuration file");Object.entries(E).forEach(([N,C])=>{a(N,C)}),He.success("Configuration imported successfully")}catch(E){console.error("Failed to parse configuration file:",E),He.error("Invalid configuration file")}finally{o(!1)}},b.onerror=()=>{He.error("Failed to read configuration file"),o(!1)},b.readAsText(v),y.target.value=""},[a]);return m.jsxs("div",{className:"bg-white shadow-xs border border-gray-200 rounded-xl p-6 space-y-4",children:[m.jsxs("h3",{className:"font-medium text-base text-gray-900",children:[m.jsx(aT,{className:"size-6 inline-block mr-2 mb-1 text-indigo-600"}),"Configuration Management"]}),m.jsxs("div",{className:"flex flex-col gap-4",children:[m.jsxs("div",{children:[m.jsx("span",{className:"font-normal text-base text-gray-900",children:"Import or Export Configuration"}),m.jsx("p",{className:"text-sm text-gray-500 mt-1",children:"Backup your settings or transfer them to another site"})]}),m.jsxs("div",{className:"flex gap-4",children:[m.jsx(ut,{variant:"outline",onClick:h,children:"Export Configuration"}),m.jsx(ut,{variant:"outline",onClick:d,loading:r,children:"Import Configuration"}),m.jsx("input",{type:"file",ref:c,onChange:p,accept:".json",className:"hidden"})]})]})]})}function C8(){const{mutate:t,isPending:a}=yT();return m.jsxs("div",{className:"bg-white shadow-xs border border-gray-200 rounded-xl p-6 space-y-4",children:[m.jsxs("h3",{className:"font-medium text-base text-gray-900 text-red-600",children:[m.jsx(ad,{className:"size-6 inline-block mr-2 mb-1 text-red-600"}),"Danger Zone"]}),m.jsxs("div",{className:"flex flex-col gap-4",children:[m.jsxs("div",{children:[m.jsx("span",{className:"font-normal text-base text-gray-900",children:"Purge Entire Cache"}),m.jsx("p",{className:"text-sm text-gray-500 mt-1",children:"Clears all caches, including FlyingCDN and cloud optimizations. May temporarily slow down preload and serve uncached pages."})]}),m.jsx("div",{children:m.jsx(ut,{variant:"danger",onClick:t,loading:a,children:"Purge Entire Cache"})})]})]})}function j8(){const{register:t,handleSubmit:a,reset:s}=ed(),{mutate:r,isPending:o}=lv(),c=({license_key:d})=>{r(d,{onSuccess:()=>{s()}})};return m.jsxs("div",{className:"bg-white shadow-xs border border-gray-200 rounded-xl p-6 space-y-4",children:[m.jsxs("h3",{className:"font-medium text-base text-gray-900",children:[m.jsx(av,{className:"size-6 inline-block mr-2 mb-1 text-indigo-600"}),"License Management"]}),m.jsxs("div",{className:"flex flex-col gap-4",children:[m.jsxs("div",{children:[m.jsx("span",{className:"font-normal text-base text-gray-900",children:"Change License Key"}),m.jsx("p",{className:"text-sm text-gray-500 mt-1",children:"Enter a new license key to change your current license"})]}),m.jsxs("form",{onSubmit:a(c),className:"flex gap-3 max-w-xl",children:[m.jsx($o,{...t("license_key"),required:!0,placeholder:"Enter new license key",className:"w-64",disabled:o}),m.jsx(ut,{variant:"outline",type:"submit",loading:o,disabled:o,children:"Change License"})]})]})]})}function A8(){return m.jsxs("section",{className:"space-y-6",children:[m.jsx(j8,{}),m.jsx(T8,{}),m.jsx(C8,{})]})}function D8(){var y;const[t]=oo(),{mutateAsync:a}=gT(),{license_key:s,license_active:r,license_status:o,...c}=((y=window.flying_press)==null?void 0:y.config)||{license_key:"",license_active:!0,license_status:"active",css_js_minify:!0,css_rucss:!0,css_rucss_include_selectors:[],js_delay:!0,js_delay_method:"background",js_delay_excludes:[],js_delay_third_party:!0,js_delay_third_party_excludes:[],css_js_self_host_third_party:!0,lazy_load:!0,lazy_load_exclusions:[],properly_size_images:!0,youtube_placeholder:!0,self_host_gravatars:!0,fonts_preload:!0,fonts_optimize_google:!0,fonts_display_swap:!0,lazy_render:!1,lazy_render_excludes:[],cache_mobile:!1,cache_logged_in:!1,cache_link_prefetch:!0,cache_refresh:!1,cache_refresh_interval:"2hours",cache_bypass_urls:[],cache_include_queries:[],cache_bypass_cookies:[],cdn:!0,cdn_type:"flying_cdn",cdn_url:"",cdn_file_types:"all",flying_cdn_api_key:"",db_auto_clean:!1,db_auto_clean_interval:"daily",db_post_revisions:!0,db_post_auto_drafts:!0,db_post_trashed:!0,db_comments_spam:!0,db_comments_trashed:!0,db_transients_expired:!0,db_optimize_tables:!0,bloat_disable_block_css:!0,bloat_disable_dashicons:!0,bloat_disable_emojis:!0,bloat_disable_jquery_migrate:!0,bloat_disable_xml_rpc:!0,bloat_disable_rss_feed:!1,bloat_disable_oembeds:!0,bloat_disable_cron:!0,bloat_post_revisions_control:!0,bloat_heartbeat_control:!1},d=ed({defaultValues:c}),h=d.watch(),p=w.useMemo(()=>R3(async v=>{try{await a(v),d.reset(v)}catch(b){console.error("Failed to save config:",b)}},2e3),[a,d]);return w.useEffect(()=>(d.formState.isDirty&&p(h),()=>p.cancel()),[h,d.formState.isDirty,p]),m.jsxs(m.Fragment,{children:[m.jsx("div",{className:"bg-white border-b border-gray-200",children:m.jsxs("div",{className:"max-w-6xl mx-auto px-4",children:[m.jsx(ST,{}),m.jsx(ET,{})]})}),m.jsx(L3,{...d,children:m.jsx("div",{className:"max-w-6xl mx-auto px-4 py-8",children:m.jsx(U0,{mode:"wait",initial:!1,children:m.jsx(Do.div,{initial:{x:20,opacity:0},animate:{x:0,opacity:1},exit:{x:-20,opacity:0},transition:{duration:.15,ease:"easeInOut"},children:m.jsxs(JS,{location:t,children:[m.jsx(ja,{path:"/",component:e1}),m.jsx(ja,{path:"/dashboard",component:e1}),m.jsx(ja,{path:"/optimization",component:b8}),m.jsx(ja,{path:"/caching",component:u8}),m.jsx(ja,{path:"/cdn",component:c8}),m.jsx(ja,{path:"/database",component:v8}),m.jsx(ja,{path:"/bloat",component:TT}),m.jsx(ja,{path:"/settings",component:A8})]})},t)})})}),m.jsx(FE,{toastOptions:{className:"text-sm text-gray-600 mt-8"}})]})}const _8=new uS;RS.createRoot(document.getElementById("app")).render(m.jsx(w.StrictMode,{children:m.jsx(pS,{client:_8,children:m.jsx(N0,{hook:oo,children:m.jsx(D8,{})})})}))})();
