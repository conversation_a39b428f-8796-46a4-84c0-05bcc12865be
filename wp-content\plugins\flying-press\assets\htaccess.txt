# BEGIN FlyingPress
# Cache-control response header for browser caching
<IfModule mod_expires.c>
	ExpiresActive on
	# Your document html
	ExpiresByType text/html                     "access plus 0 seconds"
	# Data
	ExpiresByType text/xml                      "access plus 0 seconds"
	ExpiresByType application/xml               "access plus 0 seconds"
	ExpiresByType application/json              "access plus 0 seconds"
	# Favicon
	ExpiresByType image/x-icon                  "access plus 1 year"
	# Media: images, video, audio
	ExpiresByType image/gif                     "access plus 1 year"
	ExpiresByType image/png                     "access plus 1 year"
	ExpiresByType image/jpeg                    "access plus 1 year"
	ExpiresByType image/webp                    "access plus 1 year"
	ExpiresByType image/avif                    "access plus 1 year"
	ExpiresByType video/ogg                     "access plus 1 year"
	ExpiresByType audio/ogg                     "access plus 1 year"
	ExpiresByType video/mp4                     "access plus 1 year"
	ExpiresByType video/webm                    "access plus 1 year"
	# HTC files  (css3pie)
	ExpiresByType text/x-component              "access plus 1 year"
	# Webfonts
	ExpiresByType font/ttf                      "access plus 1 year"
	ExpiresByType font/otf                      "access plus 1 year"
	ExpiresByType font/woff                     "access plus 1 year"
	ExpiresByType font/woff2                    "access plus 1 year"
	ExpiresByType image/svg+xml                 "access plus 1 year"
	ExpiresByType application/vnd.ms-fontobject "access plus 1 year"
	# CSS and JavaScript
	ExpiresByType text/css                      "access plus 1 year"
	ExpiresByType text/javascript               "access plus 1 year"
	ExpiresByType application/javascript        "access plus 1 year"
	ExpiresByType application/x-javascript      "access plus 1 year"
	ExpiresByType application/font-woff2        "access plus 1 year"
</IfModule>

# GZIP compression for text files: HTML, CSS, JS, Text, XML, fonts
<IfModule mod_deflate.c>
	AddOutputFilterByType DEFLATE application/javascript
	AddOutputFilterByType DEFLATE application/rss+xml
	AddOutputFilterByType DEFLATE application/vnd.ms-fontobject
	AddOutputFilterByType DEFLATE application/x-font
	AddOutputFilterByType DEFLATE application/x-font-opentype
	AddOutputFilterByType DEFLATE application/x-font-otf
	AddOutputFilterByType DEFLATE application/x-font-truetype
	AddOutputFilterByType DEFLATE application/x-font-ttf
	AddOutputFilterByType DEFLATE application/x-javascript
	AddOutputFilterByType DEFLATE application/font-woff2
	AddOutputFilterByType DEFLATE application/xhtml+xml
	AddOutputFilterByType DEFLATE application/xml
	AddOutputFilterByType DEFLATE font/opentype
	AddOutputFilterByType DEFLATE font/otf
	AddOutputFilterByType DEFLATE font/ttf
	AddOutputFilterByType DEFLATE font/woff
	AddOutputFilterByType DEFLATE font/woff2
	AddOutputFilterByType DEFLATE image/svg+xml
	AddOutputFilterByType DEFLATE image/x-icon
	AddOutputFilterByType DEFLATE text/css
	AddOutputFilterByType DEFLATE text/html
	AddOutputFilterByType DEFLATE text/javascript
	AddOutputFilterByType DEFLATE text/plain
	AddOutputFilterByType DEFLATE text/xml
	SetEnvIfNoCase Request_URI \.gz$ no-gzip
</IfModule>

# Response headers for cached .html.gz pages
<FilesMatch "\.html\.gz$">
	<IfModule mod_headers.c>
        Header set Content-Encoding "gzip"
        Header set Content-Type "text/html; charset=UTF-8"
        Header set Cache-Tag "HOSTNAME"
        Header set CDN-Cache-Control "max-age=2592000"
        Header set Cache-Control "no-cache, must-revalidate"
        Header set x-flying-press-cache "HIT"
        Header set x-flying-press-source "Web Server"
    </IfModule>
	# Fallback when mod_headers is not available
	<IfModule mod_mime.c>
        AddType text/html .gz
        AddCharset UTF-8 .gz
        AddEncoding gzip .gz
    </IfModule>
</FilesMatch>

# Start rewrite requests to cache if found
<IfModule mod_rewrite.c>
	RewriteEngine On
	RewriteBase /

	# Block direct access to .html.gz files
	RewriteCond %{THE_REQUEST} "/wp-content/cache/flying-press/.*\.html\.gz" [NC]
	RewriteRule ^ - [F]

	# Set mobile caching flag to 1 if mobile caching is enabled
	RewriteRule ^ - [E=MOBILE_CACHING_FLAG:0]

	# Serve mobile cache if the request is from a mobile device and mobile caching is enabled
	RewriteCond %{REQUEST_METHOD} GET|HEAD
	RewriteCond %{QUERY_STRING} =""
	RewriteCond %{HTTP:Cookie} =""
	RewriteCond %{REQUEST_URI} !^/(wp-(?:admin|login|register|comments-post|cron|json))/ [NC]
	RewriteCond %{HTTP_USER_AGENT} "android|blackberry|ipad|iphone|ipod|iemobile|opera mobile|palmos|webos|googlebot-mobile" [NC]
	RewriteCond %{DOCUMENT_ROOT}/wp-content/cache/flying-press/%{HTTP_HOST}%{REQUEST_URI}/index-mobile.html.gz -f
	RewriteCond %{ENV:MOBILE_CACHING_FLAG} =1
	RewriteRule ^(.*)$ wp-content/cache/flying-press/%{HTTP_HOST}%{REQUEST_URI}/index-mobile.html.gz [L]

	# Serve desktop cache if the request is not from a mobile device or if mobile caching is disabled
	RewriteCond %{REQUEST_METHOD} GET|HEAD
	RewriteCond %{QUERY_STRING} =""
	RewriteCond %{HTTP:Cookie} =""
	RewriteCond %{REQUEST_URI} !^/(wp-(?:admin|login|register|comments-post|cron|json))/ [NC]
	RewriteCond %{HTTP_USER_AGENT} "!(android|blackberry|ipad|iphone|ipod|iemobile|opera mobile|palmos|webos|googlebot-mobile)" [NC,OR]
	RewriteCond %{ENV:MOBILE_CACHING_FLAG} !=1
	RewriteCond %{DOCUMENT_ROOT}/wp-content/cache/flying-press/%{HTTP_HOST}%{REQUEST_URI}/index.html.gz -f
	RewriteRule ^(.*)$ wp-content/cache/flying-press/%{HTTP_HOST}%{REQUEST_URI}/index.html.gz [L]
</IfModule>
# End rewrite requests to cache
# END FlyingPress