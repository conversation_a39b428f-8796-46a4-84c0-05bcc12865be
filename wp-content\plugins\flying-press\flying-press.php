<?php

/**
 * Plugin Name: FlyingPress
 * Plugin URI: https://flyingpress.com
 * Description: Lightning-Fast WordPress on Autopilot
 * Version: 5.0.2
 * Requires PHP: 7.4
 * Requires at least: 4.7
 * Author: FlyingWeb
 */

defined('ABSPATH') or die('No script kiddies please!');

require_once dirname(__FILE__) . '/vendor/autoload.php';

define('FLYING_PRESS_VERSION', '5.0.2');
define('FLYING_PRESS_FILE', __FILE__);
define('FLYING_PRESS_FILE_NAME', plugin_basename(__FILE__));
define('FLYING_PRESS_PLUGIN_URL', plugin_dir_url(__FILE__));
define('FLYING_PRESS_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('FLYING_PRESS_CACHE_DIR', WP_CONTENT_DIR . '/cache/flying-press/');
define('FLYING_PRESS_CACHE_URL', WP_CONTENT_URL . '/cache/flying-press/');

!is_dir(FLYING_PRESS_CACHE_DIR) && mkdir(FLYING_PRESS_CACHE_DIR, 0755, true);

// Add HTTP request filter to intercept calls to external license and optimization services
add_filter('pre_http_request', function ($preempt, $args, $url) {
    // Intercept calls to license validation services
    if (strpos($url, 'api.surecart.com') !== false || strpos($url, 'license.flyingpress.com') !== false) {
        return [
            'response' => ['code' => 200, 'message' => 'OK'],
            'body'     => json_encode([
                'status' => 'active',
                'success' => true,
                'expires_at' => date('Y-m-d', strtotime('+10 years')),
                'license_limit' => 1000,
                'site_count' => 1,
                'activations_left' => 999,
                'license_key' => 'B5E0B5F8DD8689E6ACA49DD6E6E1A930'
            ]),
            'headers'  => [],
            'cookies'  => [],
            'filename' => null
        ];
    }

    // Intercept calls to page optimizer service
    if (strpos($url, 'page-optimizer.flyingpress.com') !== false) {
        // Extract the HTML from the request body
        $html = $args['body']['html'] ?? '';
        $hash = $args['body']['hash'] ?? md5($html);

        // Create mock optimization data
        $mock = [
            'structure_hash' => $hash,
            'used_css' => '',
            'used_classes' => [],
            'used_ids' => [],
            'critical_css' => '',
            'critical_classes' => [],
            'critical_ids' => [],
            'unused_css' => '',
            'unused_classes' => [],
            'unused_ids' => [],
            'js_to_delay' => [],
            'font_css' => '',
            'font_preloads' => [],
            'scripts_to_preload' => [],
            'scripts_to_defer' => [],
            'styles_to_preload' => [],
            'processed' => true,
            'success' => true
        ];

        // Find CSS classes in the HTML
        if (!empty($html)) {
            preg_match_all('/\bclass\s*=\s*["\']([^"\']+)["\']/i', $html, $classes);
            $class_list = [];
            if (!empty($classes[1])) {
                foreach ($classes[1] as $class_string) {
                    foreach (preg_split('/\s+/', trim($class_string)) as $class) {
                        $class = trim($class);
                        if ($class !== '') {
                            $class_list[] = $class;
                        }
                    }
                }
                $mock['used_classes'] = array_unique($class_list);
                $mock['critical_classes'] = array_slice($mock['used_classes'], 0, min(20, count($mock['used_classes'])));
            }

            // Find IDs in the HTML
            preg_match_all('/\bid\s*=\s*["\']([^"\']+)["\']/i', $html, $ids);
            if (!empty($ids[1])) {
                $mock['used_ids'] = array_unique($ids[1]);
                $mock['critical_ids'] = array_slice($mock['used_ids'], 0, min(10, count($mock['used_ids'])));
            }
        }

        return [
            'response' => ['code' => 200, 'message' => 'OK'],
            'body'     => json_encode($mock),
            'headers'  => [],
            'cookies'  => [],
            'filename' => null
        ];
    }

    // Let other requests pass through
    return $preempt;
}, 10, 3);

FlyingPress\WPCache::init();
FlyingPress\Htaccess::init();
FlyingPress\AdvancedCache::init();
FlyingPress\Integrations::init();
FlyingPress\AutoPurge::init();
FlyingPress\License::init();
FlyingPress\Preload::init();
FlyingPress\Config::init();
FlyingPress\Cron::init();
FlyingPress\Caching::init();
FlyingPress\RestApi::init();
FlyingPress\AdminBar::init();
FlyingPress\Optimizer::init();
FlyingPress\FlyingCDN::init();
FlyingPress\Dashboard::init();
FlyingPress\Database::init();
FlyingPress\Compatibility::init();
FlyingPress\Permalink::init();
FlyingPress\Shortcuts::init();
FlyingPress\WpCLI::init();
