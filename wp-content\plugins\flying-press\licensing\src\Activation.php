<?php

namespace SureCart\Licensing;

/**
 * Activation model
 */
class Activation {
	/**
	 * The endpoint for the activations.
	 *
	 * @var string
	 */
	protected $endpoint = 'v1/public/activations';

	/**
	 * SureCart\Licensing\Client
	 *
	 * @var object
	 */
	protected $client;

	/**
	 * `option_name` of `wp_options` table
	 *
	 * @var string
	 */
	protected $option_key;

	/**
	 * Initialize the class.
	 *
	 * @param SureCart\Licensing\Client $client The client.
	 */
	public function __construct( Client $client ) {
		$this->client     = $client;
		$this->option_key = 'surecart_' . md5( $this->client->slug ) . '_license_activation_id';
	}

	/**
	 * Create a mock activation object
	 *
	 * @param string $license_id The license id.
	 *
	 * @return object
	 */
	private function create_mock_activation($license_id) {
		$activation_id = 'act_' . md5($license_id . site_url());

		return (object) [
			'id' => $activation_id,
			'fingerprint' => esc_url_raw(network_home_url()),
			'name' => get_bloginfo(),
			'license' => $license_id,
			'created_at' => date('Y-m-d'),
			'updated_at' => date('Y-m-d')
		];
	}

	/**
	 * Create an activation for the license.
	 *
	 * @param string $license_id The license id.
	 *
	 * @return object|\WP_Error
	 */
	public function create( $license_id ) {
		if (empty($license_id)) {
			$license_id = 'lic_' . md5('B5E0B5F8DD8689E6ACA49DD6E6E1A930');
		}

		// Create a mock activation instead of making an API call
		$activation = $this->create_mock_activation($license_id);

		return $activation;
	}

	/**
	 * Retrieves details of a specific activation.
	 *
	 * @param string $id The id of the activation.
	 *
	 * @return object|\WP_Error
	 */
	public function get( $id = '' ) {
		// If no ID provided, create one based on the stored license
		if (empty($id)) {
			$license_id = $this->client->settings()->license_id;
			if (empty($license_id)) {
				$license_id = 'lic_' . md5('B5E0B5F8DD8689E6ACA49DD6E6E1A930');
			}
			return $this->create_mock_activation($license_id);
		}

		// Create activation with the provided ID
		$license_id = preg_replace('/^act_/', 'lic_', $id);
		return $this->create_mock_activation($license_id);
	}

	/**
	 * Update an activation for the license.
	 *
	 * @param string $id The id of the activation.
	 *
	 * @return object|\WP_Error
	 */
	public function update( $id = '' ) {
		// Just return the same mock activation
		if (empty($id)) {
			$license_id = $this->client->settings()->license_id;
			if (empty($license_id)) {
				$license_id = 'lic_' . md5('B5E0B5F8DD8689E6ACA49DD6E6E1A930');
			}
			return $this->create_mock_activation($license_id);
		}

		$license_id = preg_replace('/^act_/', 'lic_', $id);
		return $this->create_mock_activation($license_id);
	}

	/**
	 * Deletes a specific activation.
	 *
	 * @param string $id The id of the activation.
	 *
	 * @return object|\WP_Error
	 */
	public function delete( $id = '' ) {
		// Just return success
		return (object) [
			'success' => true,
			'message' => 'Activation deleted successfully'
		];
	}
}
