<?php

namespace FlyingPress;

class CloudOptimizer
{
  const API_URL = 'https://page-optimizer.flyingpress.com/optimizer/';

  public static $optimizations;

  // Fetches optimizations from cache or API
  public static function fetch_optimizations($html)
  {
    $hash = self::get_hash($html);
    $cache_file = Caching::get_cache_path() . Caching::get_cache_file_name('json');

    // Try reading from cache
    if (is_readable($cache_file)) {
      $json = json_decode(gzdecode(file_get_contents($cache_file)));
      if (($json->structure_hash ?? '') === $hash) {
        return self::$optimizations = $json;
      }
    }

    // Create mock optimization data instead of fetching from API
    $json = self::create_mock_optimizations($html, $hash);

    $json->structure_hash = $hash;
    file_put_contents($cache_file, gzencode(json_encode($json)));

    return self::$optimizations = $json;
  }

  // Create mock optimization data instead of calling external API
  private static function create_mock_optimizations($html, $hash)
  {
    // Create a basic optimization object with empty arrays
    $mock = (object) [
      'structure_hash' => $hash,
      'used_css' => '',
      'used_classes' => [],
      'used_ids' => [],
      'critical_css' => '',
      'critical_classes' => [],
      'critical_ids' => [],
      'unused_css' => '',
      'unused_classes' => [],
      'unused_ids' => [],
      'js_to_delay' => [],
      'font_css' => '',
      'font_preloads' => [],
      'scripts_to_preload' => [],
      'scripts_to_defer' => [],
      'styles_to_preload' => [],
      'processed' => true,
      'success' => true
    ];

    // Find CSS classes in the HTML
    preg_match_all('/\bclass\s*=\s*["\']([^"\']+)["\']/i', $html, $classes);
    $class_list = [];
    foreach ($classes[1] as $class_string) {
      foreach (preg_split('/\s+/', trim($class_string)) as $class) {
        $class = trim($class);
        if ($class !== '') {
          $class_list[] = $class;
        }
      }
    }
    $mock->used_classes = array_unique($class_list);
    $mock->critical_classes = array_slice($mock->used_classes, 0, min(20, count($mock->used_classes)));

    // Find IDs in the HTML
    preg_match_all('/\bid\s*=\s*["\']([^"\']+)["\']/i', $html, $ids);
    $mock->used_ids = array_unique($ids[1] ?? []);
    $mock->critical_ids = array_slice($mock->used_ids, 0, min(10, count($mock->used_ids)));

    return $mock;
  }

  // Sends request to the optimizer API - not used anymore but kept for compatibility
  private static function fetch_from_api($html, $hash)
  {
    // Return mock data instead of making an external API call
    return self::create_mock_optimizations($html, $hash);
  }

  // Creates a structure hash based on HTML tag structure, IDs, classes, etc.
  private static function get_hash($html)
  {
    $html = html_entity_decode($html);

    preg_match_all('/<\s*([a-zA-Z][\w:-]*)\b[^>]*>/i', $html, $tags);
    preg_match_all('/\bid\s*=\s*["\']([^"\']+)["\']/i', $html, $ids);
    preg_match_all('/\bclass\s*=\s*["\']([^"\']+)["\']/i', $html, $classes);
    preg_match_all(
      '/<link[^>]*rel=["\']stylesheet["\'][^>]*href=["\']([^"\']+)["\']/i',
      $html,
      $stylesheets
    );
    preg_match_all('/<script[^>]*src=["\']([^"\']+)["\']/i', $html, $scripts);

    // Add rucss include selectors
    $include_selectors = Config::$config['css_rucss_include_selectors'] ?? [];

    // Flatten and clean class list
    $class_list = [];
    foreach ($classes[1] as $class_string) {
      foreach (preg_split('/\s+/', trim($class_string)) as $class) {
        $class = preg_replace('/\d.*$/', '', trim($class));
        if ($class !== '') {
          $class_list[] = $class;
        }
      }
    }

    // Clean IDs
    $id_list = array_map(fn($id) => preg_replace('/\d.*$/', '', $id), $ids[1] ?? []);

    // Clean tag names
    $tag_list = array_map(fn($tag) => '<' . strtolower($tag) . '>', $tags[1] ?? []);

    // Combine and hash
    $all = array_merge(
      $id_list,
      $class_list,
      $stylesheets[1] ?? [],
      $scripts[1] ?? [],
      $tag_list,
      $include_selectors
    );
    $all = array_unique(array_filter($all));
    sort($all);

    return md5(implode(' ', $all));
  }
}
