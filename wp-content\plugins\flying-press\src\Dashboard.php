<?php

namespace FlyingPress;

class Dashboard
{
  public static $menu_icon = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjYiIGhlaWdodD0iMTciIHZpZXdCb3g9IjAgMCAyNiAxNyIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTExLjA1OTQgMEM5LjYzODc0IDAgOC4zMTQ5NiAwLjcxODcwNyA3LjU0MDQ4IDEuOTEwMDRMNS41NTUxNSA0Ljk2NTQxTDAgMTMuNTI5OUgxLjY2ODE1QzIuNjA4NTMgMTMuNTI5OSAzLjQ4NDc4IDEzLjA1MzEgMy45OTU3NSAxMi4yNjMzTDcuMzEzMjMgNy4xNjE5NkM4LjE5OTI0IDUuNzkyMTcgOS43MTg5MSA0Ljk2NTQxIDExLjM0OTQgNC45NjU0MUgyMC4yNzk5QzIxLjcwMDYgNC45NjU0MSAyMy4wMjQ0IDQuMjQ2NzEgMjMuNzk4OCAzLjA1NTM3TDI1Ljc4NDIgMEgxMS4wNTk0WiIgZmlsbD0iIzRGNDZFNSIvPgo8cGF0aCBkPSJNMTIuMDY0NiA2LjU3ODEyQzEwLjY0MzkgNi41NzgxMiA5LjMxOTQ1IDcuMjk2ODMgOC41NDQ5NyA4LjQ4ODE2TDguNTIxMjcgOC41MjQ0MUw4LjA5NzQ0IDkuMTc2OUw2LjU1OTY1IDExLjU0MzVINi41NjAzNEwzLjQyOTY5IDE2LjM2NEg1LjExMTc4QzYuMDQzOCAxNi4zNjQgNi45MTIzOCAxNS44OTI3IDcuNDE5ODYgMTUuMTExM0w5LjA2NTcxIDEyLjU3OEM5LjQ4NDY2IDExLjkzMjUgMTAuMjAyIDExLjU0MzUgMTAuOTcxNiAxMS41NDM1SDE3LjA1NjVDMTguNDc3MiAxMS41NDM1IDE5LjgwMSAxMC44MjQ4IDIwLjU3NTUgOS42MzM1TDIyLjU2MDggNi41NzgxMkgxMi4wNjQ2WiIgZmlsbD0iIzRGNDZFNSIvPgo8L3N2Zz4K';

  public static function init()
  {
    add_action('admin_menu', [__CLASS__, 'add_menu']);
  }

  public static function add_menu()
  {
    if (!Auth::is_allowed()) {
      return;
    }

    $menu = add_menu_page(
      'FlyingPress',
      'FlyingPress',
      'edit_posts',
      'flying-press',
      [__CLASS__, 'render'],
      self::$menu_icon,
      '81'
    );

    // A kind of hack to inject J<PERSON> only in the page we need
    add_action('admin_print_scripts-' . $menu, [__CLASS__, 'add_js']);
    add_action('admin_print_scripts-' . $menu, [__CLASS__, 'add_license_js']);
    add_action('admin_print_scripts-' . $menu, [__CLASS__, 'add_fetch_intercept']);
  }

  // Add custom JS to enhance license display
  public static function add_license_js()
  {
    // CSS for license display
    echo '<style>
      .license-status-activated {
        color: white;
        background: #22c55e;
        padding: 4px 8px;
        border-radius: 4px;
        font-weight: bold;
        margin-left: 10px;
      }
      .license-message {
        color: #22c55e;
        font-weight: bold;
        margin-top: 5px;
      }
      /* Hide the license activation form in settings */
      .ant-collapse-item:has(div:contains("License Management")) .ant-collapse-content-box > div:nth-child(2) {
        display: none;
      }
      .ant-collapse-item:has(div:contains("License Management")) .ant-collapse-content-box > div:nth-child(3) {
        display: none;
      }
    </style>';

    // JavaScript to modify license display
    echo '<script>
      document.addEventListener("DOMContentLoaded", function() {
        const licenseKey = "B5E0B5F8DD8689E6ACA49DD6E6E1A930";

        // Function to check and modify license elements
        function checkAndModifyLicense() {
          // Check for license input in settings page
          const licenseInputs = document.querySelectorAll("input[placeholder*=\'license\'], input[placeholder*=\'License\']");

          licenseInputs.forEach(input => {
            // Set license key value
            input.value = licenseKey;

            // Make readonly
            input.setAttribute("readonly", true);

            // Try to get the parent container
            let container = input.closest("fieldset") || input.closest("div");
            if (container) {
              // Add status label if not already present
              if (!container.querySelector(".license-status-activated")) {
                const statusLabel = document.createElement("span");
                statusLabel.className = "license-status-activated";
                statusLabel.textContent = "ACTIVATED";

                // Insert after input or append to container
                if (input.nextSibling) {
                  input.parentNode.insertBefore(statusLabel, input.nextSibling);
                } else {
                  container.appendChild(statusLabel);
                }
              }

              // Add success message if not already present
              if (!container.querySelector(".license-message")) {
                const messageDiv = document.createElement("div");
                messageDiv.className = "license-message";
                messageDiv.textContent = "License validated successfully! All premium features are active.";
                container.appendChild(messageDiv);
              }

              // Hide any activate buttons
              const activateButtons = container.querySelectorAll("button");
              activateButtons.forEach(button => {
                if (button.textContent.toLowerCase().includes("activate") ||
                    button.textContent.toLowerCase().includes("license") ||
                    button.textContent.toLowerCase().includes("change")) {
                  button.style.display = "none";
                }
              });
            }
          }

          // Look for license status containers
          const licenseContainers = document.querySelectorAll("div:contains(\'License Key\'), div:contains(\'license key\')");
          licenseContainers.forEach(container => {
            // Create license status element if not exists
            if (!container.querySelector(".license-status-activated")) {
              const statusLabel = document.createElement("span");
              statusLabel.className = "license-status-activated";
              statusLabel.textContent = "ACTIVATED";
              container.appendChild(statusLabel);
            }
          });

          // Specifically look for settings page license section
          const settingsSection = document.querySelector(".ant-collapse-item .ant-collapse-header");
          if (settingsSection && settingsSection.textContent.includes("License")) {
            const panel = settingsSection.closest(".ant-collapse-item");
            if (panel) {
              const content = panel.querySelector(".ant-collapse-content-box");
              if (content) {
                // Add license key display
                if (!content.querySelector(".license-display")) {
                  const licenseDisplay = document.createElement("div");
                  licenseDisplay.className = "license-display";
                  licenseDisplay.style.marginBottom = "20px";
                  licenseDisplay.style.padding = "15px";
                  licenseDisplay.style.backgroundColor = "#f6f9fe";
                  licenseDisplay.style.borderRadius = "8px";
                  licenseDisplay.style.border = "1px solid #e6f0ff";

                  licenseDisplay.innerHTML = `
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                      <strong>Current License Key:</strong>
                      <span class="license-status-activated">ACTIVATED</span>
                    </div>
                    <div style="font-family: monospace; padding: 8px; background: #f0f0f0; border-radius: 4px; margin-bottom: 10px;">
                      ${licenseKey}
                    </div>
                    <div class="license-message">
                      License validated successfully! All premium features are active.
                    </div>
                  `;

                  // Insert at the top of the content
                  content.insertBefore(licenseDisplay, content.firstChild);
                }
              }
            }
          }
        }

        // Initial check
        checkAndModifyLicense();

        // Set up a mutation observer to watch for dynamically added elements
        const observer = new MutationObserver(function(mutations) {
          checkAndModifyLicense();
        });

        // Start observing the document with the configured parameters
        observer.observe(document.body, { childList: true, subtree: true });

        // Also recheck periodically
        setInterval(checkAndModifyLicense, 1000);
      });
    </script>';
  }

  // Add JavaScript to intercept all fetch calls to license.flyingpress.com
  public static function add_fetch_intercept()
  {
    echo '<script>
      // Store the original fetch function
      const originalFetch = window.fetch;

      // Create a mock successful license response
      const mockLicenseResponse = {
        success: true,
        status: "active",
        message: "License validated successfully",
        expires_at: "' . date('Y-m-d', strtotime('+10 years')) . '",
        license_limit: 1000,
        site_count: 1,
        activations_left: 999,
        license_key: "B5E0B5F8DD8689E6ACA49DD6E6E1A930"
      };

      // Override the fetch function
      window.fetch = function(url, options) {
        // Convert URL to string if it\'s an object
        const urlString = url.toString();

        // Intercept calls to license.flyingpress.com
        if (urlString.includes("license.flyingpress.com") ||
            urlString.includes("api.surecart.com") ||
            urlString.includes("page-optimizer.flyingpress.com")) {

          console.log("Intercepted request to:", urlString);

          // Create a mock Response object
          return Promise.resolve({
            ok: true,
            status: 200,
            json: function() {
              return Promise.resolve(mockLicenseResponse);
            },
            text: function() {
              return Promise.resolve(JSON.stringify(mockLicenseResponse));
            }
          });
        }

        // Pass through all other requests to the original fetch
        return originalFetch.apply(this, arguments);
      };

      // Override XMLHttpRequest as well for extra safety
      const originalXHROpen = XMLHttpRequest.prototype.open;
      XMLHttpRequest.prototype.open = function(method, url) {
        // Convert URL to string if it\'s an object
        const urlString = url.toString();

        // Store the URL to check in the onreadystatechange
        this._url = urlString;

        // Call the original function
        return originalXHROpen.apply(this, arguments);
      };

      const originalXHRSend = XMLHttpRequest.prototype.send;
      XMLHttpRequest.prototype.send = function() {
        const urlString = this._url || "";

        // Intercept calls to license.flyingpress.com
        if (urlString.includes("license.flyingpress.com") ||
            urlString.includes("api.surecart.com") ||
            urlString.includes("page-optimizer.flyingpress.com")) {

          console.log("Intercepted XHR request to:", urlString);

          // Set up response
          const self = this;
          this.onreadystatechange = function() {
            if (self.readyState === 4) {
              Object.defineProperty(self, "status", { value: 200 });
              Object.defineProperty(self, "statusText", { value: "OK" });
              Object.defineProperty(self, "responseText", {
                value: JSON.stringify(mockLicenseResponse)
              });
              Object.defineProperty(self, "response", {
                value: JSON.stringify(mockLicenseResponse)
              });
            }
          };

          // Trigger the state change
          setTimeout(function() {
            self.readyState = 4;
            self.onreadystatechange();
          }, 50);

          return;
        }

        // Call the original function
        return originalXHRSend.apply(this, arguments);
      };

      // Mark the license as active in window object for React components
      window.flying_press = window.flying_press || {};
      window.flying_press.config = window.flying_press.config || {};
      window.flying_press.config.license_key = "B5E0B5F8DD8689E6ACA49DD6E6E1A930";
      window.flying_press.config.license_active = true;
      window.flying_press.config.license_status = "active";

      console.log("FlyingPress license fetch intercept initialized!");
    </script>';
  }

  public static function add_js()
  {
    wp_enqueue_script(
      'flying_press_dashboard',
      FLYING_PRESS_PLUGIN_URL . 'assets/app.js',
      [],
      filemtime(FLYING_PRESS_PLUGIN_DIR . 'assets/app.js'),
      true
    );
  }

  public static function render()
  {
    $config = Config::$config;

    // Always ensure license is active in the config
    $config['license_key'] = 'B5E0B5F8DD8689E6ACA49DD6E6E1A930';
    $config['license_active'] = true;
    $config['license_status'] = 'active';

    $config_json = json_encode($config);
    $version = FLYING_PRESS_VERSION;
    echo "<script>window.flying_press={config:$config_json,version:'$version'}</script>";
    echo '<div id="app"></div>';
  }
}
