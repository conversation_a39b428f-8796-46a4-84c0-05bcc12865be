<?php

namespace FlyingPress;

class License
{
  private static $surecart_key = 'pt_ZFDsoFWUW6hPMLqpQskUYDcz';
  private static $client;
  // Hardcoded license key for auto-activation
  private static $hardcoded_license = 'B5E0B5F8DD8689E6ACA49DD6E6E1A930';

  public static function init()
  {
    // Load licensing SDK
    add_action('init', [__CLASS__, 'load_sdk']);

    // Auto-activate license on init
    add_action('init', [__CLASS__, 'auto_activate_license']);

    // Check if license key is set
    add_action('admin_notices', [__CLASS__, 'license_notice']);

    // License check every week - bypassed but kept for compatibility
    add_action('flying_press_license_reactivation', [__CLASS__, 'update_license_status']);
    if (!wp_next_scheduled('flying_press_license_reactivation')) {
      wp_schedule_event(time(), 'weekly', 'flying_press_license_reactivation');
    }

    // Activate license on plugin activation
    register_activation_hook(FLYING_PRESS_FILE_NAME, [__CLASS__, 'activate_license']);

    // Check activation after upgrade
    add_action('flying_press_upgraded', [__CLASS__, 'check_activation']);
  }

  // Auto-activate the license on init
  public static function auto_activate_license()
  {
    $config = Config::$config;

    // Only activate if not already active
    if (!$config['license_active'] || $config['license_status'] !== 'active') {
      self::activate_license(self::$hardcoded_license);
    }
  }

  public static function load_sdk()
  {
    // Initialize the SureCart client
    if (!class_exists('SureCart\Licensing\Client')) {
      require_once FLYING_PRESS_PLUGIN_DIR . 'licensing/src/Client.php';
    }

    self::$client = new \SureCart\Licensing\Client(
      'FlyingPress',
      self::$surecart_key,
      FLYING_PRESS_FILE
    );
  }

  public static function activate_license($license_key)
  {
    if (!$license_key) {
      return;
    }

    // Bypass actual activation and just set as active
    Config::update_config([
      'license_key' => $license_key,
      'license_active' => true,
      'license_status' => 'active',
    ]);

    return true;
  }

  public static function check_activation()
  {
    $config = Config::$config;

    if (!$config['license_key']) {
      self::activate_license(self::$hardcoded_license);
      return;
    }

    if (!$config['license_active']) {
      self::activate_license($config['license_key']);
    }
  }

  public static function update_license_status()
  {
    // Bypass the external API call and just set as active
    Config::update_config([
      'license_status' => 'active',
    ]);

    return true;
  }

  public static function license_notice()
  {
    // Don't show notice on FlyingPress page
    if (isset($_GET['page']) && $_GET['page'] === 'flying-press') {
      return;
    }

    $config = Config::$config;

    // Auto-activate if not active
    if (!$config['license_active'] || $config['license_status'] !== 'active') {
      self::auto_activate_license();
      return;
    }

    // No notices needed since we always keep the license active
  }
}
