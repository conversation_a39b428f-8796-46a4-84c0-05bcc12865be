<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInit1d25bd974b27601b0fab8e145a469eaf
{
    public static $prefixLengthsPsr4 = array (
        'W' => 
        array (
            'Wa72\\Url\\' => 9,
        ),
        'M' => 
        array (
            '<PERSON><PERSON><PERSON><PERSON>\\PathConverter\\' => 29,
            '<PERSON><PERSON><PERSON><PERSON>\\Minify\\' => 22,
        ),
        'F' => 
        array (
            'FlyingPress\\' => 12,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'Wa72\\Url\\' => 
        array (
            0 => __DIR__ . '/..' . '/wa72/url/src/Wa72/Url',
        ),
        '<PERSON><PERSON><PERSON><PERSON>\\PathConverter\\' => 
        array (
            0 => __DIR__ . '/..' . '/matthiasmullie/path-converter/src',
        ),
        '<PERSON><PERSON><PERSON><PERSON>\\Minify\\' => 
        array (
            0 => __DIR__ . '/..' . '/matthiasmullie/minify/src',
        ),
        'FlyingPress\\' => 
        array (
            0 => __DIR__ . '/../..' . '/src',
        ),
    );

    public static $classMap = array (
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInit1d25bd974b27601b0fab8e145a469eaf::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInit1d25bd974b27601b0fab8e145a469eaf::$prefixDirsPsr4;
            $loader->classMap = ComposerStaticInit1d25bd974b27601b0fab8e145a469eaf::$classMap;

        }, null, ClassLoader::class);
    }
}
