{"packages": [{"name": "matthi<PERSON><PERSON><PERSON>/minify", "version": "1.3.73", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/matthiasmullie/minify.git", "reference": "cb7a9297b4ab070909cefade30ee95054d4ae87a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/matthiasmullie/minify/zipball/cb7a9297b4ab070909cefade30ee95054d4ae87a", "reference": "cb7a9297b4ab070909cefade30ee95054d4ae87a", "shasum": ""}, "require": {"ext-pcre": "*", "matthiasmullie/path-converter": "~1.1", "php": ">=5.3.0"}, "require-dev": {"friendsofphp/php-cs-fixer": ">=2.0", "matthiasmullie/scrapbook": ">=1.3", "phpunit/phpunit": ">=4.8", "squizlabs/php_codesniffer": ">=3.0"}, "suggest": {"psr/cache-implementation": "Cache implementation to use with Minify::cache"}, "time": "2024-03-15T10:27:10+00:00", "bin": ["bin/minifycss", "bin/minifyjs"], "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"MatthiasMullie\\Minify\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.mullie.eu", "role": "Developer"}], "description": "CSS & JavaScript minifier, in PHP. Removes whitespace, strips comments, combines files (incl. @import statements and small assets in CSS files), and optimizes/shortens a few common programming patterns.", "homepage": "https://github.com/matthiasmullie/minify", "keywords": ["JS", "css", "javascript", "minifier", "minify"], "support": {"issues": "https://github.com/matthiasmullie/minify/issues", "source": "https://github.com/matthiasmullie/minify/tree/1.3.73"}, "funding": [{"url": "https://github.com/matthiasmullie", "type": "github"}], "install-path": "../matthiasmullie/minify"}, {"name": "mat<PERSON><PERSON><PERSON><PERSON>/path-converter", "version": "1.1.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/matthiasmullie/path-converter.git", "reference": "e7d13b2c7e2f2268e1424aaed02085518afa02d9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/matthiasmullie/path-converter/zipball/e7d13b2c7e2f2268e1424aaed02085518afa02d9", "reference": "e7d13b2c7e2f2268e1424aaed02085518afa02d9", "shasum": ""}, "require": {"ext-pcre": "*", "php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "~4.8"}, "time": "2019-02-05T23:41:09+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"MatthiasMullie\\PathConverter\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.mullie.eu", "role": "Developer"}], "description": "Relative path converter", "homepage": "http://github.com/matthiasmullie/path-converter", "keywords": ["converter", "path", "paths", "relative"], "support": {"issues": "https://github.com/matthiasmullie/path-converter/issues", "source": "https://github.com/matthiasmullie/path-converter/tree/1.1.3"}, "install-path": "../matthias<PERSON><PERSON>/path-converter"}, {"name": "wa72/url", "version": "v0.7.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/wasinger/url.git", "reference": "9ae182a0e3408ca8956186eafbbc497144d27d44"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/wasinger/url/zipball/9ae182a0e3408ca8956186eafbbc497144d27d44", "reference": "9ae182a0e3408ca8956186eafbbc497144d27d44", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"phpunit/phpunit": "^4|^5|^6|^7", "psr/http-message": "^1.0"}, "suggest": {"psr/http-message": "For using the Psr7Uri class implementing Psr\\Http\\Message\\UriInterface"}, "time": "2018-07-25T15:54:06+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Wa72\\Url\\": "src/Wa72/Url"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.webagentur72.de"}], "description": "Class for handling and manipulating URLs", "homepage": "http://github.com/wasinger/url", "support": {"issues": "https://github.com/wasinger/url/issues", "source": "https://github.com/wasinger/url/tree/master"}, "install-path": "../wa72/url"}], "dev": true, "dev-package-names": []}