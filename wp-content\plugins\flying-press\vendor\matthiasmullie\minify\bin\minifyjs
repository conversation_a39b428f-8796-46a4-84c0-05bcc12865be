#!/usr/bin/env php
<?php
use <PERSON><PERSON><PERSON><PERSON>\Minify;

// command line utility to minify JS
if (file_exists(__DIR__ . '/../../../autoload.php')) {
    // if composer install
    require_once __DIR__ . '/../../../autoload.php';
} else {
    require_once __DIR__ . '/../src/Minify.php';
    require_once __DIR__ . '/../src/JS.php';
    require_once __DIR__ . '/../src/Exception.php';
}

error_reporting(E_ALL);
// check PHP setup for cli arguments
if (!isset($_SERVER['argv']) && !isset($argv)) {
    fwrite(STDERR, 'Please enable the "register_argc_argv" directive in your php.ini' . PHP_EOL);
    exit(1);
} elseif (!isset($argv)) {
    $argv = $_SERVER['argv'];
}
// check if path to file given
if (!isset($argv[1])) {
    fwrite(STDERR, 'Argument expected: path to file' . PHP_EOL);
    exit(1);
}
// check if script run in cli environment
if ('cli' !== php_sapi_name()) {
    fwrite(STDERR, $argv[1] . ' must be run in the command line' . PHP_EOL);
    exit(1);
}
// check if source file exists
if (!file_exists($argv[1])) {
    fwrite(STDERR, 'Source file "' . $argv[1] . '" not found' . PHP_EOL);
    exit(1);
}

try {
    $minifier = new Minify\JS($argv[1]);
    echo $minifier->minify();
} catch (Exception $e) {
    fwrite(STDERR, $e->getMessage(), PHP_EOL);
    exit(1);
}
