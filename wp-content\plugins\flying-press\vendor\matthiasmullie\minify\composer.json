{"name": "matthi<PERSON><PERSON><PERSON>/minify", "type": "library", "description": "CSS & JavaScript minifier, in PHP. Removes whitespace, strips comments, combines files (incl. @import statements and small assets in CSS files), and optimizes/shortens a few common programming patterns.", "keywords": ["minify", "minifier", "css", "js", "javascript"], "homepage": "https://github.com/matthiasmullie/minify", "license": "MIT", "authors": [{"name": "<PERSON>", "homepage": "https://www.mullie.eu", "email": "<EMAIL>", "role": "Developer"}], "require": {"php": ">=5.3.0", "ext-pcre": "*", "matthiasmullie/path-converter": "~1.1"}, "require-dev": {"friendsofphp/php-cs-fixer": ">=2.0", "matthiasmullie/scrapbook": ">=1.3", "phpunit/phpunit": ">=4.8", "squizlabs/php_codesniffer": ">=3.0"}, "suggest": {"psr/cache-implementation": "Cache implementation to use with Minify::cache"}, "autoload": {"psr-4": {"MatthiasMullie\\Minify\\": "src/"}}, "autoload-dev": {"psr-4": {"MatthiasMullie\\Minify\\Tests\\": "tests/"}}, "bin": ["bin/minifycss", "bin/minifyjs"]}