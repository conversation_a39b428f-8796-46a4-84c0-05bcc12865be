{"name": "wa72/url", "description": "Class for handling and manipulating URLs", "homepage": "http://github.com/wasinger/url", "type": "library", "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.webagentur72.de"}], "autoload": {"psr-4": {"Wa72\\Url\\": "src/Wa72/Url"}}, "autoload-dev": {"psr-4": {"Wa72\\Url\\Tests\\": "tests"}}, "require": {"php": ">=5.6"}, "suggest": {"psr/http-message": "For using the Psr7Uri class implementing Psr\\Http\\Message\\UriInterface"}, "require-dev": {"phpunit/phpunit": "^4|^5|^6|^7", "psr/http-message": "^1.0"}}