# SnapCRE WordPress Plugin

A comprehensive WordPress plugin for managing commercial real estate properties with advanced filtering, mapping, and agent management capabilities.

## Features

### Property Management
- Custom post types for Properties, Agents, Agreements, Contacts, Download Requests, and Document Vault
- Comprehensive property details including:
  - Property types and transaction types
  - Address and location coordinates
  - Building specifications (size, class, lease rate, year built)
  - Photo galleries and document management
  - Demographics information
  - Confidentiality agreements

### Agent Management
- Agent profiles with contact information
- Bio and profile management
- Property-agent relationships
- Contact forms for agent inquiries

### Frontend Features
- Property filtering and search
- Interactive maps with Leaflet.js
- Responsive property grid layouts
- Property detail pages with tabbed interface
- Agent profile pages
- Multiple shortcodes for flexible display

### Admin Features
- Intuitive admin interface with tabbed metaboxes
- Property and transaction type management
- Settings panel for configuration
- Media upload integration
- Form validation and auto-save

## Installation

1. Upload the `snap-cre` folder to your `/wp-content/plugins/` directory
2. Activate the plugin through the 'Plugins' menu in WordPress
3. Configure property and transaction types in SnapCRE > Settings
4. Start adding properties and agents

## Shortcodes

### [snapcre_properties]
Display properties with optional filters and map.

**Attributes:**
- `property_types` - Comma-separated property type IDs to filter by
- `transaction_types` - Comma-separated transaction type IDs to filter by
- `show_filters` - Show filter form (true/false, default: true)
- `show_map` - Show properties map (true/false, default: false)
- `posts_per_page` - Number of properties to show (-1 for all, default: -1)

**Examples:**
```
[snapcre_properties]
[snapcre_properties property_types="1,2,3"]
[snapcre_properties transaction_types="1,2" show_map="true"]
[snapcre_properties property_types="1" show_filters="false"]
```

### [snapcre_property_filters]
Display only the property filter form.

**Attributes:**
- `target` - CSS selector for results container (default: #snap-cre-results)

**Examples:**
```
[snapcre_property_filters]
[snapcre_property_filters target="#my-results"]
```

### [snapcre_property_grid]
Display only the properties grid without filters.

**Attributes:**
- `property_types` - Comma-separated property type IDs to filter by
- `transaction_types` - Comma-separated transaction type IDs to filter by
- `posts_per_page` - Number of properties to show (-1 for all, default: -1)
- `columns` - Number of columns in grid (default: 3)

**Examples:**
```
[snapcre_property_grid]
[snapcre_property_grid property_types="1,2" columns="4"]
[snapcre_property_grid transaction_types="1" posts_per_page="6"]
```

## Template Customization

You can override plugin templates by copying them to your theme:

1. Create a `snap-cre` folder in your active theme directory
2. Copy template files from `wp-content/plugins/snap-cre/templates/` to your theme's `snap-cre/` folder
3. Customize the templates as needed

Available templates:
- `single-property.php` - Single property page template
- `single-agent.php` - Single agent page template

## Hooks and Filters

### Actions
- `snap_cre_log_activity` - Log plugin activities
- `snap_cre_get_template_part_{$slug}` - Before loading template parts

### Filters
- `snap_cre_property_card_html` - Modify property card HTML
- `snap_cre_agent_contact_form` - Modify agent contact form

## Requirements

- WordPress 4.7 or higher
- PHP 7.4 or higher
- Modern browser with JavaScript enabled

## Architecture

This plugin follows modern WordPress development practices:

- **PSR-4 Autoloading**: Clean namespace structure with `SnapCRE\` namespace
- **Modular Design**: Separate classes for different functionality
- **Static Initialization**: Each class has an `init()` method for clean setup
- **Template System**: Flexible template loading with theme override support
- **AJAX Integration**: Real-time filtering and interactions
- **Responsive Design**: Mobile-first CSS approach

## File Structure

```
snap-cre/
├── snap-cre.php              # Main plugin file
├── composer.json             # Composer configuration
├── src/                      # PHP classes
│   ├── Config.php           # Configuration management
│   ├── PostTypes.php        # Custom post types
│   ├── MetaBoxes.php        # Admin metaboxes
│   ├── Dashboard.php        # Admin interface
│   ├── PublicFrontend.php   # Public-facing functionality
│   ├── Shortcodes.php       # Shortcode handlers
│   ├── Templates.php        # Template loading
│   └── Utils.php            # Utility functions
├── templates/               # Template files
│   ├── single-property.php
│   └── single-agent.php
├── assets/                  # CSS/JS/Images
│   ├── admin.css
│   ├── admin.js
│   ├── public.css
│   ├── public.js
│   └── images/
└── vendor/                  # Composer dependencies
```

## Support

For support and feature requests, please contact the development team.

## License

This plugin is licensed under the GPL v2 or later.

## Changelog

### 1.0.0
- Initial release
- Property and agent management
- Frontend filtering and display
- Map integration
- Shortcode system
- Admin interface
