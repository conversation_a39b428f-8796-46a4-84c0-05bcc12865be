/* SnapCRE Admin Styles - Based on original fcre-properties */

/* Tab Header */
.fcre-tab-header h2 {
    margin-top: 0;
    font-size: 24px;
    font-weight: bold;
}

.fcre-tab-header p {
    color: #666;
    margin-bottom: 20px;
}

/* Tab Section Layout */
.fcre-tab-section {
    display: flex;
    gap: 20px;
}

.fcre-tab-sidebar {
    flex: 0 0 250px;
}

.fcre-tab-content {
    flex: 1;
}

/* Tab Navigation */
.fcre-tabs {
    flex-direction: column !important;
}

.fcre-tabs .nav-tab {
    display: block;
    margin: 0 0 5px 0;
    border-radius: 4px;
    text-align: left;
}

/* Settings Cards */
.fcre-settings-card {
    background: #fff;
    border: 1px solid #ddd;
    padding: 20px;
    margin-bottom: 20px;
    border-radius: 4px;
}

.fcre-settings-card .card-title {
    margin-top: 0;
    margin-bottom: 5px;
    font-size: 18px;
    font-weight: bold;
}

/* Types Table */
.fcre-types-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 15px;
}

.fcre-types-table th,
.fcre-types-table td {
    padding: 12px 8px;
    border-bottom: 1px solid #eee;
    vertical-align: middle;
}

.fcre-types-table tbody tr:last-child td {
    border-bottom: none;
}

.fcre-types-table input[type="text"] {
    width: 90%;
    padding: 8px;
    font-size: 14px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

/* Drag Handler */
.drag-handler {
    cursor: move;
    position: relative;
}

.move-handle {
    display: inline-block;
    width: 20px;
    height: 20px;
    background: #f0f0f0;
    border: 1px solid #ddd;
    border-radius: 3px;
    margin-right: 10px;
    vertical-align: middle;
    cursor: move;
}

.move-handle:before {
    content: "⋮⋮";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 12px;
    color: #666;
    line-height: 1;
}

/* Row Remove */
.row-remove {
    text-align: center;
    width: 40px;
}

.row-remove span {
    cursor: pointer;
    color: #dc3232;
    font-size: 16px;
    padding: 5px;
    border-radius: 3px;
    transition: background-color 0.2s;
}

.row-remove span:hover {
    background-color: #f0f0f0;
}

.icon-trash:before {
    content: "🗑";
}

/* Add More Button */
.fcre-btn-add-more {
    background: #0073aa;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
}

.fcre-btn-add-more:hover {
    background: #005a87;
}

/* Sortable UI */
.ui-state-highlight {
    height: 50px;
    background: #f0f8ff;
    border: 2px dashed #0073aa;
}

.ui-sortable-helper {
    background: #fff;
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
}

/* Tab Content Header */
.tab-content-header {
    margin-bottom: 30px;
}

.tab-content-header h2 {
    margin-top: 0;
    font-size: 24px;
    font-weight: bold;
}

.tab-content-header input[type="submit"] {
    margin-top: 10px;
}

/* Metabox Styles */
.snap-cre-metabox-tabs {
    margin: 20px 0;
}

.snap-cre-tab-nav {
    list-style: none;
    margin: 0;
    padding: 0;
    border-bottom: 1px solid #ccc;
}

.snap-cre-tab-nav li {
    display: inline-block;
    margin: 0;
}

.snap-cre-tab-nav li a {
    display: block;
    padding: 10px 15px;
    text-decoration: none;
    border: 1px solid transparent;
    border-bottom: none;
    background: #f1f1f1;
    color: #333;
}

.snap-cre-tab-nav li.active a {
    background: #fff;
    border-color: #ccc;
    border-bottom: 1px solid #fff;
    margin-bottom: -1px;
    color: #007cba;
}

.snap-cre-tab-pane {
    display: none;
    padding: 20px 0;
}

.snap-cre-tab-pane.active {
    display: block;
}

.snap-cre-field {
    margin-bottom: 15px;
}

.snap-cre-field label {
    display: block;
    font-weight: bold;
    margin-bottom: 5px;
}

.snap-cre-field input,
.snap-cre-field textarea,
.snap-cre-field select {
    width: 100%;
    max-width: 400px;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.snap-cre-field textarea {
    min-height: 100px;
    resize: vertical;
}

.snap-cre-field select[multiple] {
    height: 120px;
}

.snap-cre-field input[type="checkbox"] {
    width: auto;
    margin-right: 8px;
}

/* Multiselect styles */
.snap-cre-multiselect-wrapper {
    margin-bottom: 15px;
}

.snap-cre-multiselect-label {
    display: block;
    font-weight: bold;
    margin-bottom: 5px;
}

.snap-cre-multiselect-dropdown {
    position: relative;
    display: inline-block;
    width: 100%;
    max-width: 400px;
}

.snap-cre-multiselect-toggle {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: white;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.snap-cre-multiselect-options {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #ddd;
    border-top: none;
    border-radius: 0 0 4px 4px;
    max-height: 200px;
    overflow-y: auto;
    z-index: 1000;
}

.option-item {
    padding: 8px 12px;
    border-bottom: 1px solid #eee;
    display: flex;
    align-items: center;
}

.option-item:last-child {
    border-bottom: none;
}

.option-item:hover {
    background: #f5f5f5;
}

.option-item input[type="checkbox"] {
    margin-right: 8px;
}

.option-item label {
    cursor: pointer;
    flex: 1;
}

/* Responsive */
@media (max-width: 768px) {
    .snap-cre-field input,
    .snap-cre-field textarea,
    .snap-cre-field select {
        max-width: 100%;
    }
    
    .type-row {
        flex-direction: column;
        align-items: stretch;
    }
    
    .type-row input[type="text"] {
        width: 100%;
        margin-bottom: 10px;
    }
}
