/* SnapCRE Admin Styles */

.snap-cre-metabox-tabs {
    margin: 20px 0;
}

.snap-cre-tab-nav {
    list-style: none;
    margin: 0;
    padding: 0;
    border-bottom: 1px solid #ccc;
}

.snap-cre-tab-nav li {
    display: inline-block;
    margin: 0;
}

.snap-cre-tab-nav li a {
    display: block;
    padding: 10px 15px;
    text-decoration: none;
    border: 1px solid transparent;
    border-bottom: none;
    background: #f1f1f1;
    color: #333;
}

.snap-cre-tab-nav li.active a {
    background: #fff;
    border-color: #ccc;
    border-bottom: 1px solid #fff;
    margin-bottom: -1px;
    color: #007cba;
}

.snap-cre-tab-pane {
    display: none;
    padding: 20px 0;
}

.snap-cre-tab-pane.active {
    display: block;
}

.snap-cre-field {
    margin-bottom: 15px;
}

.snap-cre-field label {
    display: block;
    font-weight: bold;
    margin-bottom: 5px;
}

.snap-cre-field input,
.snap-cre-field textarea,
.snap-cre-field select {
    width: 100%;
    max-width: 400px;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.snap-cre-field textarea {
    min-height: 100px;
    resize: vertical;
}

.snap-cre-field select[multiple] {
    height: 120px;
}

.snap-cre-field input[type="checkbox"] {
    width: auto;
    margin-right: 8px;
}

/* Settings page styles */
.type-row {
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.type-row input[type="text"] {
    width: 300px;
}

.type-row .button {
    flex-shrink: 0;
}

#property-types-container,
#transaction-types-container {
    border: 1px solid #ddd;
    padding: 15px;
    border-radius: 4px;
    background: #f9f9f9;
    margin-bottom: 10px;
}

/* Multiselect styles */
.snap-cre-multiselect-wrapper {
    margin-bottom: 15px;
}

.snap-cre-multiselect-label {
    display: block;
    font-weight: bold;
    margin-bottom: 5px;
}

.snap-cre-multiselect-dropdown {
    position: relative;
    display: inline-block;
    width: 100%;
    max-width: 400px;
}

.snap-cre-multiselect-toggle {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: white;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.snap-cre-multiselect-options {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #ddd;
    border-top: none;
    border-radius: 0 0 4px 4px;
    max-height: 200px;
    overflow-y: auto;
    z-index: 1000;
}

.option-item {
    padding: 8px 12px;
    border-bottom: 1px solid #eee;
    display: flex;
    align-items: center;
}

.option-item:last-child {
    border-bottom: none;
}

.option-item:hover {
    background: #f5f5f5;
}

.option-item input[type="checkbox"] {
    margin-right: 8px;
}

.option-item label {
    cursor: pointer;
    flex: 1;
}

/* Responsive */
@media (max-width: 768px) {
    .snap-cre-field input,
    .snap-cre-field textarea,
    .snap-cre-field select {
        max-width: 100%;
    }
    
    .type-row {
        flex-direction: column;
        align-items: stretch;
    }
    
    .type-row input[type="text"] {
        width: 100%;
        margin-bottom: 10px;
    }
}
