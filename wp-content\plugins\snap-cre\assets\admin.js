/* SnapCRE Admin JavaScript */

jQuery(document).ready(function($) {
    'use strict';

    // Tab functionality for metaboxes
    $('.snap-cre-tab-nav a').on('click', function(e) {
        e.preventDefault();
        var target = $(this).attr('href');
        
        // Update nav
        $('.snap-cre-tab-nav li').removeClass('active');
        $(this).parent().addClass('active');
        
        // Update content
        $('.snap-cre-tab-pane').removeClass('active');
        $(target).addClass('active');
    });

    // Multiselect functionality
    $('.snap-cre-multiselect-toggle').on('click', function(e) {
        e.preventDefault();
        var $dropdown = $(this).siblings('.snap-cre-multiselect-options');
        
        // Close other dropdowns
        $('.snap-cre-multiselect-options').not($dropdown).hide();
        
        // Toggle current dropdown
        $dropdown.toggle();
    });

    // Close dropdowns when clicking outside
    $(document).on('click', function(e) {
        if (!$(e.target).closest('.snap-cre-multiselect-dropdown').length) {
            $('.snap-cre-multiselect-options').hide();
        }
    });

    // Handle "Select All" checkbox
    $('.select-all').on('change', function() {
        var $container = $(this).closest('.snap-cre-multiselect-options');
        var $checkboxes = $container.find('.option-checkbox');
        
        if ($(this).is(':checked')) {
            $checkboxes.prop('checked', true);
        } else {
            $checkboxes.prop('checked', false);
        }
        
        updateMultiselectText($container.closest('.snap-cre-multiselect-dropdown'));
    });

    // Handle individual checkbox changes
    $('.option-checkbox').on('change', function() {
        var $container = $(this).closest('.snap-cre-multiselect-options');
        var $selectAll = $container.find('.select-all');
        var $checkboxes = $container.find('.option-checkbox');
        var checkedCount = $checkboxes.filter(':checked').length;
        
        // Update "Select All" state
        if (checkedCount === 0) {
            $selectAll.prop('indeterminate', false).prop('checked', false);
        } else if (checkedCount === $checkboxes.length) {
            $selectAll.prop('indeterminate', false).prop('checked', true);
        } else {
            $selectAll.prop('indeterminate', true);
        }
        
        updateMultiselectText($container.closest('.snap-cre-multiselect-dropdown'));
    });

    function updateMultiselectText($dropdown) {
        var $toggle = $dropdown.find('.snap-cre-multiselect-toggle');
        var $selectedText = $toggle.find('.selected-text');
        var $checkboxes = $dropdown.find('.option-checkbox:checked');
        var placeholder = $selectedText.data('placeholder') || 'Select';
        
        if ($checkboxes.length === 0) {
            $selectedText.text(placeholder);
        } else if ($checkboxes.length === 1) {
            $selectedText.text($checkboxes.first().siblings('label').text());
        } else {
            $selectedText.text($checkboxes.length + ' items selected');
        }
    }

    // Initialize multiselect text
    $('.snap-cre-multiselect-dropdown').each(function() {
        updateMultiselectText($(this));
    });

    // Settings page: Add/Remove property types
    $('#add-property-type').on('click', function() {
        var container = $('#property-types-container');
        var index = container.find('.type-row').length;
        var newId = Date.now();
        var html = '<div class="type-row">' +
            '<input type="hidden" name="property_types[' + index + '][id]" value="' + newId + '">' +
            '<input type="text" name="property_types[' + index + '][name]" placeholder="Property Type Name">' +
            '<button type="button" class="button remove-type">Remove</button>' +
            '</div>';
        container.append(html);
    });

    // Settings page: Add/Remove transaction types
    $('#add-transaction-type').on('click', function() {
        var container = $('#transaction-types-container');
        var index = container.find('.type-row').length;
        var newId = Date.now();
        var html = '<div class="type-row">' +
            '<input type="hidden" name="transaction_types[' + index + '][id]" value="' + newId + '">' +
            '<input type="text" name="transaction_types[' + index + '][name]" placeholder="Transaction Type Name">' +
            '<button type="button" class="button remove-type">Remove</button>' +
            '</div>';
        container.append(html);
    });

    // Remove type row
    $(document).on('click', '.remove-type', function() {
        $(this).closest('.type-row').remove();
    });

    // Media uploader for images
    var mediaUploader;
    
    $('.upload-image-button').on('click', function(e) {
        e.preventDefault();
        
        var $button = $(this);
        var $input = $button.siblings('input[type="text"]');
        
        if (mediaUploader) {
            mediaUploader.open();
            return;
        }
        
        mediaUploader = wp.media({
            title: 'Choose Image',
            button: {
                text: 'Choose Image'
            },
            multiple: false
        });
        
        mediaUploader.on('select', function() {
            var attachment = mediaUploader.state().get('selection').first().toJSON();
            $input.val(attachment.url);
        });
        
        mediaUploader.open();
    });

    // Gallery management
    $('.add-gallery-item').on('click', function(e) {
        e.preventDefault();
        
        var $container = $(this).siblings('.gallery-items');
        var fieldName = $(this).data('field-name');
        var index = $container.find('.gallery-item').length;
        
        var html = '<div class="gallery-item">' +
            '<input type="text" name="' + fieldName + '[' + index + ']" placeholder="Image URL">' +
            '<button type="button" class="button upload-gallery-image">Upload</button>' +
            '<button type="button" class="button remove-gallery-item">Remove</button>' +
            '</div>';
        
        $container.append(html);
    });

    // Remove gallery item
    $(document).on('click', '.remove-gallery-item', function() {
        $(this).closest('.gallery-item').remove();
    });

    // Upload gallery image
    $(document).on('click', '.upload-gallery-image', function(e) {
        e.preventDefault();
        
        var $button = $(this);
        var $input = $button.siblings('input[type="text"]');
        
        var galleryUploader = wp.media({
            title: 'Choose Image',
            button: {
                text: 'Choose Image'
            },
            multiple: false
        });
        
        galleryUploader.on('select', function() {
            var attachment = galleryUploader.state().get('selection').first().toJSON();
            $input.val(attachment.url);
        });
        
        galleryUploader.open();
    });

    // Form validation
    $('form').on('submit', function(e) {
        var hasErrors = false;
        
        // Check required fields
        $(this).find('[required]').each(function() {
            if (!$(this).val().trim()) {
                $(this).addClass('error');
                hasErrors = true;
            } else {
                $(this).removeClass('error');
            }
        });
        
        if (hasErrors) {
            e.preventDefault();
            alert('Please fill in all required fields.');
        }
    });

    // Auto-save functionality (optional)
    var autoSaveTimer;
    
    function autoSave() {
        if (typeof snapCreAdmin !== 'undefined') {
            var formData = $('#post').serialize();
            
            $.ajax({
                url: snapCreAdmin.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'snap_cre_auto_save',
                    nonce: snapCreAdmin.nonce,
                    form_data: formData
                },
                success: function(response) {
                    if (response.success) {
                        console.log('Auto-saved successfully');
                    }
                }
            });
        }
    }
    
    // Trigger auto-save on form changes (debounced)
    $('#post').on('change input', function() {
        clearTimeout(autoSaveTimer);
        autoSaveTimer = setTimeout(autoSave, 5000); // Auto-save after 5 seconds of inactivity
    });
});
