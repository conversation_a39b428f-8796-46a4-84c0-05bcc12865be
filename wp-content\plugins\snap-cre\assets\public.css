/* SnapCRE Public Styles */

/* Filter Form Styles */
.snap-cre-filter-wrapper {
    background: #f9f9f9;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 30px;
}

.snap-cre-filter-form .filter-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    align-items: end;
}

.filter-field {
    display: flex;
    flex-direction: column;
}

.filter-field label {
    font-weight: bold;
    margin-bottom: 5px;
    color: #333;
}

.filter-field input,
.filter-field select {
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.filter-field select[multiple] {
    height: 80px;
}

.filter-submit,
.filter-reset {
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.3s ease;
}

.filter-submit {
    background: #007cba;
    color: white;
}

.filter-submit:hover {
    background: #005a87;
}

.filter-reset {
    background: #666;
    color: white;
    margin-left: 10px;
}

.filter-reset:hover {
    background: #444;
}

/* Properties Grid */
.snap-cre-results {
    margin-bottom: 30px;
}

.properties-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.snap-cre-property-grid.columns-2 .properties-grid {
    grid-template-columns: repeat(2, 1fr);
}

.snap-cre-property-grid.columns-3 .properties-grid {
    grid-template-columns: repeat(3, 1fr);
}

.snap-cre-property-grid.columns-4 .properties-grid {
    grid-template-columns: repeat(4, 1fr);
}

/* Property Card */
.snap-cre-property-card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.snap-cre-property-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.property-thumbnail {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.property-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.snap-cre-property-card:hover .property-thumbnail img {
    transform: scale(1.05);
}

.property-content {
    padding: 20px;
}

.property-title {
    margin: 0 0 10px 0;
    font-size: 1.2em;
    font-weight: bold;
}

.property-title a {
    color: #333;
    text-decoration: none;
}

.property-title a:hover {
    color: #007cba;
}

.property-address {
    color: #666;
    margin-bottom: 15px;
    font-size: 0.9em;
}

.property-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 15px;
}

.property-meta span {
    background: #e9ecef;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8em;
    color: #495057;
}

.property-details {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
    font-size: 0.9em;
    color: #666;
}

.property-link {
    display: inline-block;
    background: #007cba;
    color: white;
    padding: 8px 16px;
    text-decoration: none;
    border-radius: 4px;
    font-size: 0.9em;
    transition: background-color 0.3s ease;
}

.property-link:hover {
    background: #005a87;
    color: white;
}

/* No Properties Message */
.no-properties {
    text-align: center;
    padding: 40px;
    color: #666;
    font-style: italic;
}

/* Map Container */
.snap-cre-map-container {
    margin: 30px 0;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Property Popup in Map */
.property-popup {
    max-width: 200px;
}

.property-popup h4 {
    margin: 0 0 10px 0;
    font-size: 1em;
}

.property-popup h4 a {
    color: #007cba;
    text-decoration: none;
}

.property-popup p {
    margin: 5px 0;
    font-size: 0.9em;
    color: #666;
}

.property-popup img {
    margin-top: 10px;
    border-radius: 4px;
}

/* Loading States */
.snap-cre-loading {
    text-align: center;
    padding: 40px;
}

.snap-cre-loading::after {
    content: '';
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007cba;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .snap-cre-filter-form .filter-row {
        grid-template-columns: 1fr;
    }
    
    .properties-grid {
        grid-template-columns: 1fr;
    }
    
    .snap-cre-property-grid.columns-2 .properties-grid,
    .snap-cre-property-grid.columns-3 .properties-grid,
    .snap-cre-property-grid.columns-4 .properties-grid {
        grid-template-columns: 1fr;
    }
    
    .property-details {
        flex-direction: column;
        gap: 5px;
    }
    
    .filter-submit,
    .filter-reset {
        width: 100%;
        margin: 5px 0;
    }
}

@media (max-width: 480px) {
    .snap-cre-filter-wrapper,
    .property-content {
        padding: 15px;
    }
    
    .property-thumbnail {
        height: 150px;
    }
}

/* Accessibility */
.snap-cre-property-card:focus-within {
    outline: 2px solid #007cba;
    outline-offset: 2px;
}

.filter-submit:focus,
.filter-reset:focus,
.property-link:focus {
    outline: 2px solid #007cba;
    outline-offset: 2px;
}

/* Print Styles */
@media print {
    .snap-cre-filter-wrapper,
    .snap-cre-map-container {
        display: none;
    }
    
    .snap-cre-property-card {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #ddd;
    }
    
    .property-link {
        display: none;
    }
}
