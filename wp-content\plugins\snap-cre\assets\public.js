/* SnapCRE Public JavaScript */

(function($) {
    'use strict';

    $(document).ready(function() {
        var SNAP_CRE = {
            init: function() {
                this.cacheDOM();
                this.bindEvents();
                this.initializeFilters();
                this.initializeTabs();
                this.initializeMap();
            },

            cacheDOM: function() {
                this.$filterWrapper = $('.snap-cre-filter-wrapper');
                this.$filterForm = $('#snap-cre-filter-form');
                this.$resultsWrapper = $('#snap-cre-results');
                this.$propertiesGrid = $('.properties-grid');
            },

            bindEvents: function() {
                // Filter form submission
                this.$filterForm.on('submit', this.handleFilterSubmit.bind(this));
                
                // Reset button
                $('.filter-reset').on('click', this.resetFilters.bind(this));
                
                // Real-time filtering (optional)
                this.$filterForm.find('input, select').on('change', this.debounce(this.handleFilterChange.bind(this), 500));
                
                // Property card interactions
                $(document).on('click', '.snap-cre-property-card', this.handlePropertyCardClick.bind(this));
            },

            initializeFilters: function() {
                // Initialize any custom filter components
                this.initializeMultiselect();
            },

            initializeMultiselect: function() {
                $('.snap-cre-multiselect-toggle').on('click', function(e) {
                    e.preventDefault();
                    var $dropdown = $(this).siblings('.snap-cre-multiselect-options');
                    
                    // Close other dropdowns
                    $('.snap-cre-multiselect-options').not($dropdown).hide();
                    
                    // Toggle current dropdown
                    $dropdown.toggle();
                });

                // Close dropdowns when clicking outside
                $(document).on('click', function(e) {
                    if (!$(e.target).closest('.snap-cre-multiselect-dropdown').length) {
                        $('.snap-cre-multiselect-options').hide();
                    }
                });

                // Handle checkbox changes
                $('.option-checkbox').on('change', function() {
                    var $dropdown = $(this).closest('.snap-cre-multiselect-dropdown');
                    SNAP_CRE.updateMultiselectText($dropdown);
                });
            },

            updateMultiselectText: function($dropdown) {
                var $toggle = $dropdown.find('.snap-cre-multiselect-toggle');
                var $selectedText = $toggle.find('.selected-text');
                var $checkboxes = $dropdown.find('.option-checkbox:checked');
                var placeholder = $selectedText.data('placeholder') || 'Select';
                
                if ($checkboxes.length === 0) {
                    $selectedText.text(placeholder);
                } else if ($checkboxes.length === 1) {
                    $selectedText.text($checkboxes.first().siblings('label').text());
                } else {
                    $selectedText.text($checkboxes.length + ' items selected');
                }
            },

            initializeTabs: function() {
                $('.tab-btn').on('click', function(e) {
                    e.preventDefault();
                    var target = $(this).data('tab');
                    
                    // Update navigation
                    $('.tab-btn').removeClass('active');
                    $(this).addClass('active');
                    
                    // Update content
                    $('.tab-pane').removeClass('active');
                    $('#' + target).addClass('active');
                });
            },

            initializeMap: function() {
                // Initialize maps if Leaflet is available
                if (typeof L !== 'undefined') {
                    this.initializePropertyMaps();
                    this.initializeListingMap();
                }
            },

            initializePropertyMaps: function() {
                // Initialize individual property maps
                $('#property-map').each(function() {
                    var $map = $(this);
                    var lat = $map.data('lat');
                    var lng = $map.data('lng');
                    var title = $map.data('title');
                    var address = $map.data('address');
                    
                    if (lat && lng) {
                        var map = L.map(this).setView([lat, lng], 15);
                        
                        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                            attribution: '© OpenStreetMap contributors'
                        }).addTo(map);

                        var marker = L.marker([lat, lng]).addTo(map);
                        if (title && address) {
                            marker.bindPopup('<strong>' + title + '</strong><br>' + address);
                        }
                    }
                });
            },

            initializeListingMap: function() {
                // Initialize map for property listings
                $('#snap-cre-properties-map').each(function() {
                    // Map initialization is handled in the shortcode template
                });
            },

            handleFilterSubmit: function(e) {
                e.preventDefault();
                this.filterProperties();
            },

            handleFilterChange: function() {
                // Auto-filter on change (if enabled)
                this.filterProperties();
            },

            filterProperties: function() {
                var formData = this.$filterForm.serialize();
                
                // Show loading state
                this.showLoading();
                
                $.ajax({
                    url: snapCrePublic.admin_ajax,
                    type: 'POST',
                    data: {
                        action: 'snap_cre_filter_properties',
                        nonce: snapCrePublic.nonce,
                        ...this.getFormData()
                    },
                    success: this.handleFilterSuccess.bind(this),
                    error: this.handleFilterError.bind(this)
                });
            },

            getFormData: function() {
                var data = {};
                
                // Get search term
                data.search_term = $('#search-term').val();
                
                // Get selected property types
                data.property_types = [];
                $('#property-types option:selected').each(function() {
                    data.property_types.push($(this).val());
                });
                
                // Get selected transaction types
                data.transaction_types = [];
                $('#transaction-types option:selected').each(function() {
                    data.transaction_types.push($(this).val());
                });
                
                return data;
            },

            handleFilterSuccess: function(response) {
                this.hideLoading();
                
                if (response.success) {
                    this.updateResults(response.data.properties);
                    this.updateResultsCount(response.data.count);
                } else {
                    this.showError('Failed to filter properties. Please try again.');
                }
            },

            handleFilterError: function() {
                this.hideLoading();
                this.showError('An error occurred while filtering properties. Please try again.');
            },

            updateResults: function(properties) {
                var html = '';
                
                if (properties.length > 0) {
                    properties.forEach(function(property) {
                        html += SNAP_CRE.renderPropertyCard(property);
                    });
                } else {
                    html = '<p class="no-properties">No properties found matching your criteria.</p>';
                }
                
                this.$propertiesGrid.html(html);
            },

            renderPropertyCard: function(property) {
                var thumbnail = property.thumbnail ? 
                    '<div class="property-thumbnail"><img src="' + property.thumbnail + '" alt="' + property.title + '"></div>' : '';
                
                var address = property.address ? 
                    '<p class="property-address">' + property.address + '</p>' : '';
                
                var propertyTypes = property.property_types ? 
                    '<span class="property-types">' + property.property_types + '</span>' : '';
                
                var transactionTypes = property.transaction_types ? 
                    '<span class="transaction-types">' + property.transaction_types + '</span>' : '';
                
                var buildingSize = property.building_size ? 
                    '<span class="building-size">' + property.building_size + ' sq ft</span>' : '';
                
                var leaseRate = property.lease_rate ? 
                    '<span class="lease-rate">$' + property.lease_rate + '</span>' : '';
                
                return '<div class="snap-cre-property-card" data-property-id="' + property.id + '">' +
                    thumbnail +
                    '<div class="property-content">' +
                        '<h3 class="property-title"><a href="' + property.permalink + '">' + property.title + '</a></h3>' +
                        address +
                        '<div class="property-meta">' + propertyTypes + transactionTypes + '</div>' +
                        '<div class="property-details">' + buildingSize + leaseRate + '</div>' +
                        '<a href="' + property.permalink + '" class="property-link">View Details</a>' +
                    '</div>' +
                '</div>';
            },

            updateResultsCount: function(count) {
                var countText = count === 1 ? '1 property found' : count + ' properties found';
                $('.results-count').text(countText);
            },

            resetFilters: function(e) {
                e.preventDefault();
                
                // Reset form
                this.$filterForm[0].reset();
                
                // Reset multiselects
                $('.option-checkbox').prop('checked', false);
                $('.snap-cre-multiselect-dropdown').each(function() {
                    SNAP_CRE.updateMultiselectText($(this));
                });
                
                // Reload all properties
                this.filterProperties();
            },

            handlePropertyCardClick: function(e) {
                // Handle property card interactions
                if (!$(e.target).is('a')) {
                    var $card = $(e.currentTarget);
                    var propertyUrl = $card.find('.property-title a').attr('href');
                    if (propertyUrl) {
                        window.location.href = propertyUrl;
                    }
                }
            },

            showLoading: function() {
                this.$resultsWrapper.addClass('snap-cre-loading');
                this.$propertiesGrid.html('<p>Loading properties...</p>');
            },

            hideLoading: function() {
                this.$resultsWrapper.removeClass('snap-cre-loading');
            },

            showError: function(message) {
                this.$propertiesGrid.html('<p class="error-message">' + message + '</p>');
            },

            debounce: function(func, wait) {
                var timeout;
                return function executedFunction() {
                    var context = this;
                    var args = arguments;
                    var later = function() {
                        timeout = null;
                        func.apply(context, args);
                    };
                    clearTimeout(timeout);
                    timeout = setTimeout(later, wait);
                };
            }
        };

        // Initialize the application
        SNAP_CRE.init();

        // Make SNAP_CRE available globally for debugging
        window.SNAP_CRE = SNAP_CRE;
    });

})(jQuery);
