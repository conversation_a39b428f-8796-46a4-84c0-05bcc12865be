<?php

/**
 * Plugin Name: SnapCRE
 * Plugin URI: https://focusedcre.com
 * Description: WordPress powered custom properties database system, you can add as many properties you want, commercial or residential types supported.
 * Version: 2.0.0
 * Requires PHP: 7.4
 * Requires at least: 4.7
 * Author: Focused CRE
 * Author URI: https://focusedcre.com
 * License: GPLv2 or later
 * License URI: http://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: snap-cre
 * Domain Path: /languages
 */

defined('ABSPATH') or die('No script kiddies please!');

// Plugin constants
define('SNAP_CRE_VERSION', '1.0.0');
define('SNAP_CRE_FILE', __FILE__);
define('SNAP_CRE_FILE_NAME', plugin_basename(__FILE__));
define('SNAP_CRE_PLUGIN_URL', plugin_dir_url(__FILE__));
define('SNAP_CRE_PLUGIN_DIR', plugin_dir_path(__FILE__));

// Autoload classes
require_once dirname(__FILE__) . '/vendor/autoload.php';

// Initialize the plugin
SnapCRE\Config::init();
SnapCRE\PostTypes::init();
SnapCRE\MetaBoxes::init();
SnapCRE\Dashboard::init();
SnapCRE\PublicFrontend::init();
SnapCRE\Shortcodes::init();
SnapCRE\Templates::init();

// Activation and deactivation hooks
register_activation_hook(__FILE__, ['SnapCRE\Config', 'on_activation']);
register_deactivation_hook(__FILE__, ['SnapCRE\Config', 'on_deactivation']);
register_uninstall_hook(__FILE__, ['SnapCRE\Config', 'on_uninstall']);
