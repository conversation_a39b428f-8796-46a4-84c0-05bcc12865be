<?php

namespace SnapCRE;

class Config
{
    public static $config = [];
    
    // Plugin configuration
    public static $plugin_name = 'snap-cre';
    public static $version = '1.0.0';
    
    // Custom post type slugs
    public static $properties_post_slug = 'properties';
    public static $agents_post_slug = 'agents';
    public static $agreements_post_slug = 'agreements';
    public static $contacts_post_slug = 'contacts';
    public static $download_request_post_slug = 'download-request';
    public static $document_vault_post_slug = 'document-vault';
    
    // Email settings
    public static $email_from_name = 'SnapCRE Properties';
    public static $email_from_email = '<EMAIL>';
    public static $email_bcc = '';
    public static $email_cc = '';

    public static function init()
    {
        // Get the saved configuration from the database
        self::$config = get_option('SNAP_CRE_CONFIG', []);
        
        // Set version
        if (defined('SNAP_CRE_VERSION')) {
            self::$version = SNAP_CRE_VERSION;
        }
        
        // Initialize default config if empty
        if (empty(self::$config)) {
            self::set_default_config();
        }
        
        // Update version if needed
        $saved_version = get_option('SNAP_CRE_VERSION');
        $current_version = SNAP_CRE_VERSION;
        
        if ($saved_version !== $current_version) {
            update_option('SNAP_CRE_VERSION', $current_version);
            self::migrate_config();
        }
    }
    
    public static function set_default_config()
    {
        $default_config = [
            'property_types' => [
                ['id' => 1, 'name' => 'Office'],
                ['id' => 2, 'name' => 'Retail'],
                ['id' => 3, 'name' => 'Industrial'],
                ['id' => 4, 'name' => 'Land'],
                ['id' => 5, 'name' => 'Multi-Family'],
            ],
            'transaction_types' => [
                ['id' => 1, 'name' => 'For Sale'],
                ['id' => 2, 'name' => 'For Lease'],
                ['id' => 3, 'name' => 'Sold'],
                ['id' => 4, 'name' => 'Leased'],
            ],
        ];
        
        // Save to options
        update_option('snap_cre_property_types', $default_config['property_types']);
        update_option('snap_cre_transaction_types', $default_config['transaction_types']);
        
        self::$config = $default_config;
        update_option('SNAP_CRE_CONFIG', self::$config);
    }
    
    public static function migrate_config()
    {
        // Handle any config migrations between versions
        // This is where we'd handle upgrading from older versions
    }
    
    public static function get_option($key, $default = '')
    {
        return get_option('snap_cre_' . $key, $default);
    }
    
    public static function update_option($key, $value)
    {
        return update_option('snap_cre_' . $key, $value);
    }
    
    public static function on_activation()
    {
        // Create database tables if needed
        self::create_tables();
        
        // Set default options
        self::set_default_config();
        
        // Flush rewrite rules
        flush_rewrite_rules();
    }
    
    public static function on_deactivation()
    {
        // Flush rewrite rules
        flush_rewrite_rules();
    }
    
    public static function on_uninstall()
    {
        // Clean up options
        delete_option('SNAP_CRE_CONFIG');
        delete_option('SNAP_CRE_VERSION');
        delete_option('snap_cre_property_types');
        delete_option('snap_cre_transaction_types');
        
        // Remove custom tables if needed
        self::drop_tables();
    }
    
    private static function create_tables()
    {
        // Create any custom database tables if needed
        // For now, we'll use WordPress meta tables
    }
    
    private static function drop_tables()
    {
        // Drop custom tables if they exist
    }
}
