<?php

namespace SnapCRE;

class Dashboard
{
    private static $tabs = [
        'snap-cre-main' => 'Property & Transaction Types',
        // Future tabs can be uncommented as needed
        // 'snap-cre-filter' => 'Filters, Views and Sorting',
        // 'snap-cre-thumbnail-fields' => 'Thumbnail Fields',
        // 'snap-cre-confidentiality-agreement' => 'Confidentiality Agreement',
        // 'snap-cre-ca' => 'Property Detail Page',
        // 'snap-cre-formbuilder' => 'Form Builder',
        // 'snap-cre-email' => 'Admin Email and Form Selector',
        // 'snap-cre-sharing' => 'Sharing',
        // 'snap-cre-pagination' => 'Pagination',
        // 'snap-cre-registration' => 'User Registration',
        // 'snap-cre-general-settings' => 'General Settings',
        // 'snap-cre-widget' => 'Search Widget',
        // 'snap-cre-shortcode' => 'Shortcodes',
        // 'snap-cre-map' => 'Map & reCAPTCHA',
        // 'snap-cre-api' => 'API',
        // 'snap-cre-colors' => 'Colors',
        // 'snap-cre-branding' => 'Branding',
    ];

    private static $active_tab;

    public static function init()
    {
        add_action('admin_menu', [__CLASS__, 'add_menu']);
        add_action('admin_enqueue_scripts', [__CLASS__, 'enqueue_admin_assets']);
    }

    public static function add_menu()
    {
        if (!current_user_can('edit_posts')) {
            return;
        }

        // Main menu page
        add_menu_page(
            'SnapCRE',
            'SnapCRE',
            'edit_posts',
            Config::$plugin_name,
            '__return_false', // No callback, just a parent menu
            'dashicons-building',
            '12'
        );

        // Settings submenu
        add_submenu_page(
            Config::$plugin_name,
            'Settings',
            'Settings',
            'manage_options',
            Config::$plugin_name . '-settings',
            [__CLASS__, 'render_settings_page']
        );
    }

    public static function enqueue_admin_assets($hook)
    {
        // Only load on our admin pages
        if (strpos($hook, Config::$plugin_name) === false) {
            return;
        }

        // Enqueue jQuery UI for sortable functionality
        wp_enqueue_script('jquery-ui-sortable');

        wp_enqueue_style(
            'snap-cre-admin',
            SNAP_CRE_PLUGIN_URL . 'assets/admin.css',
            [],
            SNAP_CRE_VERSION
        );

        wp_enqueue_script(
            'snap-cre-admin',
            SNAP_CRE_PLUGIN_URL . 'assets/admin.js',
            ['jquery', 'jquery-ui-sortable'],
            SNAP_CRE_VERSION,
            true
        );

        wp_localize_script('snap-cre-admin', 'snapCreAdmin', [
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('snap_cre_admin_nonce'),
        ]);
    }

    public static function render_settings_page()
    {
        self::$active_tab = isset($_GET['tab']) ? sanitize_text_field($_GET['tab']) : 'snap-cre-main';

        if (!array_key_exists(self::$active_tab, self::$tabs)) {
            self::$active_tab = 'snap-cre-main';
        }

        ?>
        <div class="fcre-tab-header">
            <h2><?php echo esc_html__('Settings', 'snap-cre'); ?></h2>
            <p><?php echo esc_html__('Configure the settings for the plugin.', 'snap-cre'); ?></p>
        </div>

        <div class="fcre-tab-section">
            <div class="fcre-tab-sidebar">
                <div class="nav-tab-wrapper fcre-tabs">
                    <?php foreach (self::$tabs as $key => $label) : ?>
                        <a href="?page=<?php echo Config::$plugin_name; ?>-settings&tab=<?php echo esc_attr($key); ?>"
                           class="nav-tab <?php echo (self::$active_tab == $key) ? 'nav-tab-active' : ''; ?>">
                            <?php echo esc_html__($label, 'snap-cre'); ?>
                        </a>
                    <?php endforeach; ?>
                </div>
            </div>
            <div class="fcre-tab-content">
                <form action="" method="post">
                    <?php self::render_tab_content(self::$active_tab); ?>
                    <?php submit_button(); ?>
                </form>
            </div>
        </div>
        <?php
    }

    private static function render_tab_content($active_tab)
    {
        switch ($active_tab) {
            case 'snap-cre-main':
                self::render_main_tab();
                break;
            default:
                echo '<p>Tab content coming soon...</p>';
                break;
        }
    }

    private static function render_main_tab()
    {
        // Define all types we want to manage (matching original structure)
        $types = [
            'transaction-types' => 'Transaction Types',
            'property-types' => 'Property Types',
            'property-status' => 'Property Status',
        ];

        // Handle form submission (matching original logic)
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            foreach ($types as $option_key => $label) {
                if (isset($_POST[$option_key])) {
                    update_option(Config::$plugin_name . '-' . $option_key, $_POST[$option_key]);
                }
            }

            // Show success message
            echo '<div class="notice notice-success is-dismissible"><p>' . __('Settings saved successfully!', 'snap-cre') . '</p></div>';
        }

        // Load saved options (matching original structure)
        $options = [];
        foreach ($types as $option_key => $label) {
            $saved_data = get_option(Config::$plugin_name . '-' . $option_key, []);
            $options[$option_key] = is_array($saved_data) ? $saved_data : [];
        }

        // Prepare last IDs (matching original logic)
        $last_ids = [];
        foreach ($types as $option_key => $label) {
            $last_ids[$option_key] = !empty($options[$option_key]) ? max(array_column($options[$option_key], 'id')) : 0;
        }

        ?>


        <?php foreach ($types as $option_key => $label): ?>
            <div class="fcre-settings-card">
                <table class="fcre-types-table fcre-sort-table" id="<?php echo esc_attr($option_key); ?>-table">
                    <thead>
                        <tr>
                            <h3 class="card-title"><?php echo esc_html($label); ?></h3>
                            <small>Example: <?php echo ($option_key == 'transaction-types') ? 'for sale, for lease, etc.' : 'Retail, Office, Land, etc.'; ?></small>
                        </tr>
                    </thead>
                    <tbody id="<?php echo esc_attr($option_key); ?>">
                        <?php
                        $index = 1;
                        if (!empty($options[$option_key])) {
                            foreach ($options[$option_key] as $item) {
                                ?>
                                <tr>
                                    <td class="drag-handler" valign="top">
                                        <span class="move-handle"></span>
                                        <input type="hidden" name="<?php echo $option_key; ?>[<?php echo $index; ?>][id]" value="<?php echo esc_attr($item['id']); ?>">
                                        <input type="text" name="<?php echo $option_key; ?>[<?php echo $index; ?>][name]" value="<?php echo esc_attr($item['name']); ?>" placeholder="Enter name">
                                    </td>
                                    <td><span><?php echo esc_html($item['id']); ?></span></td>
                                    <td valign="top" class="row-remove"><span><i class="icon-trash"></i></span></td>
                                </tr>
                                <?php
                                $index++;
                            }
                        }
                        ?>
                    </tbody>
                </table>
                <button class="fcre-btn-add-more button button-default" data-type="<?php echo esc_attr($option_key); ?>">+ Add more</button>
            </div>
        <?php endforeach; ?>

        <script>
        jQuery(function ($) {
            var lastIds = <?php echo json_encode($last_ids); ?>;
            var counts = {};

            <?php foreach ($types as $option_key => $label): ?>
            counts['<?php echo $option_key; ?>'] = $('#<?php echo $option_key; ?> tr').length + 1;
            makeSortable('<?php echo $option_key; ?>');
            <?php endforeach; ?>

            function makeSortable(type) {
                $('#' + type).sortable({
                    opacity: 0.6,
                    axis: 'y',
                    handle: ".drag-handler",
                    placeholder: "ui-state-highlight",
                    stop: function () {
                        resetIndexes(type);
                    },
                    helper: function (e, ui) {
                        ui.children().each(function () {
                            $(this).width($(this).width());
                        });
                        return ui;
                    }
                });
            }

            function resetIndexes(type) {
                $('#' + type + ' tr').each(function (index) {
                    $(this).find('input').each(function () {
                        var name = $(this).attr('name');
                        if (name) {
                            var newName = name.replace(/\[\d+\]/, '[' + (index + 1) + ']');
                            $(this).attr('name', newName);
                        }
                    });
                });
            }

            $('.fcre-btn-add-more').click(function (e) {
                e.preventDefault();
                var type = $(this).data('type');
                var tbody = $('#' + type);
                var id = ++lastIds[type];
                var index = counts[type]++;

                var html = '<tr>' +
                    '<td class="drag-handler" valign="top">' +
                    '<span class="move-handle"></span>' +
                    '<input type="hidden" name="' + type + '[' + index + '][id]" value="' + id + '">' +
                    '<input type="text" name="' + type + '[' + index + '][name]" value="" placeholder="Enter name">' +
                    '</td>' +
                    '<td><span>' + id + '</span></td>' +
                    '<td valign="top" class="row-remove"><span><i class="icon-trash"></i></span></td>' +
                    '</tr>';

                tbody.append(html);
            });

            $(document).on('click', '.row-remove', function () {
                var type = $(this).closest('table').find('tbody').attr('id');
                $(this).closest('tr').remove();
                resetIndexes(type);
            });
        });
        </script>
        <?php
    }
}
