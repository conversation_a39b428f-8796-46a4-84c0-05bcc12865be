<?php

namespace SnapCRE;

class Dashboard
{
    private static $tabs = [
        'snap-cre-main' => 'Property & Transaction Types',
        // Future tabs can be uncommented as needed
        // 'snap-cre-filter' => 'Filters, Views and Sorting',
        // 'snap-cre-thumbnail-fields' => 'Thumbnail Fields',
        // 'snap-cre-confidentiality-agreement' => 'Confidentiality Agreement',
        // 'snap-cre-ca' => 'Property Detail Page',
        // 'snap-cre-formbuilder' => 'Form Builder',
        // 'snap-cre-email' => 'Admin Email and Form Selector',
        // 'snap-cre-sharing' => 'Sharing',
        // 'snap-cre-pagination' => 'Pagination',
        // 'snap-cre-registration' => 'User Registration',
        // 'snap-cre-general-settings' => 'General Settings',
        // 'snap-cre-widget' => 'Search Widget',
        // 'snap-cre-shortcode' => 'Shortcodes',
        // 'snap-cre-map' => 'Map & reCAPTCHA',
        // 'snap-cre-api' => 'API',
        // 'snap-cre-colors' => 'Colors',
        // 'snap-cre-branding' => 'Branding',
    ];

    public static function init()
    {
        add_action('admin_menu', [__CLASS__, 'add_menu']);
        add_action('admin_enqueue_scripts', [__CLASS__, 'enqueue_admin_assets']);
        add_action('admin_init', [__CLASS__, 'handle_form_submissions']);
    }

    public static function add_menu()
    {
        if (!current_user_can('edit_posts')) {
            return;
        }

        // Main menu page
        add_menu_page(
            'SnapCRE',
            'SnapCRE',
            'edit_posts',
            Config::$plugin_name,
            '__return_false', // No callback, just a parent menu
            'dashicons-building',
            '12'
        );

        // Settings submenu
        add_submenu_page(
            Config::$plugin_name,
            'Settings',
            'Settings',
            'manage_options',
            Config::$plugin_name . '-settings',
            [__CLASS__, 'render_settings_page']
        );
    }

    public static function enqueue_admin_assets($hook)
    {
        // Only load on our admin pages
        if (strpos($hook, Config::$plugin_name) === false) {
            return;
        }

        wp_enqueue_style(
            'snap-cre-admin',
            SNAP_CRE_PLUGIN_URL . 'assets/admin.css',
            [],
            SNAP_CRE_VERSION
        );

        wp_enqueue_script(
            'snap-cre-admin',
            SNAP_CRE_PLUGIN_URL . 'assets/admin.js',
            ['jquery'],
            SNAP_CRE_VERSION,
            true
        );

        wp_localize_script('snap-cre-admin', 'snapCreAdmin', [
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('snap_cre_admin_nonce'),
        ]);
    }

    public static function render_settings_page()
    {
        $active_tab = isset($_GET['tab']) ? sanitize_text_field($_GET['tab']) : 'snap-cre-main';
        
        ?>
        <div class="wrap">
            <h1><?php echo esc_html(get_admin_page_title()); ?></h1>
            
            <nav class="nav-tab-wrapper">
                <?php foreach (self::$tabs as $tab_key => $tab_label): ?>
                    <a href="?page=<?php echo Config::$plugin_name; ?>-settings&tab=<?php echo $tab_key; ?>" 
                       class="nav-tab <?php echo $active_tab === $tab_key ? 'nav-tab-active' : ''; ?>">
                        <?php echo esc_html($tab_label); ?>
                    </a>
                <?php endforeach; ?>
            </nav>

            <div class="tab-content">
                <?php self::render_tab_content($active_tab); ?>
            </div>
        </div>
        <?php
    }

    private static function render_tab_content($active_tab)
    {
        switch ($active_tab) {
            case 'snap-cre-main':
                self::render_main_tab();
                break;
            default:
                echo '<p>Tab content coming soon...</p>';
                break;
        }
    }

    private static function render_main_tab()
    {
        $property_types = Config::get_option('property_types', []);
        $transaction_types = Config::get_option('transaction_types', []);
        
        ?>
        <form method="post" action="">
            <?php wp_nonce_field('snap_cre_settings', 'snap_cre_settings_nonce'); ?>
            
            <table class="form-table">
                <tr>
                    <th scope="row">
                        <label for="property_types"><?php _e('Property Types', 'snap-cre'); ?></label>
                    </th>
                    <td>
                        <div id="property-types-container">
                            <?php if (!empty($property_types)): ?>
                                <?php foreach ($property_types as $index => $type): ?>
                                    <div class="type-row">
                                        <input type="hidden" name="property_types[<?php echo $index; ?>][id]" value="<?php echo esc_attr($type['id']); ?>">
                                        <input type="text" name="property_types[<?php echo $index; ?>][name]" value="<?php echo esc_attr($type['name']); ?>" placeholder="Property Type Name">
                                        <button type="button" class="button remove-type">Remove</button>
                                    </div>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>
                        <button type="button" id="add-property-type" class="button">Add Property Type</button>
                        <p class="description"><?php _e('Manage property types for your listings.', 'snap-cre'); ?></p>
                    </td>
                </tr>
                
                <tr>
                    <th scope="row">
                        <label for="transaction_types"><?php _e('Transaction Types', 'snap-cre'); ?></label>
                    </th>
                    <td>
                        <div id="transaction-types-container">
                            <?php if (!empty($transaction_types)): ?>
                                <?php foreach ($transaction_types as $index => $type): ?>
                                    <div class="type-row">
                                        <input type="hidden" name="transaction_types[<?php echo $index; ?>][id]" value="<?php echo esc_attr($type['id']); ?>">
                                        <input type="text" name="transaction_types[<?php echo $index; ?>][name]" value="<?php echo esc_attr($type['name']); ?>" placeholder="Transaction Type Name">
                                        <button type="button" class="button remove-type">Remove</button>
                                    </div>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>
                        <button type="button" id="add-transaction-type" class="button">Add Transaction Type</button>
                        <p class="description"><?php _e('Manage transaction types for your listings.', 'snap-cre'); ?></p>
                    </td>
                </tr>
            </table>
            
            <?php submit_button(); ?>
        </form>
        
        <script>
        jQuery(document).ready(function($) {
            // Add property type
            $('#add-property-type').on('click', function() {
                var container = $('#property-types-container');
                var index = container.find('.type-row').length;
                var newId = Date.now(); // Simple ID generation
                var html = '<div class="type-row">' +
                    '<input type="hidden" name="property_types[' + index + '][id]" value="' + newId + '">' +
                    '<input type="text" name="property_types[' + index + '][name]" placeholder="Property Type Name">' +
                    '<button type="button" class="button remove-type">Remove</button>' +
                    '</div>';
                container.append(html);
            });
            
            // Add transaction type
            $('#add-transaction-type').on('click', function() {
                var container = $('#transaction-types-container');
                var index = container.find('.type-row').length;
                var newId = Date.now(); // Simple ID generation
                var html = '<div class="type-row">' +
                    '<input type="hidden" name="transaction_types[' + index + '][id]" value="' + newId + '">' +
                    '<input type="text" name="transaction_types[' + index + '][name]" placeholder="Transaction Type Name">' +
                    '<button type="button" class="button remove-type">Remove</button>' +
                    '</div>';
                container.append(html);
            });
            
            // Remove type
            $(document).on('click', '.remove-type', function() {
                $(this).closest('.type-row').remove();
            });
        });
        </script>
        
        <style>
        .type-row {
            margin-bottom: 10px;
        }
        .type-row input[type="text"] {
            width: 300px;
            margin-right: 10px;
        }
        </style>
        <?php
    }

    public static function handle_form_submissions()
    {
        if (!isset($_POST['snap_cre_settings_nonce']) || !wp_verify_nonce($_POST['snap_cre_settings_nonce'], 'snap_cre_settings')) {
            return;
        }

        if (!current_user_can('manage_options')) {
            return;
        }

        // Handle property types
        if (isset($_POST['property_types'])) {
            $property_types = [];
            foreach ($_POST['property_types'] as $type) {
                if (!empty($type['name'])) {
                    $property_types[] = [
                        'id' => intval($type['id']),
                        'name' => sanitize_text_field($type['name'])
                    ];
                }
            }
            Config::update_option('property_types', $property_types);
        }

        // Handle transaction types
        if (isset($_POST['transaction_types'])) {
            $transaction_types = [];
            foreach ($_POST['transaction_types'] as $type) {
                if (!empty($type['name'])) {
                    $transaction_types[] = [
                        'id' => intval($type['id']),
                        'name' => sanitize_text_field($type['name'])
                    ];
                }
            }
            Config::update_option('transaction_types', $transaction_types);
        }

        // Show success message
        add_action('admin_notices', function() {
            echo '<div class="notice notice-success is-dismissible"><p>' . __('Settings saved successfully!', 'snap-cre') . '</p></div>';
        });
    }
}
