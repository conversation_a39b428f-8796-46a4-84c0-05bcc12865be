<?php

namespace SnapCRE;

class MetaBoxes
{
    public static function init()
    {
        add_action('add_meta_boxes', [__CLASS__, 'add_meta_boxes']);
        add_action('save_post', [__CLASS__, 'save_property_meta'], 10, 2);
        add_action('save_post', [__CLASS__, 'save_agent_meta'], 10, 2);
    }

    public static function add_meta_boxes()
    {
        // Property metabox
        add_meta_box(
            'snap-cre-property-details',
            __('<strong>Property Details</strong>', 'snap-cre'),
            [__CLASS__, 'render_property_metabox'],
            Config::$properties_post_slug,
            'normal',
            'high'
        );

        // Agent metabox
        add_meta_box(
            'snap-cre-agent-details',
            __('<strong>Agent Details</strong>', 'snap-cre'),
            [__CLASS__, 'render_agent_metabox'],
            Config::$agents_post_slug,
            'normal',
            'high'
        );
    }

    public static function render_property_metabox($post)
    {
        wp_nonce_field('snap_cre_save_metabox', 'snap_cre_metabox_nonce');

        $tabs = [
            ['slug' => 'overview', 'label' => 'Overview'],
            ['slug' => 'documents', 'label' => 'Documents'],
            ['slug' => 'photos', 'label' => 'Photos'],
            ['slug' => 'agents', 'label' => 'Agents'],
            ['slug' => 'demographics', 'label' => 'Demographics'],
            ['slug' => 'confidentiality_agreement', 'label' => 'Confidentiality Agreement']
        ];

        ?>
        <div class="snap-cre-metabox-tabs">
            <ul class="snap-cre-tab-nav">
                <?php foreach ($tabs as $index => $tab): ?>
                    <li class="<?php echo $index === 0 ? 'active' : ''; ?>">
                        <a href="#<?php echo $tab['slug']; ?>"><?php echo esc_html($tab['label']); ?></a>
                    </li>
                <?php endforeach; ?>
            </ul>

            <div class="snap-cre-tab-content">
                <?php foreach ($tabs as $index => $tab): ?>
                    <div id="<?php echo $tab['slug']; ?>" class="snap-cre-tab-pane <?php echo $index === 0 ? 'active' : ''; ?>">
                        <?php self::render_property_tab_content($tab['slug'], $post); ?>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>

        <style>
        .snap-cre-metabox-tabs {
            margin: 20px 0;
        }
        .snap-cre-tab-nav {
            list-style: none;
            margin: 0;
            padding: 0;
            border-bottom: 1px solid #ccc;
        }
        .snap-cre-tab-nav li {
            display: inline-block;
            margin: 0;
        }
        .snap-cre-tab-nav li a {
            display: block;
            padding: 10px 15px;
            text-decoration: none;
            border: 1px solid transparent;
            border-bottom: none;
        }
        .snap-cre-tab-nav li.active a {
            background: #fff;
            border-color: #ccc;
            border-bottom: 1px solid #fff;
            margin-bottom: -1px;
        }
        .snap-cre-tab-pane {
            display: none;
            padding: 20px 0;
        }
        .snap-cre-tab-pane.active {
            display: block;
        }
        .snap-cre-field {
            margin-bottom: 15px;
        }
        .snap-cre-field label {
            display: block;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .snap-cre-field input,
        .snap-cre-field textarea,
        .snap-cre-field select {
            width: 100%;
            max-width: 400px;
        }
        </style>

        <script>
        jQuery(document).ready(function($) {
            $('.snap-cre-tab-nav a').on('click', function(e) {
                e.preventDefault();
                var target = $(this).attr('href');
                
                // Update nav
                $('.snap-cre-tab-nav li').removeClass('active');
                $(this).parent().addClass('active');
                
                // Update content
                $('.snap-cre-tab-pane').removeClass('active');
                $(target).addClass('active');
            });
        });
        </script>
        <?php
    }

    private static function render_property_tab_content($tab, $post)
    {
        switch ($tab) {
            case 'overview':
                self::render_overview_tab($post);
                break;
            case 'documents':
                self::render_documents_tab($post);
                break;
            case 'photos':
                self::render_photos_tab($post);
                break;
            case 'agents':
                self::render_agents_tab($post);
                break;
            case 'demographics':
                self::render_demographics_tab($post);
                break;
            case 'confidentiality_agreement':
                self::render_confidentiality_tab($post);
                break;
        }
    }

    private static function render_overview_tab($post)
    {
        $property_types = Config::get_option('property-types', []);
        $transaction_types = Config::get_option('transaction-types', []);

        $selected_property_types = get_post_meta($post->ID, 'property_types', true);
        $selected_transaction_types = get_post_meta($post->ID, 'transaction_types', true);
        $address = get_post_meta($post->ID, 'address', true);
        $site = get_post_meta($post->ID, 'site', true);
        $building = get_post_meta($post->ID, 'building', true);
        $building_size = get_post_meta($post->ID, 'building_size', true);
        $building_class = get_post_meta($post->ID, 'building_class', true);
        $lease_rate = get_post_meta($post->ID, 'lease_rate', true);
        $year_built = get_post_meta($post->ID, 'year_built', true);
        $status = get_post_meta($post->ID, 'status', true);
        $latitude = get_post_meta($post->ID, 'latitude', true);
        $longitude = get_post_meta($post->ID, 'longitude', true);

        ?>
        <div class="snap-cre-field">
            <label><?php _e('Property Types', 'snap-cre'); ?></label>
            <select name="property_types[]" multiple>
                <?php foreach ($property_types as $type): ?>
                    <option value="<?php echo esc_attr($type['id']); ?>" 
                            <?php echo in_array($type['id'], (array)$selected_property_types) ? 'selected' : ''; ?>>
                        <?php echo esc_html($type['name']); ?>
                    </option>
                <?php endforeach; ?>
            </select>
        </div>

        <div class="snap-cre-field">
            <label><?php _e('Transaction Types', 'snap-cre'); ?></label>
            <select name="transaction_types[]" multiple>
                <?php foreach ($transaction_types as $type): ?>
                    <option value="<?php echo esc_attr($type['id']); ?>" 
                            <?php echo in_array($type['id'], (array)$selected_transaction_types) ? 'selected' : ''; ?>>
                        <?php echo esc_html($type['name']); ?>
                    </option>
                <?php endforeach; ?>
            </select>
        </div>

        <div class="snap-cre-field">
            <label><?php _e('Address', 'snap-cre'); ?></label>
            <input type="text" name="address" value="<?php echo esc_attr($address); ?>">
        </div>

        <div class="snap-cre-field">
            <label><?php _e('Site', 'snap-cre'); ?></label>
            <input type="text" name="site" value="<?php echo esc_attr($site); ?>">
        </div>

        <div class="snap-cre-field">
            <label><?php _e('Building', 'snap-cre'); ?></label>
            <input type="text" name="building" value="<?php echo esc_attr($building); ?>">
        </div>

        <div class="snap-cre-field">
            <label><?php _e('Building Size', 'snap-cre'); ?></label>
            <input type="text" name="building_size" value="<?php echo esc_attr($building_size); ?>">
        </div>

        <div class="snap-cre-field">
            <label><?php _e('Building Class', 'snap-cre'); ?></label>
            <input type="text" name="building_class" value="<?php echo esc_attr($building_class); ?>">
        </div>

        <div class="snap-cre-field">
            <label><?php _e('Lease Rate', 'snap-cre'); ?></label>
            <input type="text" name="lease_rate" value="<?php echo esc_attr($lease_rate); ?>">
        </div>

        <div class="snap-cre-field">
            <label><?php _e('Year Built', 'snap-cre'); ?></label>
            <input type="number" name="year_built" value="<?php echo esc_attr($year_built); ?>">
        </div>

        <div class="snap-cre-field">
            <label><?php _e('Status', 'snap-cre'); ?></label>
            <input type="text" name="status" value="<?php echo esc_attr($status); ?>">
        </div>

        <div class="snap-cre-field">
            <label><?php _e('Latitude', 'snap-cre'); ?></label>
            <input type="text" name="latitude" value="<?php echo esc_attr($latitude); ?>">
        </div>

        <div class="snap-cre-field">
            <label><?php _e('Longitude', 'snap-cre'); ?></label>
            <input type="text" name="longitude" value="<?php echo esc_attr($longitude); ?>">
        </div>
        <?php
    }

    private static function render_documents_tab($post)
    {
        $property_flyer = get_post_meta($post->ID, 'property_flyer', true);
        $documents_gallery = get_post_meta($post->ID, 'documents_gallery', true);

        ?>
        <div class="snap-cre-field">
            <label><?php _e('Property Flyer', 'snap-cre'); ?></label>
            <input type="text" name="property_flyer" value="<?php echo esc_attr($property_flyer); ?>" placeholder="URL to property flyer">
        </div>

        <div class="snap-cre-field">
            <label><?php _e('Documents Gallery', 'snap-cre'); ?></label>
            <textarea name="documents_gallery" rows="5" placeholder="Document URLs, one per line"><?php echo esc_textarea($documents_gallery); ?></textarea>
        </div>
        <?php
    }

    private static function render_photos_tab($post)
    {
        $photo_gallery = get_post_meta($post->ID, 'photo_gallery', true);

        ?>
        <div class="snap-cre-field">
            <label><?php _e('Photo Gallery', 'snap-cre'); ?></label>
            <textarea name="photo_gallery" rows="5" placeholder="Photo URLs, one per line"><?php echo esc_textarea($photo_gallery); ?></textarea>
        </div>
        <?php
    }

    private static function render_agents_tab($post)
    {
        $related_agents = get_post_meta($post->ID, 'related_agents', true);

        ?>
        <div class="snap-cre-field">
            <label><?php _e('Related Agents', 'snap-cre'); ?></label>
            <textarea name="related_agents" rows="3" placeholder="Agent IDs, comma separated"><?php echo esc_textarea($related_agents); ?></textarea>
        </div>
        <?php
    }

    private static function render_demographics_tab($post)
    {
        $demographics = get_post_meta($post->ID, 'demographics', true);

        ?>
        <div class="snap-cre-field">
            <label><?php _e('Demographics', 'snap-cre'); ?></label>
            <textarea name="demographics" rows="5" placeholder="Demographics information"><?php echo esc_textarea($demographics); ?></textarea>
        </div>
        <?php
    }

    private static function render_confidentiality_tab($post)
    {
        $ca_check = get_post_meta($post->ID, 'ca_check', true);
        $is_global_ca_check = get_post_meta($post->ID, 'is_global_ca_check', true);
        $confidential_agreement = get_post_meta($post->ID, 'confidential_agreement', true);

        ?>
        <div class="snap-cre-field">
            <label>
                <input type="checkbox" name="ca_check" value="1" <?php checked($ca_check, '1'); ?>>
                <?php _e('Require Confidentiality Agreement', 'snap-cre'); ?>
            </label>
        </div>

        <div class="snap-cre-field">
            <label>
                <input type="checkbox" name="is_global_ca_check" value="1" <?php checked($is_global_ca_check, '1'); ?>>
                <?php _e('Use Global Confidentiality Agreement', 'snap-cre'); ?>
            </label>
        </div>

        <div class="snap-cre-field">
            <label><?php _e('Custom Confidentiality Agreement', 'snap-cre'); ?></label>
            <textarea name="confidential_agreement" rows="10"><?php echo esc_textarea($confidential_agreement); ?></textarea>
        </div>
        <?php
    }

    public static function render_agent_metabox($post)
    {
        wp_nonce_field('snap_cre_save_metabox', 'snap_cre_metabox_nonce');
        
        $email = get_post_meta($post->ID, 'email', true);
        $phone = get_post_meta($post->ID, 'phone', true);
        $bio = get_post_meta($post->ID, 'bio', true);

        ?>
        <div class="snap-cre-field">
            <label><?php _e('Email', 'snap-cre'); ?></label>
            <input type="email" name="email" value="<?php echo esc_attr($email); ?>">
        </div>

        <div class="snap-cre-field">
            <label><?php _e('Phone', 'snap-cre'); ?></label>
            <input type="text" name="phone" value="<?php echo esc_attr($phone); ?>">
        </div>

        <div class="snap-cre-field">
            <label><?php _e('Bio', 'snap-cre'); ?></label>
            <textarea name="bio" rows="5"><?php echo esc_textarea($bio); ?></textarea>
        </div>
        <?php
    }

    public static function save_property_meta($post_id, $post)
    {
        if (!isset($_POST['snap_cre_metabox_nonce']) || !wp_verify_nonce($_POST['snap_cre_metabox_nonce'], 'snap_cre_save_metabox')) {
            return;
        }

        if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
            return;
        }

        if (!current_user_can('edit_post', $post_id)) {
            return;
        }

        if ($post->post_type !== Config::$properties_post_slug) {
            return;
        }

        $fields_to_save = [
            'transaction_types',
            'property_types',
            'address',
            'site',
            'building',
            'building_size',
            'building_class',
            'lease_rate',
            'year_built',
            'status',
            'latitude',
            'longitude',
            'property_flyer',
            'documents_gallery',
            'photo_gallery',
            'demographics',
            'related_agents',
            'ca_check',
            'is_global_ca_check',
            'confidential_agreement',
        ];

        foreach ($fields_to_save as $field) {
            if (isset($_POST[$field])) {
                update_post_meta($post_id, $field, $_POST[$field]);
            } else {
                delete_post_meta($post_id, $field);
            }
        }
    }

    public static function save_agent_meta($post_id, $post)
    {
        if (!isset($_POST['snap_cre_metabox_nonce']) || !wp_verify_nonce($_POST['snap_cre_metabox_nonce'], 'snap_cre_save_metabox')) {
            return;
        }

        if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
            return;
        }

        if (!current_user_can('edit_post', $post_id)) {
            return;
        }

        if ($post->post_type !== Config::$agents_post_slug) {
            return;
        }

        $fields_to_save = [
            'email',
            'phone',
            'bio',
        ];

        foreach ($fields_to_save as $field) {
            if (isset($_POST[$field])) {
                update_post_meta($post_id, $field, sanitize_text_field($_POST[$field]));
            } else {
                delete_post_meta($post_id, $field);
            }
        }
    }
}
