<?php

namespace SnapCRE;

class PostTypes
{
    public static function init()
    {
        add_action('init', [__CLASS__, 'register_post_types']);
        add_action('init', [__CLASS__, 'register_taxonomies']);
    }
    
    public static function register_post_types()
    {
        self::register_properties_post_type();
        self::register_agents_post_type();
        self::register_agreements_post_type();
        self::register_contacts_post_type();
        self::register_download_request_post_type();
        self::register_document_vault_post_type();
    }
    
    public static function register_properties_post_type()
    {
        $labels = array(
            'name' => _x('Properties', 'post type general name', 'snap-cre'),
            'singular_name' => _x('Property', 'post type singular name', 'snap-cre'),
            'add_new' => _x('Add New', 'property', 'snap-cre'),
            'add_new_item' => __('Add New Property', 'snap-cre'),
            'edit_item' => __('Edit Property', 'snap-cre'),
            'new_item' => __('New Property', 'snap-cre'),
            'all_items' => __('Properties', 'snap-cre'),
            'view_item' => __('View Property', 'snap-cre'),
            'search_items' => __('Search Properties', 'snap-cre'),
            'not_found' => __('No Properties found', 'snap-cre'),
            'not_found_in_trash' => __('No Properties found in the Trash', 'snap-cre'),
            'parent_item_colon' => '',
            'menu_name' => 'Properties',
        );
        
        $args = array(
            'labels' => $labels,
            'hierarchical' => false,
            'supports' => array('title', 'editor', 'thumbnail'),
            'public' => true,
            'show_ui' => true,
            'show_in_nav_menus' => false,
            'publicly_queryable' => true,
            'exclude_from_search' => false,
            'has_archive' => false,
            'query_var' => true,
            'can_export' => true,
            'capability_type' => 'post',
            'show_in_menu' => Config::$plugin_name,
            'rewrite' => array('slug' => Config::$properties_post_slug),
        );
        
        register_post_type(Config::$properties_post_slug, $args);
    }
    
    public static function register_agents_post_type()
    {
        $labels = array(
            'name' => _x('Agents', 'post type general name', 'snap-cre'),
            'singular_name' => _x('Agent', 'post type singular name', 'snap-cre'),
            'add_new' => _x('Add New', 'agent', 'snap-cre'),
            'add_new_item' => __('Add New Agent', 'snap-cre'),
            'edit_item' => __('Edit Agent', 'snap-cre'),
            'all_items' => __('Agents', 'snap-cre'),
            'view_item' => __('View Agent', 'snap-cre'),
            'search_items' => __('Search Agents', 'snap-cre'),
            'not_found' => __('No Agents found', 'snap-cre'),
            'not_found_in_trash' => __('No Agents found in the Trash', 'snap-cre'),
            'parent_item_colon' => '',
            'menu_name' => 'Agents',
        );
        
        $args = array(
            'labels' => $labels,
            'hierarchical' => false,
            'taxonomies' => array('category'),
            'supports' => array('title', 'editor', 'thumbnail'),
            'public' => true,
            'show_ui' => true,
            'show_in_nav_menus' => false,
            'publicly_queryable' => true,
            'exclude_from_search' => false,
            'has_archive' => false,
            'query_var' => true,
            'can_export' => true,
            'capability_type' => 'post',
            'show_in_menu' => Config::$plugin_name,
            'rewrite' => array('slug' => Config::$agents_post_slug),
        );
        
        register_post_type(Config::$agents_post_slug, $args);
    }
    
    public static function register_agreements_post_type()
    {
        $labels = array(
            'name' => _x('Agreements', 'post type general name', 'snap-cre'),
            'singular_name' => _x('Agreement', 'post type singular name', 'snap-cre'),
            'edit_item' => __('Edit Agreement', 'snap-cre'),
            'all_items' => __('Agreements', 'snap-cre'),
            'view_item' => __('View Agreement', 'snap-cre'),
            'search_items' => __('Search Agreements', 'snap-cre'),
            'not_found' => __('No Agreements found', 'snap-cre'),
            'not_found_in_trash' => __('No Agreements found in the Trash', 'snap-cre'),
            'parent_item_colon' => '',
            'menu_name' => 'Agreements',
        );
        
        $args = array(
            'labels' => $labels,
            'description' => '',
            'public' => true,
            'menu_position' => 30,
            'supports' => array('title'),
            'has_archive' => false,
            'capability_type' => 'post',
            'capabilities' => array(
                'create_posts' => 'do_not_allow',
            ),
            'publicly_queryable' => false,
            'map_meta_cap' => true,
            'show_in_menu' => Config::$plugin_name,
            'rewrite' => array('slug' => Config::$agreements_post_slug),
        );
        
        register_post_type(Config::$agreements_post_slug, $args);
    }
    
    public static function register_contacts_post_type()
    {
        $labels = array(
            'name' => _x('Contacts', 'post type general name', 'snap-cre'),
            'singular_name' => _x('Contact', 'post type singular name', 'snap-cre'),
            'edit_item' => __('Edit Contact', 'snap-cre'),
            'all_items' => __('Contacts', 'snap-cre'),
            'view_item' => __('View Contact', 'snap-cre'),
            'search_items' => __('Search Contacts', 'snap-cre'),
            'not_found' => __('No Contacts found', 'snap-cre'),
            'not_found_in_trash' => __('No Contacts found in the Trash', 'snap-cre'),
            'parent_item_colon' => '',
            'menu_name' => 'Contacts',
        );
        
        $args = array(
            'labels' => $labels,
            'public' => false,
            'show_ui' => true,
            'supports' => array('title'),
            'has_archive' => false,
            'capability_type' => 'post',
            'capabilities' => array(
                'create_posts' => 'do_not_allow',
            ),
            'publicly_queryable' => false,
            'map_meta_cap' => true,
            'show_in_menu' => Config::$plugin_name,
        );
        
        register_post_type(Config::$contacts_post_slug, $args);
    }
    
    public static function register_download_request_post_type()
    {
        $labels = array(
            'name' => _x('Download Requests', 'post type general name', 'snap-cre'),
            'singular_name' => _x('Download Request', 'post type singular name', 'snap-cre'),
            'edit_item' => __('Edit Download Request', 'snap-cre'),
            'all_items' => __('Download Requests', 'snap-cre'),
            'view_item' => __('View Download Request', 'snap-cre'),
            'search_items' => __('Search Download Requests', 'snap-cre'),
            'not_found' => __('No Download Requests found', 'snap-cre'),
            'not_found_in_trash' => __('No Download Requests found in the Trash', 'snap-cre'),
            'parent_item_colon' => '',
            'menu_name' => 'Download Requests',
        );
        
        $args = array(
            'labels' => $labels,
            'public' => false,
            'show_ui' => true,
            'supports' => array('title'),
            'has_archive' => false,
            'capability_type' => 'post',
            'capabilities' => array(
                'create_posts' => 'do_not_allow',
            ),
            'publicly_queryable' => false,
            'map_meta_cap' => true,
            'show_in_menu' => Config::$plugin_name,
        );
        
        register_post_type(Config::$download_request_post_slug, $args);
    }
    
    public static function register_document_vault_post_type()
    {
        $labels = array(
            'name' => _x('Document Vault', 'post type general name', 'snap-cre'),
            'singular_name' => _x('Document', 'post type singular name', 'snap-cre'),
            'add_new' => _x('Add New', 'document', 'snap-cre'),
            'add_new_item' => __('Add New Document', 'snap-cre'),
            'edit_item' => __('Edit Document', 'snap-cre'),
            'all_items' => __('Document Vault', 'snap-cre'),
            'view_item' => __('View Document', 'snap-cre'),
            'search_items' => __('Search Documents', 'snap-cre'),
            'not_found' => __('No Documents found', 'snap-cre'),
            'not_found_in_trash' => __('No Documents found in the Trash', 'snap-cre'),
            'parent_item_colon' => '',
            'menu_name' => 'Document Vault',
        );
        
        $args = array(
            'labels' => $labels,
            'public' => false,
            'show_ui' => true,
            'supports' => array('title', 'editor'),
            'has_archive' => false,
            'capability_type' => 'post',
            'publicly_queryable' => false,
            'show_in_menu' => Config::$plugin_name,
        );
        
        register_post_type(Config::$document_vault_post_slug, $args);
    }
    
    public static function register_taxonomies()
    {
        // Register any custom taxonomies here if needed
    }
}
