<?php

namespace SnapCRE;

class PublicFrontend
{
    public static function init()
    {
        add_action('wp_enqueue_scripts', [__CLASS__, 'enqueue_styles']);
        add_action('wp_enqueue_scripts', [__CLASS__, 'enqueue_scripts']);
        add_filter('single_template', [__CLASS__, 'custom_single_templates']);
        add_action('wp_ajax_snap_cre_filter_properties', [__CLASS__, 'ajax_filter_properties']);
        add_action('wp_ajax_nopriv_snap_cre_filter_properties', [__CLASS__, 'ajax_filter_properties']);
    }

    public static function enqueue_styles()
    {
        wp_enqueue_style(
            'snap-cre-public',
            SNAP_CRE_PLUGIN_URL . 'assets/public.css',
            [],
            SNAP_CRE_VERSION
        );

        // External CSS dependencies
        wp_enqueue_style('leaflet-css', 'https://unpkg.com/leaflet@1.7.1/dist/leaflet.css', [], null);
        wp_enqueue_style('fotorama-css', 'https://cdnjs.cloudflare.com/ajax/libs/fotorama/4.6.4/fotorama.css', [], null);
        wp_enqueue_style('leaflet-markercluster-css', 'https://unpkg.com/leaflet.markercluster@1.5.3/dist/MarkerCluster.css', [], null);
        wp_enqueue_style('leaflet-markercluster-default-css', 'https://unpkg.com/leaflet.markercluster@1.5.3/dist/MarkerCluster.Default.css', [], null);
    }

    public static function enqueue_scripts()
    {
        wp_enqueue_script('jquery');

        // External JS dependencies
        wp_enqueue_script('leaflet-js', 'https://unpkg.com/leaflet@1.7.1/dist/leaflet.js', ['jquery'], null, true);
        wp_enqueue_script('fotorama-js', 'https://cdnjs.cloudflare.com/ajax/libs/fotorama/4.6.4/fotorama.js', ['jquery'], null, true);
        wp_enqueue_script('leaflet-markercluster-js', 'https://unpkg.com/leaflet.markercluster@1.5.3/dist/leaflet.markercluster.js', ['jquery'], null, true);
        wp_enqueue_script('leaflet-markercluster-default-js', 'https://unpkg.com/leaflet.markercluster@1.5.3/dist/MarkerCluster.Default.js', ['jquery'], null, true);

        wp_enqueue_script(
            'snap-cre-public',
            SNAP_CRE_PLUGIN_URL . 'assets/public.js',
            ['jquery'],
            SNAP_CRE_VERSION,
            true
        );

        $filter_vars = [
            'admin_ajax' => admin_url('admin-ajax.php'),
            'marker_icon_url' => SNAP_CRE_PLUGIN_URL . 'assets/images/map-marker.png',
            'nonce' => wp_create_nonce('snap_cre_public_nonce'),
        ];

        wp_localize_script('snap-cre-public', 'snapCrePublic', $filter_vars);
    }

    public static function custom_single_templates($single)
    {
        global $post;

        if ($post->post_type === Config::$properties_post_slug) {
            $template = Templates::locate_template('single-property.php');
            if ($template) {
                return $template;
            }
        }

        if ($post->post_type === Config::$agents_post_slug) {
            $template = Templates::locate_template('single-agent.php');
            if ($template) {
                return $template;
            }
        }

        return $single;
    }

    public static function ajax_filter_properties()
    {
        check_ajax_referer('snap_cre_public_nonce', 'nonce');

        $property_types = isset($_POST['property_types']) ? array_map('intval', $_POST['property_types']) : [];
        $transaction_types = isset($_POST['transaction_types']) ? array_map('intval', $_POST['transaction_types']) : [];
        $search_term = isset($_POST['search_term']) ? sanitize_text_field($_POST['search_term']) : '';

        $args = [
            'post_type' => Config::$properties_post_slug,
            'post_status' => 'publish',
            'posts_per_page' => -1,
        ];

        // Add meta query for filtering
        $meta_query = ['relation' => 'AND'];

        if (!empty($property_types)) {
            $meta_query[] = [
                'key' => 'property_types',
                'value' => $property_types,
                'compare' => 'IN',
            ];
        }

        if (!empty($transaction_types)) {
            $meta_query[] = [
                'key' => 'transaction_types',
                'value' => $transaction_types,
                'compare' => 'IN',
            ];
        }

        if (!empty($meta_query) && count($meta_query) > 1) {
            $args['meta_query'] = $meta_query;
        }

        // Add search query
        if (!empty($search_term)) {
            $args['s'] = $search_term;
        }

        $query = new \WP_Query($args);
        $properties = [];

        if ($query->have_posts()) {
            while ($query->have_posts()) {
                $query->the_post();
                $post_id = get_the_ID();

                $property = [
                    'id' => $post_id,
                    'title' => get_the_title(),
                    'permalink' => get_permalink(),
                    'excerpt' => get_the_excerpt(),
                    'thumbnail' => get_the_post_thumbnail_url($post_id, 'medium'),
                    'address' => get_post_meta($post_id, 'address', true),
                    'property_types' => Utils::get_option_labels_from_meta($post_id, 'property_types', 'property_types'),
                    'transaction_types' => Utils::get_option_labels_from_meta($post_id, 'transaction_types', 'transaction_types'),
                    'building_size' => get_post_meta($post_id, 'building_size', true),
                    'lease_rate' => get_post_meta($post_id, 'lease_rate', true),
                    'year_built' => get_post_meta($post_id, 'year_built', true),
                    'latitude' => get_post_meta($post_id, 'latitude', true),
                    'longitude' => get_post_meta($post_id, 'longitude', true),
                ];

                $properties[] = $property;
            }
            wp_reset_postdata();
        }

        wp_send_json_success([
            'properties' => $properties,
            'count' => count($properties),
        ]);
    }

    public static function get_properties_for_display($atts = [])
    {
        $property_types = isset($atts['property_types']) ? explode(',', $atts['property_types']) : [];
        $transaction_types = isset($atts['transaction_types']) ? explode(',', $atts['transaction_types']) : [];

        $args = [
            'post_type' => Config::$properties_post_slug,
            'post_status' => 'publish',
            'posts_per_page' => -1,
        ];

        // Add meta query for filtering
        $meta_query = ['relation' => 'AND'];

        if (!empty($property_types)) {
            $property_types = array_map('trim', $property_types);
            $property_types = array_map('intval', $property_types);
            $meta_query[] = [
                'key' => 'property_types',
                'value' => $property_types,
                'compare' => 'IN',
            ];
        }

        if (!empty($transaction_types)) {
            $transaction_types = array_map('trim', $transaction_types);
            $transaction_types = array_map('intval', $transaction_types);
            $meta_query[] = [
                'key' => 'transaction_types',
                'value' => $transaction_types,
                'compare' => 'IN',
            ];
        }

        if (!empty($meta_query) && count($meta_query) > 1) {
            $args['meta_query'] = $meta_query;
        }

        return new \WP_Query($args);
    }

    public static function render_property_card($post_id)
    {
        $title = get_the_title($post_id);
        $permalink = get_permalink($post_id);
        $thumbnail = get_the_post_thumbnail_url($post_id, 'medium');
        $address = get_post_meta($post_id, 'address', true);
        $property_types = Utils::get_option_labels_from_meta($post_id, 'property_types', 'property_types');
        $transaction_types = Utils::get_option_labels_from_meta($post_id, 'transaction_types', 'transaction_types');
        $building_size = get_post_meta($post_id, 'building_size', true);
        $lease_rate = get_post_meta($post_id, 'lease_rate', true);

        ob_start();
        ?>
        <div class="snap-cre-property-card" data-property-id="<?php echo esc_attr($post_id); ?>">
            <?php if ($thumbnail): ?>
                <div class="property-thumbnail">
                    <img src="<?php echo esc_url($thumbnail); ?>" alt="<?php echo esc_attr($title); ?>">
                </div>
            <?php endif; ?>
            
            <div class="property-content">
                <h3 class="property-title">
                    <a href="<?php echo esc_url($permalink); ?>"><?php echo esc_html($title); ?></a>
                </h3>
                
                <?php if ($address): ?>
                    <p class="property-address"><?php echo esc_html($address); ?></p>
                <?php endif; ?>
                
                <div class="property-meta">
                    <?php if ($property_types): ?>
                        <span class="property-types"><?php echo esc_html($property_types); ?></span>
                    <?php endif; ?>
                    
                    <?php if ($transaction_types): ?>
                        <span class="transaction-types"><?php echo esc_html($transaction_types); ?></span>
                    <?php endif; ?>
                </div>
                
                <div class="property-details">
                    <?php if ($building_size): ?>
                        <span class="building-size"><?php echo esc_html($building_size); ?> sq ft</span>
                    <?php endif; ?>
                    
                    <?php if ($lease_rate): ?>
                        <span class="lease-rate">$<?php echo esc_html($lease_rate); ?></span>
                    <?php endif; ?>
                </div>
                
                <a href="<?php echo esc_url($permalink); ?>" class="property-link">View Details</a>
            </div>
        </div>
        <?php
        return ob_get_clean();
    }

    public static function render_filter_form($atts = [])
    {
        $property_types = Config::get_option('property-types', []);
        $transaction_types = Config::get_option('transaction-types', []);

        ob_start();
        ?>
        <div class="snap-cre-filter-wrapper">
            <form class="snap-cre-filter-form" id="snap-cre-filter-form">
                <div class="filter-row">
                    <div class="filter-field">
                        <label for="search-term"><?php _e('Search', 'snap-cre'); ?></label>
                        <input type="text" id="search-term" name="search_term" placeholder="<?php _e('Search properties...', 'snap-cre'); ?>">
                    </div>
                    
                    <div class="filter-field">
                        <label for="property-types"><?php _e('Property Types', 'snap-cre'); ?></label>
                        <select id="property-types" name="property_types[]" multiple>
                            <?php foreach ($property_types as $type): ?>
                                <option value="<?php echo esc_attr($type['id']); ?>">
                                    <?php echo esc_html($type['name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="filter-field">
                        <label for="transaction-types"><?php _e('Transaction Types', 'snap-cre'); ?></label>
                        <select id="transaction-types" name="transaction_types[]" multiple>
                            <?php foreach ($transaction_types as $type): ?>
                                <option value="<?php echo esc_attr($type['id']); ?>">
                                    <?php echo esc_html($type['name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="filter-field">
                        <button type="submit" class="filter-submit"><?php _e('Filter', 'snap-cre'); ?></button>
                        <button type="button" class="filter-reset"><?php _e('Reset', 'snap-cre'); ?></button>
                    </div>
                </div>
            </form>
        </div>
        <?php
        return ob_get_clean();
    }

    public static function render_properties_grid($query = null)
    {
        if (!$query) {
            $query = self::get_properties_for_display();
        }

        ob_start();
        ?>
        <div class="snap-cre-results" id="snap-cre-results">
            <div class="properties-grid">
                <?php if ($query->have_posts()): ?>
                    <?php while ($query->have_posts()): $query->the_post(); ?>
                        <?php echo self::render_property_card(get_the_ID()); ?>
                    <?php endwhile; ?>
                    <?php wp_reset_postdata(); ?>
                <?php else: ?>
                    <p class="no-properties"><?php _e('No properties found.', 'snap-cre'); ?></p>
                <?php endif; ?>
            </div>
        </div>
        <?php
        return ob_get_clean();
    }
}
