<?php

namespace SnapCRE;

class Shortcodes
{
    public static function init()
    {
        add_shortcode('snapcre_properties', [__CLASS__, 'properties_shortcode']);
        add_shortcode('snapcre_property_filters', [__CLASS__, 'property_filters_shortcode']);
        add_shortcode('snapcre_property_grid', [__CLASS__, 'property_grid_shortcode']);
    }

    /**
     * Main properties shortcode that includes both filters and results
     * Usage: [snapcre_properties property_types="1,2,3" transaction_types="1,2"]
     */
    public static function properties_shortcode($atts = [])
    {
        $atts = shortcode_atts([
            'property_types' => '',
            'transaction_types' => '',
            'show_filters' => 'true',
            'show_map' => 'false',
            'posts_per_page' => -1,
        ], $atts, 'snapcre_properties');

        ob_start();

        // Show filters if enabled
        if ($atts['show_filters'] === 'true') {
            echo PublicFrontend::render_filter_form($atts);
        }

        // Get and display properties
        $query = PublicFrontend::get_properties_for_display($atts);
        echo PublicFrontend::render_properties_grid($query);

        // Show map if enabled
        if ($atts['show_map'] === 'true') {
            echo self::render_properties_map($query);
        }

        return ob_get_clean();
    }

    /**
     * Property filters only shortcode
     * Usage: [snapcre_property_filters]
     */
    public static function property_filters_shortcode($atts = [])
    {
        $atts = shortcode_atts([
            'target' => '#snap-cre-results',
        ], $atts, 'snapcre_property_filters');

        return PublicFrontend::render_filter_form($atts);
    }

    /**
     * Property grid only shortcode
     * Usage: [snapcre_property_grid property_types="1,2" transaction_types="1"]
     */
    public static function property_grid_shortcode($atts = [])
    {
        $atts = shortcode_atts([
            'property_types' => '',
            'transaction_types' => '',
            'posts_per_page' => -1,
            'columns' => '3',
        ], $atts, 'snapcre_property_grid');

        $query = PublicFrontend::get_properties_for_display($atts);
        
        ob_start();
        ?>
        <div class="snap-cre-property-grid columns-<?php echo esc_attr($atts['columns']); ?>">
            <?php echo PublicFrontend::render_properties_grid($query); ?>
        </div>
        <?php
        return ob_get_clean();
    }

    /**
     * Render properties map
     */
    private static function render_properties_map($query)
    {
        $properties_with_coords = [];

        if ($query->have_posts()) {
            while ($query->have_posts()) {
                $query->the_post();
                $post_id = get_the_ID();
                $latitude = get_post_meta($post_id, 'latitude', true);
                $longitude = get_post_meta($post_id, 'longitude', true);

                if ($latitude && $longitude) {
                    $properties_with_coords[] = [
                        'id' => $post_id,
                        'title' => get_the_title(),
                        'permalink' => get_permalink(),
                        'address' => get_post_meta($post_id, 'address', true),
                        'latitude' => floatval($latitude),
                        'longitude' => floatval($longitude),
                        'thumbnail' => get_the_post_thumbnail_url($post_id, 'thumbnail'),
                    ];
                }
            }
            wp_reset_postdata();
        }

        if (empty($properties_with_coords)) {
            return '<p>' . __('No properties with coordinates found for map display.', 'snap-cre') . '</p>';
        }

        ob_start();
        ?>
        <div class="snap-cre-map-container">
            <div id="snap-cre-properties-map" style="height: 400px; width: 100%;"></div>
        </div>

        <script>
        jQuery(document).ready(function($) {
            if (typeof L !== 'undefined') {
                var map = L.map('snap-cre-properties-map').setView([40.7128, -74.0060], 10);
                
                L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                    attribution: '© OpenStreetMap contributors'
                }).addTo(map);

                var markers = L.markerClusterGroup();
                var properties = <?php echo json_encode($properties_with_coords); ?>;
                
                properties.forEach(function(property) {
                    var marker = L.marker([property.latitude, property.longitude]);
                    
                    var popupContent = '<div class="property-popup">' +
                        '<h4><a href="' + property.permalink + '">' + property.title + '</a></h4>';
                    
                    if (property.address) {
                        popupContent += '<p>' + property.address + '</p>';
                    }
                    
                    if (property.thumbnail) {
                        popupContent += '<img src="' + property.thumbnail + '" alt="' + property.title + '" style="max-width: 150px; height: auto;">';
                    }
                    
                    popupContent += '</div>';
                    
                    marker.bindPopup(popupContent);
                    markers.addLayer(marker);
                });

                map.addLayer(markers);
                
                // Fit map to show all markers
                if (properties.length > 0) {
                    var group = new L.featureGroup(markers.getLayers());
                    map.fitBounds(group.getBounds().pad(0.1));
                }
            }
        });
        </script>
        <?php
        return ob_get_clean();
    }

    /**
     * Get shortcode usage instructions for admin
     */
    public static function get_shortcode_help()
    {
        return [
            'snapcre_properties' => [
                'description' => __('Display properties with optional filters and map', 'snap-cre'),
                'attributes' => [
                    'property_types' => __('Comma-separated property type IDs to filter by', 'snap-cre'),
                    'transaction_types' => __('Comma-separated transaction type IDs to filter by', 'snap-cre'),
                    'show_filters' => __('Show filter form (true/false, default: true)', 'snap-cre'),
                    'show_map' => __('Show properties map (true/false, default: false)', 'snap-cre'),
                    'posts_per_page' => __('Number of properties to show (-1 for all, default: -1)', 'snap-cre'),
                ],
                'examples' => [
                    '[snapcre_properties]',
                    '[snapcre_properties property_types="1,2,3"]',
                    '[snapcre_properties transaction_types="1,2" show_map="true"]',
                    '[snapcre_properties property_types="1" show_filters="false"]',
                ],
            ],
            'snapcre_property_filters' => [
                'description' => __('Display only the property filter form', 'snap-cre'),
                'attributes' => [
                    'target' => __('CSS selector for results container (default: #snap-cre-results)', 'snap-cre'),
                ],
                'examples' => [
                    '[snapcre_property_filters]',
                    '[snapcre_property_filters target="#my-results"]',
                ],
            ],
            'snapcre_property_grid' => [
                'description' => __('Display only the properties grid without filters', 'snap-cre'),
                'attributes' => [
                    'property_types' => __('Comma-separated property type IDs to filter by', 'snap-cre'),
                    'transaction_types' => __('Comma-separated transaction type IDs to filter by', 'snap-cre'),
                    'posts_per_page' => __('Number of properties to show (-1 for all, default: -1)', 'snap-cre'),
                    'columns' => __('Number of columns in grid (default: 3)', 'snap-cre'),
                ],
                'examples' => [
                    '[snapcre_property_grid]',
                    '[snapcre_property_grid property_types="1,2" columns="4"]',
                    '[snapcre_property_grid transaction_types="1" posts_per_page="6"]',
                ],
            ],
        ];
    }
}
