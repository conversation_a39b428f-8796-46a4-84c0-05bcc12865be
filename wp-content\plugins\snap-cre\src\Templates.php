<?php

namespace SnapCRE;

class Templates
{
    public static function init()
    {
        // Templates are loaded via PublicFrontend class
        // This class provides template loading utilities
    }

    /**
     * Load a template part from the plugin templates directory
     * Similar to get_template_part() but for plugin templates
     */
    public static function get_template_part($slug, $name = null, $args = null)
    {
        do_action("snap_cre_get_template_part_{$slug}", $slug, $name, $args);

        $templates = [];
        if (isset($name)) {
            $templates[] = "{$slug}-{$name}.php";
        }
        $templates[] = "{$slug}.php";

        self::load_template($templates, true, false, $args);
    }

    /**
     * Locate a template file
     */
    public static function locate_template($template_names, $load = false, $require_once = true, $args = [])
    {
        $located = '';
        
        foreach ((array) $template_names as $template_name) {
            if (!$template_name) {
                continue;
            }

            // Check theme directory first
            if (file_exists(get_stylesheet_directory() . '/snap-cre/' . $template_name)) {
                $located = get_stylesheet_directory() . '/snap-cre/' . $template_name;
                break;
            }

            // Check parent theme directory
            if (is_child_theme() && file_exists(get_template_directory() . '/snap-cre/' . $template_name)) {
                $located = get_template_directory() . '/snap-cre/' . $template_name;
                break;
            }

            // Check plugin templates directory
            if (file_exists(SNAP_CRE_PLUGIN_DIR . 'templates/' . $template_name)) {
                $located = SNAP_CRE_PLUGIN_DIR . 'templates/' . $template_name;
                break;
            }
        }

        if ($load && '' != $located) {
            load_template($located, $require_once, $args);
        }

        return $located;
    }

    /**
     * Load template with fallback
     */
    private static function load_template($template_names, $load = false, $require_once = true, $args = null)
    {
        $located = '';
        
        foreach ((array) $template_names as $template_name) {
            if (!$template_name) {
                continue;
            }

            // Search in plugin template directory
            if (file_exists(SNAP_CRE_PLUGIN_DIR . 'templates/' . $template_name)) {
                $located = SNAP_CRE_PLUGIN_DIR . 'templates/' . $template_name;
                break;
            }
        }

        if ($load && '' != $located) {
            load_template($located, $require_once, $args);
        }

        return $located;
    }

    /**
     * Get template content as string
     */
    public static function get_template_content($template_name, $args = [])
    {
        $template_path = self::locate_template($template_name);
        
        if (!$template_path) {
            return '';
        }

        ob_start();
        
        // Extract args to variables
        if (!empty($args) && is_array($args)) {
            extract($args);
        }
        
        include $template_path;
        
        return ob_get_clean();
    }

    /**
     * Render property single page template
     */
    public static function render_single_property($post)
    {
        $property_id = $post->ID;
        $address = get_post_meta($property_id, 'address', true);
        $property_types = Utils::get_option_labels_from_meta($property_id, 'property_types', 'property_types');
        $transaction_types = Utils::get_option_labels_from_meta($property_id, 'transaction_types', 'transaction_types');
        $building_size = get_post_meta($property_id, 'building_size', true);
        $building_class = get_post_meta($property_id, 'building_class', true);
        $lease_rate = get_post_meta($property_id, 'lease_rate', true);
        $year_built = get_post_meta($property_id, 'year_built', true);
        $status = get_post_meta($property_id, 'status', true);
        $latitude = get_post_meta($property_id, 'latitude', true);
        $longitude = get_post_meta($property_id, 'longitude', true);
        $property_flyer = get_post_meta($property_id, 'property_flyer', true);
        $documents_gallery = get_post_meta($property_id, 'documents_gallery', true);
        $photo_gallery = get_post_meta($property_id, 'photo_gallery', true);
        $demographics = get_post_meta($property_id, 'demographics', true);
        $related_agents = get_post_meta($property_id, 'related_agents', true);

        $args = compact(
            'post', 'property_id', 'address', 'property_types', 'transaction_types',
            'building_size', 'building_class', 'lease_rate', 'year_built', 'status',
            'latitude', 'longitude', 'property_flyer', 'documents_gallery',
            'photo_gallery', 'demographics', 'related_agents'
        );

        return self::get_template_content('single-property.php', $args);
    }

    /**
     * Render agent single page template
     */
    public static function render_single_agent($post)
    {
        $agent_id = $post->ID;
        $email = get_post_meta($agent_id, 'email', true);
        $phone = get_post_meta($agent_id, 'phone', true);
        $bio = get_post_meta($agent_id, 'bio', true);

        $args = compact('post', 'agent_id', 'email', 'phone', 'bio');

        return self::get_template_content('single-agent.php', $args);
    }

    /**
     * Get available template files
     */
    public static function get_available_templates()
    {
        $templates = [];
        $template_dir = SNAP_CRE_PLUGIN_DIR . 'templates/';

        if (is_dir($template_dir)) {
            $files = scandir($template_dir);
            foreach ($files as $file) {
                if (pathinfo($file, PATHINFO_EXTENSION) === 'php') {
                    $templates[] = $file;
                }
            }
        }

        return $templates;
    }

    /**
     * Check if template exists
     */
    public static function template_exists($template_name)
    {
        return !empty(self::locate_template($template_name));
    }

    /**
     * Get template hierarchy for a given template
     */
    public static function get_template_hierarchy($base_name, $specific_name = null)
    {
        $templates = [];

        if ($specific_name) {
            $templates[] = "{$base_name}-{$specific_name}.php";
        }

        $templates[] = "{$base_name}.php";

        return $templates;
    }

    /**
     * Register template directory with WordPress
     */
    public static function add_template_directory()
    {
        add_filter('theme_page_templates', function($templates) {
            $plugin_templates = [
                'snap-cre-properties.php' => __('SnapCRE Properties', 'snap-cre'),
                'snap-cre-agents.php' => __('SnapCRE Agents', 'snap-cre'),
            ];

            return array_merge($templates, $plugin_templates);
        });
    }
}
