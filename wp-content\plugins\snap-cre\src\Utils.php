<?php

namespace SnapCRE;

class Utils
{
    /**
     * Get IP address of the current user
     */
    public static function get_ip_address()
    {
        // Check for IP from shared internet
        if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
            $ip = $_SERVER['HTTP_CLIENT_IP'];
        }
        // Check for IP passed from proxy
        elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
            $ip = $_SERVER['HTTP_X_FORWARDED_FOR'];
        }
        // Check for IP from remote address
        else {
            $ip = $_SERVER['REMOTE_ADDR'];
        }
        
        // Convert localhost to standard IP
        if ($ip == '::1') {
            $ip = '127.0.0.1';
        }
        
        return $ip;
    }

    /**
     * Get option labels from meta value
     * Converts stored IDs to human-readable labels
     */
    public static function get_option_labels_from_meta($post_id, $meta_key, $option_key)
    {
        $meta_value = get_post_meta($post_id, $meta_key, true);
        $options = Config::get_option($option_key, []);

        if (empty($options) || !is_array($options)) {
            return '';
        }

        $selected_ids = is_array($meta_value) ? $meta_value : (array) $meta_value;
        $labels = [];

        foreach ($options as $option) {
            if (isset($option['id'], $option['name']) && in_array($option['id'], $selected_ids)) {
                $labels[] = $option['name'];
            }
        }

        return implode(', ', $labels);
    }

    /**
     * Check if request is from current website
     */
    public static function is_from_current_website()
    {
        $ref = isset($_SERVER['HTTP_REFERER']) ? $_SERVER['HTTP_REFERER'] : '';
        $ref_data = parse_url($ref);
        $ref_domain = isset($ref_data['host']) ? $ref_data['host'] : '';

        $url_parts = parse_url(home_url());
        $domain = $url_parts['host'];

        return $ref_domain === $domain;
    }

    /**
     * Send email using WordPress wp_mail with SnapCRE settings
     */
    public static function send_email($to, $subject, $message, $attachment = '')
    {
        $headers = ['Content-Type: text/html; charset=UTF-8'];
        $headers[] = 'From: ' . Config::$email_from_name . ' <' . Config::$email_from_email . '>';
        $headers[] = 'Reply-To: ' . Config::$email_from_name . ' <' . Config::$email_from_email . '>';
        
        if (Config::$email_bcc) {
            $headers[] = 'Bcc: ' . Config::$email_bcc;
        }
        
        if (Config::$email_cc) {
            $headers[] = 'Cc: ' . Config::$email_cc;
        }
        
        $headers[] = 'X-Mailer: PHP/' . phpversion();
        $headers[] = 'X-Priority: 1';
        $headers[] = 'X-MSMail-Priority: High';
        $headers[] = 'Importance: High';
        $headers[] = 'MIME-Version: 1.0';
        $headers[] = 'Content-Transfer-Encoding: 8bit';
        $headers[] = 'Date: ' . date('r', $_SERVER['REQUEST_TIME']);
        $headers[] = 'X-MS-Has-Attach: ' . (!empty($attachment) ? 'yes' : 'no');

        return wp_mail($to, $subject, $message, $headers, $attachment);
    }

    /**
     * Render frontend multiselect dropdown
     */
    public static function frontend_multiselect($args = [])
    {
        $defaults = [
            'option_key' => '',
            'field_name' => '',
            'label' => '',
            'placeholder' => 'Select',
            'group_class' => '',
            'selected_values' => [],
        ];
        
        $args = wp_parse_args($args, $defaults);

        if (empty($args['option_key']) || empty($args['field_name'])) {
            return 'Missing required parameters.';
        }

        $options = Config::get_option($args['option_key'], []);
        $selected_values = (array) $args['selected_values'];

        ob_start();
        ?>
        <div class="snap-cre-multiselect-wrapper <?php echo esc_attr($args['group_class']); ?>">
            <label class="snap-cre-multiselect-label">
                <?php echo esc_html($args['label']); ?>
            </label>
            
            <div class="snap-cre-multiselect-dropdown">
                <button type="button" class="snap-cre-multiselect-toggle">
                    <span class="selected-text"><?php echo esc_html($args['placeholder']); ?></span>
                    <span class="dropdown-arrow">▼</span>
                </button>
                
                <div class="snap-cre-multiselect-options" style="display: none;">
                    <div class="option-item">
                        <input type="checkbox" class="select-all" id="<?php echo esc_attr($args['field_name']); ?>-all">
                        <label for="<?php echo esc_attr($args['field_name']); ?>-all">All</label>
                    </div>
                    
                    <?php if (!empty($options) && is_array($options)): ?>
                        <?php foreach ($options as $index => $option): ?>
                            <div class="option-item">
                                <input
                                    type="checkbox"
                                    class="option-checkbox"
                                    name="<?php echo esc_attr($args['field_name']); ?>[]"
                                    value="<?php echo esc_attr($option['id']); ?>"
                                    id="<?php echo esc_attr($args['field_name']); ?>-<?php echo $index; ?>"
                                    <?php checked(in_array($option['id'], $selected_values)); ?>
                                >
                                <label for="<?php echo esc_attr($args['field_name']); ?>-<?php echo $index; ?>">
                                    <?php echo esc_html($option['name']); ?>
                                </label>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        <?php
        return ob_get_clean();
    }

    /**
     * Sanitize array of integers
     */
    public static function sanitize_int_array($array)
    {
        if (!is_array($array)) {
            return [];
        }

        return array_map('intval', array_filter($array, 'is_numeric'));
    }

    /**
     * Sanitize array of strings
     */
    public static function sanitize_text_array($array)
    {
        if (!is_array($array)) {
            return [];
        }

        return array_map('sanitize_text_field', $array);
    }

    /**
     * Format file size
     */
    public static function format_file_size($bytes, $precision = 2)
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, $precision) . ' ' . $units[$i];
    }

    /**
     * Get property status options
     */
    public static function get_property_status_options()
    {
        return [
            'available' => __('Available', 'snap-cre'),
            'under_contract' => __('Under Contract', 'snap-cre'),
            'sold' => __('Sold', 'snap-cre'),
            'leased' => __('Leased', 'snap-cre'),
            'off_market' => __('Off Market', 'snap-cre'),
        ];
    }

    /**
     * Get building class options
     */
    public static function get_building_class_options()
    {
        return [
            'class_a' => __('Class A', 'snap-cre'),
            'class_b' => __('Class B', 'snap-cre'),
            'class_c' => __('Class C', 'snap-cre'),
        ];
    }

    /**
     * Format currency
     */
    public static function format_currency($amount, $currency = 'USD')
    {
        if (!is_numeric($amount)) {
            return $amount;
        }

        $formatted = number_format($amount, 2);
        
        switch ($currency) {
            case 'USD':
                return '$' . $formatted;
            default:
                return $formatted . ' ' . $currency;
        }
    }

    /**
     * Format square footage
     */
    public static function format_square_feet($sqft)
    {
        if (!is_numeric($sqft)) {
            return $sqft;
        }

        return number_format($sqft) . ' sq ft';
    }

    /**
     * Generate unique ID
     */
    public static function generate_unique_id($prefix = '')
    {
        return $prefix . uniqid() . mt_rand(1000, 9999);
    }

    /**
     * Check if user can access property
     */
    public static function can_user_access_property($property_id, $user_id = null)
    {
        if (!$user_id) {
            $user_id = get_current_user_id();
        }

        // Admins can access everything
        if (user_can($user_id, 'manage_options')) {
            return true;
        }

        // Check if property requires confidentiality agreement
        $ca_required = get_post_meta($property_id, 'ca_check', true);
        
        if (!$ca_required) {
            return true;
        }

        // Check if user has signed agreement for this property
        // This would need to be implemented based on your agreement system
        return false;
    }

    /**
     * Log activity
     */
    public static function log_activity($action, $object_id = null, $user_id = null, $details = [])
    {
        if (!$user_id) {
            $user_id = get_current_user_id();
        }

        $log_entry = [
            'action' => $action,
            'object_id' => $object_id,
            'user_id' => $user_id,
            'timestamp' => current_time('mysql'),
            'ip_address' => self::get_ip_address(),
            'details' => $details,
        ];

        // Store in database or log file
        // Implementation depends on your logging requirements
        do_action('snap_cre_log_activity', $log_entry);
    }

    /**
     * Get plugin version
     */
    public static function get_plugin_version()
    {
        return SNAP_CRE_VERSION;
    }

    /**
     * Check if plugin is in debug mode
     */
    public static function is_debug_mode()
    {
        return defined('WP_DEBUG') && WP_DEBUG;
    }
}
