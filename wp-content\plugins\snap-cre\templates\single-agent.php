<?php
/**
 * Single Agent Template
 * 
 * This template displays a single agent post
 * 
 * @package SnapCRE
 */

if (!defined('ABSPATH')) {
    exit;
}

get_header(); ?>

<main class="snap-cre-single-agent">
    <div class="snap-cre-container">
        <?php while (have_posts()) : the_post(); ?>
            <?php
            $agent_id = get_the_ID();
            $email = get_post_meta($agent_id, 'email', true);
            $phone = get_post_meta($agent_id, 'phone', true);
            $bio = get_post_meta($agent_id, 'bio', true);
            ?>

            <header class="agent-header">
                <div class="agent-info">
                    <?php if (has_post_thumbnail()): ?>
                        <div class="agent-photo">
                            <?php the_post_thumbnail('medium'); ?>
                        </div>
                    <?php endif; ?>
                    
                    <div class="agent-details">
                        <h1 class="agent-name"><?php the_title(); ?></h1>
                        
                        <div class="agent-contact">
                            <?php if ($email): ?>
                                <p class="agent-email">
                                    <i class="fas fa-envelope"></i>
                                    <a href="mailto:<?php echo esc_attr($email); ?>"><?php echo esc_html($email); ?></a>
                                </p>
                            <?php endif; ?>
                            
                            <?php if ($phone): ?>
                                <p class="agent-phone">
                                    <i class="fas fa-phone"></i>
                                    <a href="tel:<?php echo esc_attr($phone); ?>"><?php echo esc_html($phone); ?></a>
                                </p>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </header>

            <div class="agent-content">
                <?php if ($bio): ?>
                    <section class="agent-bio">
                        <h2><?php _e('About', 'snap-cre'); ?></h2>
                        <div class="bio-content">
                            <?php echo wp_kses_post(wpautop($bio)); ?>
                        </div>
                    </section>
                <?php endif; ?>

                <?php if (get_the_content()): ?>
                    <section class="agent-description">
                        <h2><?php _e('Profile', 'snap-cre'); ?></h2>
                        <div class="description-content">
                            <?php the_content(); ?>
                        </div>
                    </section>
                <?php endif; ?>

                <!-- Agent's Properties -->
                <?php
                $agent_properties = new WP_Query([
                    'post_type' => \SnapCRE\Config::$properties_post_slug,
                    'meta_query' => [
                        [
                            'key' => 'related_agents',
                            'value' => $agent_id,
                            'compare' => 'LIKE'
                        ]
                    ],
                    'posts_per_page' => -1,
                    'post_status' => 'publish'
                ]);
                ?>

                <?php if ($agent_properties->have_posts()): ?>
                    <section class="agent-properties">
                        <h2><?php _e('Properties', 'snap-cre'); ?></h2>
                        <div class="properties-grid">
                            <?php while ($agent_properties->have_posts()): $agent_properties->the_post(); ?>
                                <?php echo \SnapCRE\PublicFrontend::render_property_card(get_the_ID()); ?>
                            <?php endwhile; ?>
                            <?php wp_reset_postdata(); ?>
                        </div>
                    </section>
                <?php endif; ?>

                <!-- Contact Form -->
                <section class="agent-contact-form">
                    <h2><?php _e('Contact Agent', 'snap-cre'); ?></h2>
                    <form class="contact-agent-form" method="post" action="">
                        <?php wp_nonce_field('contact_agent_' . $agent_id, 'contact_agent_nonce'); ?>
                        <input type="hidden" name="agent_id" value="<?php echo esc_attr($agent_id); ?>">
                        
                        <div class="form-row">
                            <div class="form-field">
                                <label for="contact_name"><?php _e('Your Name', 'snap-cre'); ?> *</label>
                                <input type="text" id="contact_name" name="contact_name" required>
                            </div>
                            
                            <div class="form-field">
                                <label for="contact_email"><?php _e('Your Email', 'snap-cre'); ?> *</label>
                                <input type="email" id="contact_email" name="contact_email" required>
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-field">
                                <label for="contact_phone"><?php _e('Your Phone', 'snap-cre'); ?></label>
                                <input type="tel" id="contact_phone" name="contact_phone">
                            </div>
                            
                            <div class="form-field">
                                <label for="contact_subject"><?php _e('Subject', 'snap-cre'); ?></label>
                                <input type="text" id="contact_subject" name="contact_subject">
                            </div>
                        </div>
                        
                        <div class="form-field">
                            <label for="contact_message"><?php _e('Message', 'snap-cre'); ?> *</label>
                            <textarea id="contact_message" name="contact_message" rows="5" required></textarea>
                        </div>
                        
                        <button type="submit" name="submit_contact" class="submit-btn">
                            <?php _e('Send Message', 'snap-cre'); ?>
                        </button>
                    </form>
                </section>
            </div>

        <?php endwhile; ?>
    </div>
</main>

<style>
.snap-cre-single-agent {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.agent-header {
    margin-bottom: 40px;
    background: #f9f9f9;
    padding: 30px;
    border-radius: 10px;
}

.agent-info {
    display: flex;
    gap: 30px;
    align-items: flex-start;
}

.agent-photo {
    flex-shrink: 0;
}

.agent-photo img {
    border-radius: 10px;
    max-width: 200px;
    height: auto;
}

.agent-details {
    flex: 1;
}

.agent-name {
    font-size: 2.5em;
    margin-bottom: 20px;
    color: #333;
}

.agent-contact p {
    margin-bottom: 10px;
    font-size: 1.1em;
}

.agent-contact i {
    margin-right: 10px;
    color: #007cba;
    width: 20px;
}

.agent-contact a {
    color: #333;
    text-decoration: none;
}

.agent-contact a:hover {
    color: #007cba;
}

.agent-content section {
    margin-bottom: 40px;
}

.agent-content h2 {
    font-size: 1.8em;
    margin-bottom: 20px;
    color: #333;
    border-bottom: 2px solid #007cba;
    padding-bottom: 10px;
}

.bio-content,
.description-content {
    font-size: 1.1em;
    line-height: 1.6;
    color: #555;
}

.properties-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.contact-agent-form {
    background: #f9f9f9;
    padding: 30px;
    border-radius: 10px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.form-field {
    display: flex;
    flex-direction: column;
}

.form-field label {
    font-weight: bold;
    margin-bottom: 5px;
    color: #333;
}

.form-field input,
.form-field textarea {
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 1em;
}

.form-field input:focus,
.form-field textarea:focus {
    outline: none;
    border-color: #007cba;
    box-shadow: 0 0 5px rgba(0, 124, 186, 0.3);
}

.submit-btn {
    background: #007cba;
    color: white;
    padding: 15px 30px;
    border: none;
    border-radius: 5px;
    font-size: 1.1em;
    cursor: pointer;
    transition: background 0.3s ease;
}

.submit-btn:hover {
    background: #005a87;
}

@media (max-width: 768px) {
    .agent-info {
        flex-direction: column;
        text-align: center;
    }
    
    .agent-photo {
        align-self: center;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .properties-grid {
        grid-template-columns: 1fr;
    }
}
</style>

<?php
// Handle contact form submission
if (isset($_POST['submit_contact']) && wp_verify_nonce($_POST['contact_agent_nonce'], 'contact_agent_' . $agent_id)) {
    $contact_name = sanitize_text_field($_POST['contact_name']);
    $contact_email = sanitize_email($_POST['contact_email']);
    $contact_phone = sanitize_text_field($_POST['contact_phone']);
    $contact_subject = sanitize_text_field($_POST['contact_subject']);
    $contact_message = sanitize_textarea_field($_POST['contact_message']);
    
    if ($contact_name && $contact_email && $contact_message) {
        $subject = $contact_subject ? $contact_subject : sprintf(__('Contact from %s', 'snap-cre'), get_bloginfo('name'));
        $message = sprintf(
            __("You have received a new contact message:\n\nName: %s\nEmail: %s\nPhone: %s\n\nMessage:\n%s", 'snap-cre'),
            $contact_name,
            $contact_email,
            $contact_phone,
            $contact_message
        );
        
        if ($email && \SnapCRE\Utils::send_email($email, $subject, $message)) {
            echo '<div class="contact-success">' . __('Your message has been sent successfully!', 'snap-cre') . '</div>';
        } else {
            echo '<div class="contact-error">' . __('Sorry, there was an error sending your message. Please try again.', 'snap-cre') . '</div>';
        }
    }
}
?>

<style>
.contact-success {
    background: #d4edda;
    color: #155724;
    padding: 15px;
    border-radius: 5px;
    margin: 20px 0;
    border: 1px solid #c3e6cb;
}

.contact-error {
    background: #f8d7da;
    color: #721c24;
    padding: 15px;
    border-radius: 5px;
    margin: 20px 0;
    border: 1px solid #f5c6cb;
}
</style>

<?php get_footer(); ?>
