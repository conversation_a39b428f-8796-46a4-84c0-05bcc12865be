<?php
/**
 * Single Property Template
 * 
 * This template displays a single property post
 * 
 * @package SnapCRE
 */

if (!defined('ABSPATH')) {
    exit;
}

get_header(); ?>

<main class="snap-cre-single-property">
    <div class="snap-cre-container">
        <?php while (have_posts()) : the_post(); ?>
            <?php
            $property_id = get_the_ID();
            $address = get_post_meta($property_id, 'address', true);
            $property_types = \SnapCRE\Utils::get_option_labels_from_meta($property_id, 'property_types', 'property_types');
            $transaction_types = \SnapCRE\Utils::get_option_labels_from_meta($property_id, 'transaction_types', 'transaction_types');
            $building_size = get_post_meta($property_id, 'building_size', true);
            $building_class = get_post_meta($property_id, 'building_class', true);
            $lease_rate = get_post_meta($property_id, 'lease_rate', true);
            $year_built = get_post_meta($property_id, 'year_built', true);
            $status = get_post_meta($property_id, 'status', true);
            $latitude = get_post_meta($property_id, 'latitude', true);
            $longitude = get_post_meta($property_id, 'longitude', true);
            $property_flyer = get_post_meta($property_id, 'property_flyer', true);
            $documents_gallery = get_post_meta($property_id, 'documents_gallery', true);
            $photo_gallery = get_post_meta($property_id, 'photo_gallery', true);
            $demographics = get_post_meta($property_id, 'demographics', true);
            $related_agents = get_post_meta($property_id, 'related_agents', true);
            ?>

            <header class="property-header">
                <h1 class="property-title"><?php the_title(); ?></h1>
                <?php if ($address): ?>
                    <p class="property-address"><?php echo esc_html($address); ?></p>
                <?php endif; ?>
                
                <div class="property-meta">
                    <?php if ($property_types): ?>
                        <span class="property-types">
                            <strong><?php _e('Type:', 'snap-cre'); ?></strong> <?php echo esc_html($property_types); ?>
                        </span>
                    <?php endif; ?>
                    
                    <?php if ($transaction_types): ?>
                        <span class="transaction-types">
                            <strong><?php _e('Transaction:', 'snap-cre'); ?></strong> <?php echo esc_html($transaction_types); ?>
                        </span>
                    <?php endif; ?>
                    
                    <?php if ($status): ?>
                        <span class="property-status">
                            <strong><?php _e('Status:', 'snap-cre'); ?></strong> <?php echo esc_html($status); ?>
                        </span>
                    <?php endif; ?>
                </div>
            </header>

            <div class="property-tabs">
                <div class="tab-navigation">
                    <button class="tab-btn active" data-tab="overview">
                        <i class="fas fa-info-circle"></i> <?php _e('Overview', 'snap-cre'); ?>
                    </button>
                    <button class="tab-btn" data-tab="documents">
                        <i class="fas fa-file-alt"></i> <?php _e('Documents', 'snap-cre'); ?>
                    </button>
                    <button class="tab-btn" data-tab="photos">
                        <i class="fas fa-image"></i> <?php _e('Photos', 'snap-cre'); ?>
                    </button>
                    <button class="tab-btn" data-tab="map">
                        <i class="fas fa-map-marked-alt"></i> <?php _e('Map', 'snap-cre'); ?>
                    </button>
                    <?php if ($demographics): ?>
                        <button class="tab-btn" data-tab="demographics">
                            <i class="fas fa-users"></i> <?php _e('Demographics', 'snap-cre'); ?>
                        </button>
                    <?php endif; ?>
                </div>

                <div class="tab-content">
                    <!-- Overview Tab -->
                    <div id="overview" class="tab-pane active">
                        <div class="property-details">
                            <?php if (has_post_thumbnail()): ?>
                                <div class="property-featured-image">
                                    <?php the_post_thumbnail('large'); ?>
                                </div>
                            <?php endif; ?>

                            <div class="property-description">
                                <?php the_content(); ?>
                            </div>

                            <div class="property-specs">
                                <h3><?php _e('Property Specifications', 'snap-cre'); ?></h3>
                                <div class="specs-grid">
                                    <?php if ($building_size): ?>
                                        <div class="spec-item">
                                            <strong><?php _e('Building Size:', 'snap-cre'); ?></strong>
                                            <span><?php echo esc_html(\SnapCRE\Utils::format_square_feet($building_size)); ?></span>
                                        </div>
                                    <?php endif; ?>

                                    <?php if ($building_class): ?>
                                        <div class="spec-item">
                                            <strong><?php _e('Building Class:', 'snap-cre'); ?></strong>
                                            <span><?php echo esc_html($building_class); ?></span>
                                        </div>
                                    <?php endif; ?>

                                    <?php if ($lease_rate): ?>
                                        <div class="spec-item">
                                            <strong><?php _e('Lease Rate:', 'snap-cre'); ?></strong>
                                            <span><?php echo esc_html(\SnapCRE\Utils::format_currency($lease_rate)); ?></span>
                                        </div>
                                    <?php endif; ?>

                                    <?php if ($year_built): ?>
                                        <div class="spec-item">
                                            <strong><?php _e('Year Built:', 'snap-cre'); ?></strong>
                                            <span><?php echo esc_html($year_built); ?></span>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Documents Tab -->
                    <div id="documents" class="tab-pane">
                        <h3><?php _e('Documents', 'snap-cre'); ?></h3>
                        
                        <?php if ($property_flyer): ?>
                            <div class="property-flyer">
                                <h4><?php _e('Property Flyer', 'snap-cre'); ?></h4>
                                <a href="<?php echo esc_url($property_flyer); ?>" target="_blank" class="document-link">
                                    <?php _e('Download Property Flyer', 'snap-cre'); ?>
                                </a>
                            </div>
                        <?php endif; ?>

                        <?php if ($documents_gallery): ?>
                            <div class="documents-gallery">
                                <h4><?php _e('Additional Documents', 'snap-cre'); ?></h4>
                                <?php
                                $documents = explode("\n", $documents_gallery);
                                foreach ($documents as $doc_url) {
                                    $doc_url = trim($doc_url);
                                    if ($doc_url) {
                                        echo '<a href="' . esc_url($doc_url) . '" target="_blank" class="document-link">' . 
                                             basename($doc_url) . '</a><br>';
                                    }
                                }
                                ?>
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Photos Tab -->
                    <div id="photos" class="tab-pane">
                        <h3><?php _e('Photo Gallery', 'snap-cre'); ?></h3>
                        
                        <?php if ($photo_gallery): ?>
                            <div class="photo-gallery">
                                <?php
                                $photos = explode("\n", $photo_gallery);
                                foreach ($photos as $photo_url) {
                                    $photo_url = trim($photo_url);
                                    if ($photo_url) {
                                        echo '<div class="gallery-item">';
                                        echo '<img src="' . esc_url($photo_url) . '" alt="Property Photo" loading="lazy">';
                                        echo '</div>';
                                    }
                                }
                                ?>
                            </div>
                        <?php else: ?>
                            <p><?php _e('No photos available for this property.', 'snap-cre'); ?></p>
                        <?php endif; ?>
                    </div>

                    <!-- Map Tab -->
                    <div id="map" class="tab-pane">
                        <h3><?php _e('Location', 'snap-cre'); ?></h3>
                        
                        <?php if ($latitude && $longitude): ?>
                            <div id="property-map" style="height: 400px; width: 100%;"></div>
                            
                            <script>
                            jQuery(document).ready(function($) {
                                if (typeof L !== 'undefined') {
                                    var map = L.map('property-map').setView([<?php echo floatval($latitude); ?>, <?php echo floatval($longitude); ?>], 15);
                                    
                                    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                                        attribution: '© OpenStreetMap contributors'
                                    }).addTo(map);

                                    var marker = L.marker([<?php echo floatval($latitude); ?>, <?php echo floatval($longitude); ?>]).addTo(map);
                                    marker.bindPopup('<strong><?php echo esc_js(get_the_title()); ?></strong><br><?php echo esc_js($address); ?>');
                                }
                            });
                            </script>
                        <?php else: ?>
                            <p><?php _e('Location coordinates not available for this property.', 'snap-cre'); ?></p>
                        <?php endif; ?>
                    </div>

                    <!-- Demographics Tab -->
                    <?php if ($demographics): ?>
                        <div id="demographics" class="tab-pane">
                            <h3><?php _e('Demographics', 'snap-cre'); ?></h3>
                            <div class="demographics-content">
                                <?php echo wp_kses_post(wpautop($demographics)); ?>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Related Agents -->
            <?php if ($related_agents): ?>
                <div class="related-agents">
                    <h3><?php _e('Contact Agents', 'snap-cre'); ?></h3>
                    <?php
                    $agent_ids = explode(',', $related_agents);
                    foreach ($agent_ids as $agent_id) {
                        $agent_id = trim($agent_id);
                        if ($agent_id && get_post($agent_id)) {
                            $agent_email = get_post_meta($agent_id, 'email', true);
                            $agent_phone = get_post_meta($agent_id, 'phone', true);
                            ?>
                            <div class="agent-card">
                                <h4><a href="<?php echo get_permalink($agent_id); ?>"><?php echo get_the_title($agent_id); ?></a></h4>
                                <?php if ($agent_email): ?>
                                    <p><strong><?php _e('Email:', 'snap-cre'); ?></strong> <a href="mailto:<?php echo esc_attr($agent_email); ?>"><?php echo esc_html($agent_email); ?></a></p>
                                <?php endif; ?>
                                <?php if ($agent_phone): ?>
                                    <p><strong><?php _e('Phone:', 'snap-cre'); ?></strong> <a href="tel:<?php echo esc_attr($agent_phone); ?>"><?php echo esc_html($agent_phone); ?></a></p>
                                <?php endif; ?>
                            </div>
                            <?php
                        }
                    }
                    ?>
                </div>
            <?php endif; ?>

        <?php endwhile; ?>
    </div>
</main>

<style>
.snap-cre-single-property {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.property-header {
    margin-bottom: 30px;
    text-align: center;
}

.property-title {
    font-size: 2.5em;
    margin-bottom: 10px;
}

.property-address {
    font-size: 1.2em;
    color: #666;
    margin-bottom: 15px;
}

.property-meta {
    display: flex;
    justify-content: center;
    gap: 20px;
    flex-wrap: wrap;
}

.property-meta span {
    background: #f5f5f5;
    padding: 5px 10px;
    border-radius: 5px;
}

.tab-navigation {
    display: flex;
    border-bottom: 2px solid #ddd;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.tab-btn {
    background: none;
    border: none;
    padding: 15px 20px;
    cursor: pointer;
    border-bottom: 3px solid transparent;
    transition: all 0.3s ease;
}

.tab-btn.active {
    border-bottom-color: #007cba;
    background: #f9f9f9;
}

.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block;
}

.specs-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
    margin-top: 20px;
}

.spec-item {
    background: #f9f9f9;
    padding: 15px;
    border-radius: 5px;
}

.photo-gallery {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.gallery-item img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    border-radius: 5px;
}

.agent-card {
    background: #f9f9f9;
    padding: 20px;
    margin-bottom: 15px;
    border-radius: 5px;
}

.document-link {
    display: inline-block;
    background: #007cba;
    color: white;
    padding: 10px 15px;
    text-decoration: none;
    border-radius: 5px;
    margin: 5px 0;
}

.document-link:hover {
    background: #005a87;
    color: white;
}

@media (max-width: 768px) {
    .property-meta {
        flex-direction: column;
        align-items: center;
    }
    
    .tab-navigation {
        flex-direction: column;
    }
    
    .specs-grid {
        grid-template-columns: 1fr;
    }
}
</style>

<script>
jQuery(document).ready(function($) {
    $('.tab-btn').on('click', function() {
        var target = $(this).data('tab');
        
        // Update navigation
        $('.tab-btn').removeClass('active');
        $(this).addClass('active');
        
        // Update content
        $('.tab-pane').removeClass('active');
        $('#' + target).addClass('active');
    });
});
</script>

<?php get_footer(); ?>
