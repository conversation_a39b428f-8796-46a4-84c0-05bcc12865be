<?php
/**
 * Simple test script to verify SnapCRE plugin functionality
 * Run this from WordPress admin or via WP-CLI
 */

// Ensure we're in WordPress environment
if (!defined('ABSPATH')) {
    die('This script must be run within WordPress.');
}

echo "<h2>SnapCRE Plugin Test</h2>\n";

// Test 1: Check if plugin files exist
echo "<h3>1. File Structure Test</h3>\n";
$required_files = [
    'snap-cre.php',
    'src/Config.php',
    'src/Dashboard.php',
    'src/PostTypes.php',
    'src/MetaBoxes.php',
    'src/PublicFrontend.php',
    'src/Shortcodes.php',
    'src/Templates.php',
    'src/Utils.php',
    'assets/admin.css',
    'assets/admin.js',
    'assets/public.css',
    'assets/public.js',
    'templates/single-property.php',
    'templates/single-agent.php',
];

$plugin_dir = WP_PLUGIN_DIR . '/snap-cre/';
$missing_files = [];

foreach ($required_files as $file) {
    if (!file_exists($plugin_dir . $file)) {
        $missing_files[] = $file;
    }
}

if (empty($missing_files)) {
    echo "✅ All required files exist<br>\n";
} else {
    echo "❌ Missing files: " . implode(', ', $missing_files) . "<br>\n";
}

// Test 2: Check if classes can be loaded
echo "<h3>2. Class Loading Test</h3>\n";
$classes = [
    'SnapCRE\Config',
    'SnapCRE\Dashboard',
    'SnapCRE\PostTypes',
    'SnapCRE\MetaBoxes',
    'SnapCRE\PublicFrontend',
    'SnapCRE\Shortcodes',
    'SnapCRE\Templates',
    'SnapCRE\Utils',
];

$missing_classes = [];
foreach ($classes as $class) {
    if (!class_exists($class)) {
        $missing_classes[] = $class;
    }
}

if (empty($missing_classes)) {
    echo "✅ All classes loaded successfully<br>\n";
} else {
    echo "❌ Missing classes: " . implode(', ', $missing_classes) . "<br>\n";
}

// Test 3: Check default options
echo "<h3>3. Default Options Test</h3>\n";
$property_types = get_option('snap-cre-property-types', []);
$transaction_types = get_option('snap-cre-transaction-types', []);

if (!empty($property_types)) {
    echo "✅ Property types loaded: " . count($property_types) . " items<br>\n";
    foreach ($property_types as $type) {
        echo "  - " . $type['name'] . " (ID: " . $type['id'] . ")<br>\n";
    }
} else {
    echo "❌ No property types found<br>\n";
}

if (!empty($transaction_types)) {
    echo "✅ Transaction types loaded: " . count($transaction_types) . " items<br>\n";
    foreach ($transaction_types as $type) {
        echo "  - " . $type['name'] . " (ID: " . $type['id'] . ")<br>\n";
    }
} else {
    echo "❌ No transaction types found<br>\n";
}

// Test 4: Check post types
echo "<h3>4. Post Types Test</h3>\n";
$post_types = [
    'properties',
    'agents',
    'agreements',
    'contacts',
    'download-request',
    'document-vault',
];

$missing_post_types = [];
foreach ($post_types as $post_type) {
    if (!post_type_exists($post_type)) {
        $missing_post_types[] = $post_type;
    }
}

if (empty($missing_post_types)) {
    echo "✅ All post types registered<br>\n";
} else {
    echo "❌ Missing post types: " . implode(', ', $missing_post_types) . "<br>\n";
}

// Test 5: Check shortcodes
echo "<h3>5. Shortcodes Test</h3>\n";
$shortcodes = [
    'snapcre_properties',
    'snapcre_property_filters',
    'snapcre_property_grid',
];

$missing_shortcodes = [];
foreach ($shortcodes as $shortcode) {
    if (!shortcode_exists($shortcode)) {
        $missing_shortcodes[] = $shortcode;
    }
}

if (empty($missing_shortcodes)) {
    echo "✅ All shortcodes registered<br>\n";
} else {
    echo "❌ Missing shortcodes: " . implode(', ', $missing_shortcodes) . "<br>\n";
}

// Test 6: Check admin menu
echo "<h3>6. Admin Menu Test</h3>\n";
global $menu, $submenu;
$menu_found = false;

if (is_array($menu)) {
    foreach ($menu as $menu_item) {
        if (isset($menu_item[2]) && $menu_item[2] === 'snap-cre') {
            $menu_found = true;
            break;
        }
    }
}

if ($menu_found) {
    echo "✅ Admin menu registered<br>\n";
} else {
    echo "❌ Admin menu not found<br>\n";
}

echo "<h3>Test Complete</h3>\n";
echo "If all tests pass, the SnapCRE plugin should be working correctly.<br>\n";
echo "You can now go to the WordPress admin and check SnapCRE > Settings to test the interface.<br>\n";
?>
