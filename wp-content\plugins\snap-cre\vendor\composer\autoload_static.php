<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInit42c98d3bab69c8d6c9f46e38bf3ea50e
{
    public static $prefixLengthsPsr4 = array (
        'S' => 
        array (
            'SnapCRE\\' => 8,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'SnapCRE\\' => 
        array (
            0 => __DIR__ . '/../..' . '/src',
        ),
    );

    public static $classMap = array (
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInit42c98d3bab69c8d6c9f46e38bf3ea50e::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInit42c98d3bab69c8d6c9f46e38bf3ea50e::$prefixDirsPsr4;
            $loader->classMap = ComposerStaticInit42c98d3bab69c8d6c9f46e38bf3ea50e::$classMap;

        }, null, ClassLoader::class);
    }
}
